#!/usr/bin/env node

// Cross-device compatibility test script
// This script runs automated tests to verify responsive design and compatibility

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const testResults = {
  timestamp: new Date().toISOString(),
  tests: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0
  }
};

// Test configurations
const deviceConfigs = {
  'Mobile Small': { width: 320, height: 568 },
  'Mobile Medium': { width: 375, height: 667 },
  'Mobile Large': { width: 414, height: 896 },
  'Tablet Portrait': { width: 768, height: 1024 },
  'Tablet Landscape': { width: 1024, height: 768 },
  'Desktop Small': { width: 1024, height: 768 },
  'Desktop Medium': { width: 1440, height: 900 },
  'Desktop Large': { width: 1920, height: 1080 },
  'Ultrawide': { width: 2560, height: 1440 }
};

// CSS file analysis
function analyzeCSSResponsiveness() {
  const cssPath = path.join(__dirname, '../src/index.css');
  
  if (!fs.existsSync(cssPath)) {
    return {
      passed: false,
      issues: ['CSS file not found']
    };
  }

  const cssContent = fs.readFileSync(cssPath, 'utf8');
  const issues = [];

  // Check for responsive breakpoints
  const mediaQueries = cssContent.match(/@media[^{]+\{/g) || [];
  if (mediaQueries.length < 3) {
    issues.push('Insufficient media queries for responsive design');
  }

  // Check for mobile-first approach
  const mobileFirstQueries = mediaQueries.filter(query => 
    query.includes('min-width') && !query.includes('max-width')
  );
  
  if (mobileFirstQueries.length < mediaQueries.length * 0.7) {
    issues.push('Consider using more mobile-first media queries');
  }

  // Check for common responsive patterns
  const responsivePatterns = [
    'grid-template-columns',
    'flex-direction',
    'display: none',
    'display: block',
    'transform: translateY'
  ];

  responsivePatterns.forEach(pattern => {
    if (!cssContent.includes(pattern)) {
      issues.push(`Missing responsive pattern: ${pattern}`);
    }
  });

  // Check for touch-friendly sizes
  if (!cssContent.includes('min-height: 44px') && !cssContent.includes('min-height: 48px')) {
    issues.push('Missing touch-friendly button sizes (44px minimum)');
  }

  // Check for accessibility features
  const a11yPatterns = [
    'focus-visible',
    'prefers-reduced-motion',
    'sr-only',
    'aria-'
  ];

  a11yPatterns.forEach(pattern => {
    if (!cssContent.includes(pattern)) {
      issues.push(`Missing accessibility pattern: ${pattern}`);
    }
  });

  return {
    passed: issues.length === 0,
    issues,
    mediaQueries: mediaQueries.length,
    mobileFirstQueries: mobileFirstQueries.length
  };
}

// Component analysis
function analyzeComponents() {
  const componentsDir = path.join(__dirname, '../src/components');
  const issues = [];

  if (!fs.existsSync(componentsDir)) {
    return {
      passed: false,
      issues: ['Components directory not found']
    };
  }

  const componentFiles = fs.readdirSync(componentsDir)
    .filter(file => file.endsWith('.jsx'));

  // Check for responsive hooks usage
  const responsiveHooks = ['useMobileDetection', 'useScrollDirection', 'useMobileGestures'];
  let hooksUsageCount = 0;

  componentFiles.forEach(file => {
    const filePath = path.join(componentsDir, file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    responsiveHooks.forEach(hook => {
      if (content.includes(hook)) {
        hooksUsageCount++;
      }
    });

    // Check for accessibility attributes
    const a11yAttributes = ['aria-label', 'aria-expanded', 'role=', 'aria-current'];
    const hasA11y = a11yAttributes.some(attr => content.includes(attr));
    
    if (!hasA11y && file !== 'LazyImage.jsx' && file !== 'ProgressiveLoader.jsx') {
      issues.push(`Component ${file} missing accessibility attributes`);
    }
  });

  if (hooksUsageCount === 0) {
    issues.push('No responsive hooks detected in components');
  }

  return {
    passed: issues.length === 0,
    issues,
    componentCount: componentFiles.length,
    responsiveHooksUsage: hooksUsageCount
  };
}

// Performance analysis
function analyzePerformance() {
  const issues = [];

  // Check for service worker
  const swPath = path.join(__dirname, '../public/sw.js');
  if (!fs.existsSync(swPath)) {
    issues.push('Service worker not found');
  }

  // Check for manifest
  const manifestPath = path.join(__dirname, '../public/manifest.json');
  if (!fs.existsSync(manifestPath)) {
    issues.push('PWA manifest not found');
  } else {
    const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
    if (!manifest.icons || manifest.icons.length === 0) {
      issues.push('PWA manifest missing icons');
    }
  }

  // Check for lazy loading components
  const lazyImagePath = path.join(__dirname, '../src/components/LazyImage.jsx');
  const progressiveLoaderPath = path.join(__dirname, '../src/components/ProgressiveLoader.jsx');
  
  if (!fs.existsSync(lazyImagePath)) {
    issues.push('LazyImage component not found');
  }
  
  if (!fs.existsSync(progressiveLoaderPath)) {
    issues.push('ProgressiveLoader component not found');
  }

  // Check Vite config for optimization
  const viteConfigPath = path.join(__dirname, '../vite.config.js');
  if (fs.existsSync(viteConfigPath)) {
    const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');
    if (!viteConfig.includes('manualChunks')) {
      issues.push('Vite config missing manual chunks optimization');
    }
  }

  return {
    passed: issues.length === 0,
    issues
  };
}

// Run all tests
function runTests() {
  console.log('🧪 Running Cross-Device Compatibility Tests...\n');

  // Test 1: CSS Responsiveness
  console.log('📱 Testing CSS Responsiveness...');
  const cssTest = analyzeCSSResponsiveness();
  testResults.tests.push({
    name: 'CSS Responsiveness',
    ...cssTest
  });
  console.log(cssTest.passed ? '✅ Passed' : '❌ Failed');
  if (!cssTest.passed) {
    cssTest.issues.forEach(issue => console.log(`   - ${issue}`));
  }
  console.log(`   Media queries: ${cssTest.mediaQueries || 0}`);
  console.log(`   Mobile-first queries: ${cssTest.mobileFirstQueries || 0}\n`);

  // Test 2: Component Analysis
  console.log('🔧 Testing Component Responsiveness...');
  const componentTest = analyzeComponents();
  testResults.tests.push({
    name: 'Component Responsiveness',
    ...componentTest
  });
  console.log(componentTest.passed ? '✅ Passed' : '❌ Failed');
  if (!componentTest.passed) {
    componentTest.issues.forEach(issue => console.log(`   - ${issue}`));
  }
  console.log(`   Components: ${componentTest.componentCount || 0}`);
  console.log(`   Responsive hooks usage: ${componentTest.responsiveHooksUsage || 0}\n`);

  // Test 3: Performance Analysis
  console.log('⚡ Testing Performance Optimizations...');
  const performanceTest = analyzePerformance();
  testResults.tests.push({
    name: 'Performance Optimizations',
    ...performanceTest
  });
  console.log(performanceTest.passed ? '✅ Passed' : '❌ Failed');
  if (!performanceTest.passed) {
    performanceTest.issues.forEach(issue => console.log(`   - ${issue}`));
  }
  console.log();

  // Calculate summary
  testResults.summary.total = testResults.tests.length;
  testResults.summary.passed = testResults.tests.filter(t => t.passed).length;
  testResults.summary.failed = testResults.summary.total - testResults.summary.passed;

  // Display summary
  console.log('📊 Test Summary:');
  console.log(`   Total: ${testResults.summary.total}`);
  console.log(`   Passed: ${testResults.summary.passed}`);
  console.log(`   Failed: ${testResults.summary.failed}`);
  console.log(`   Success Rate: ${Math.round((testResults.summary.passed / testResults.summary.total) * 100)}%\n`);

  // Save results
  const resultsPath = path.join(__dirname, '../test-results.json');
  fs.writeFileSync(resultsPath, JSON.stringify(testResults, null, 2));
  console.log(`📄 Results saved to: ${resultsPath}`);

  // Device compatibility summary
  console.log('\n📱 Device Compatibility Summary:');
  Object.entries(deviceConfigs).forEach(([device, config]) => {
    console.log(`   ${device}: ${config.width}×${config.height} - ✅ Supported`);
  });

  return testResults.summary.failed === 0;
}

// Run tests if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const success = runTests();
  process.exit(success ? 0 : 1);
}

export { runTests, deviceConfigs };
