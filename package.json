{"name": "nicholas-gerasimatos-resume", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@react-spring/web": "^10.0.1", "@stagewise-plugins/react": "^0.4.9", "@stagewise/toolbar-react": "^0.4.9", "@use-gesture/react": "^10.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-slick": "^0.30.3", "slick-carousel": "^1.8.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^23.0.1", "vite": "^4.5.0", "vitest": "^0.34.6"}}