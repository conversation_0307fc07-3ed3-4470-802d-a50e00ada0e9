import resumeJson from '../data/resume.json';

class ResumeDataService {
  constructor() {
    this.data = resumeJson;
  }

  getBasics() {
    return this.data.basics;
  }

  getWork() {
    return this.data.work;
  }

  getSkills() {
    return this.data.skills;
  }

  getEducation() {
    return this.data.education || [];
  }

  getCertificates() {
    return this.data.certificates;
  }

  getPublications() {
    return this.data.publications;
  }

  getAwards() {
    return this.data.awards;
  }

  getReferences() {
    return this.data.references;
  }

  // Helper methods for formatted data
  getFormattedWorkExperience() {
    return this.data.work.map(job => ({
      title: job.position,
      company: job.name,
      duration: this.formatDateRange(job.startDate, job.endDate),
      location: job.location,
      responsibilities: job.summary.split('\n\n').filter(item => item.trim()),
      url: job.url
    }));
  }

  getFormattedSkills() {
    const skillCategories = {
      "Cloud & Virtualization": [],
      "DevOps & Containerization": [],
      "Operating Systems & Infrastructure": [],
      "AI & Data": [],
      "Software Development & Methodologies": [],
      "Business & Strategy": [],
      "Architecture & Core Engineering": [],
      "Tools & Other": []
    };

    this.data.skills.forEach(skill => {
      const skillName = skill.name;
      let categorized = false;
      if (skillName.includes('Cloud') || skillName.includes('AWS') || skillName.includes('Amazon Web Services') || skillName.includes('Azure') ||
          skillName.includes('Hybrid Cloud') || skillName.includes('VMware') ||
          skillName.includes('Private Cloud') || skillName.includes('IaaS') ||
          skillName.includes('PaaS') || skillName.includes('OpenStack') || skillName.includes('Cloud Security') ||
          skillName.includes('Virtualization') || skillName.includes('Cloud Computing')) {
        skillCategories["Cloud & Virtualization"].push(skillName);
        categorized = true;
      } else if (skillName.includes('CI/CD') || skillName.includes('Continuous Integration') || skillName.includes('GitOps') || skillName.includes('Docker') ||
                 skillName.includes('Containers') || skillName.includes('OpenShift') || skillName.includes('EKS') || skillName.includes('Amazon EKS') ||
                 skillName.includes('Kubernetes') || skillName.includes('DevOps') || skillName.includes('Release Engineering') ||
                 skillName.includes('Vagrant')) {
        skillCategories["DevOps & Containerization"].push(skillName);
        categorized = true;
      } else if (skillName.includes('Red Hat Linux') || skillName.includes('Red Hat Enterprise Linux (RHEL)') ||
                 skillName.includes('Windows Server') || skillName.includes('Load Balancing') ||
                 skillName.includes('Software Defined Storage') || skillName.includes('Software Defined Networking') ||
                 skillName.includes('Linux') || skillName.includes('Converged Infrastructure')) {
        skillCategories["Operating Systems & Infrastructure"].push(skillName);
        categorized = true;
      } else if (skillName.includes('AI') || skillName.includes('Machine Learning') || skillName.includes('LLM') ||
                 skillName.includes('Amazon Bedrock') || skillName.includes('Artificial Intelligence') || skillName.includes('Large Language Models')) {
        skillCategories["AI & Data"].push(skillName);
        categorized = true;
      } else if (skillName.includes('Software Development') || skillName.includes('Software Quality Assurance') ||
                 skillName.includes('Open-Source Software')) {
        skillCategories["Software Development & Methodologies"].push(skillName);
        categorized = true;
      } else if (skillName.includes('Business Ownership') || skillName.includes('Professional Services') ||
                 skillName.includes('Management') || skillName.includes('Research') ||
                 skillName.includes('Defining Requirements') || skillName.includes('Requirements Gathering')) {
        skillCategories["Business & Strategy"].push(skillName);
        categorized = true;
      } else if (skillName.includes('Open Systems Architecture') || skillName.includes('Distributed Systems') ||
                 skillName.includes('Systems Engineering') || skillName.includes('Capacity Planning') ||
                 skillName.includes('Engineering')) {
        skillCategories["Architecture & Core Engineering"].push(skillName);
        categorized = true;
      }
      
      // Add uncategorized skills to "Tools & Other" category
      if (!categorized) {
        skillCategories["Tools & Other"].push(skillName);
      }
    });

    // Remove empty categories
    Object.keys(skillCategories).forEach(category => {
      if (skillCategories[category].length === 0) {
        delete skillCategories[category];
      }
    });

    return skillCategories;
  }

  getFormattedCertificates() {
    return this.data.certificates.map(cert => ({
      name: cert.name,
      issuer: cert.issuer,
      date: cert.date
    }));
  }

  getFormattedPublications() {
    return this.data.publications.map(pub => ({
      name: pub.name,
      publisher: pub.publisher,
      releaseDate: pub.releaseDate,
      url: pub.url,
      summary: pub.summary
    }));
  }

  formatDateRange(startDate, endDate) {
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : null;
    
    const formatDate = (date) => {
      return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'short' 
      });
    };

    if (end) {
      return `${formatDate(start)} - ${formatDate(end)}`;
    } else {
      return `${formatDate(start)} - Present`;
    }
  }
}

export default new ResumeDataService();