.about {
  background-color: var(--color-background);
  color: var(--color-text);
  padding: var(--space-32) 0;
}

.about-content {
  max-width: var(--container-lg);
  margin: 0 auto;
}

.about-intro {
  margin-bottom: var(--space-32);
}

.about-text {
  color: var(--color-text);
  font-size: var(--font-size-lg);
  line-height: var(--line-height-normal);
  margin-bottom: var(--space-24);
}

.about-highlight {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-16);
  color: var(--color-text);
}

.value-props-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-24);
  margin: var(--space-32) 0;
}

.value-prop-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.value-prop-card:hover {
  transform: translateY(-4px);
  background-color: var(--color-primary-hover);
}

.value-prop-title {
  color: var(--color-text);
  margin: var(--space-12) 0;
}

.value-prop-description {
  color: var(--color-text);
  font-size: var(--font-size-base);
}

.testimonials-section {
  margin-top: var(--space-48);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
  margin-top: var(--space-24);
}

.testimonial-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.testimonial-quote {
  color: var(--color-text);
  font-size: var(--font-size-lg);
  font-style: italic;
  margin-bottom: var(--space-16);
}

.testimonial-author {
  display: flex;
  flex-direction: column;
}

.author-name {
  color: var(--color-text);
  font-weight: var(--font-weight-semibold);
}

.author-role {
  color: var(--color-text);
  font-size: var(--font-size-sm);
}