import React from 'react';
import FadeInUp from './FadeInUp.jsx';
import resumeData from '../services/resumeData.jsx';
import './Hero.css';

const Hero = () => {
  const basics = resumeData.getBasics();
  const linkedInProfile = basics.profiles.find(profile => profile.network === 'LinkedIn');
  
  // Extract keywords for LinkedIn-style headline
  const keywords = ['Cloud Architecture', 'AI/ML Integration', 'Platform Engineering', 'Digital Transformation'];
  
  return (
    <section id="hero" className="hero" aria-labelledby="hero-title">
      {/* YouTube Video Background */}
      <div className="video-background">
        <iframe
          src="https://www.youtube.com/embed/tXxzOXwaf1c?autoplay=1&mute=1&loop=1&playlist=tXxzOXwaf1c&start=87&controls=0&showinfo=0&rel=0"
          title="Background Video"
          frameBorder="0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          className="video-iframe"
        />
        <div className="video-overlay"></div>
      </div>
      
      {/* LinkedIn-style banner background */}
      <div className="hero-banner" aria-hidden="true">
        <div className="banner-pattern"></div>
      </div>
      
      <div className="hero-container">
        <div className="hero-content">
          <div className="hero-text">
            <FadeInUp delay={0.1}>
              <h1 id="hero-title" className="hero-title">{basics.name}</h1>
            </FadeInUp>
            <FadeInUp delay={0.2}>
              <h2 className="hero-headline">
                {basics.label.split('|')[0].trim()} • {keywords.slice(0, 3).join(' • ')}
              </h2>
            </FadeInUp>
            <FadeInUp delay={0.3}>
              <p className="hero-value-prop">
                Driving business value through innovative cloud solutions and AI integration
              </p>
            </FadeInUp>
            <FadeInUp delay={0.4}>
              <div className="hero-buttons">
                <a href="#contact" className="btn btn--primary">
                  <span>Start a Conversation</span>
                  <span className="btn-icon">→</span>
                </a>
                {linkedInProfile && (
                  <a href={linkedInProfile.url} target="_blank" rel="noopener noreferrer" className="btn btn--linkedin">
                    <span>LinkedIn Profile</span>
                    <span className="btn-icon">↗</span>
                  </a>
                )}
              </div>
            </FadeInUp>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;