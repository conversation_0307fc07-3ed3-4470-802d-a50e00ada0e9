.experience {
  background-color: var(--color-background);
  padding: var(--space-32) 0;
}

.timeline {
  max-width: var(--container-lg);
  margin: 0 auto;
  padding: var(--space-24);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-32);
  padding-left: var(--space-24);
}

.timeline-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--color-border);
}

.timeline-item::after {
  content: '';
  position: absolute;
  left: -4px;
  top: var(--space-8);
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--color-primary);
  border: 2px solid var(--color-border);
}

.timeline-content {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.timeline-header {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: var(--space-16);
  align-items: center;
  cursor: pointer;
  padding-right: var(--space-16);
}

.job-title {
  color: var(--color-text);
  font-size: var(--font-size-xl);
  grid-column: 1 / -1;
  margin: 0;
}

.company {
  color: var(--color-text);
  font-weight: var(--font-weight-medium);
}

.duration, .location {
  color: var(--color-text);
  font-size: var(--font-size-sm);
}

.expand-btn {
  background-color: var(--color-primary);
  color: var(--color-text);
  border: none;
  border-radius: var(--radius-base);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color var(--duration-normal) var(--ease-standard);
}

.expand-btn:hover {
  background-color: var(--color-primary-hover);
}

.expand-btn.active {
  background-color: var(--color-primary-hover);
}

.timeline-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--duration-normal) var(--ease-standard);
}

.timeline-details.expanded {
  max-height: 1000px;
}

.responsibilities {
  list-style-type: none;
  padding: var(--space-16) 0 0;
  margin: 0;
}

.responsibilities li {
  color: var(--color-text);
  padding: var(--space-8) 0;
  position: relative;
  padding-left: var(--space-24);
}

.responsibilities li::before {
  content: '•';
  color: var(--color-primary);
  position: absolute;
  left: var(--space-8);
}

@media (max-width: 768px) {
  .timeline-header {
    grid-template-columns: 1fr auto;
  }

  .company, .duration, .location {
    font-size: var(--font-size-sm);
  }
}