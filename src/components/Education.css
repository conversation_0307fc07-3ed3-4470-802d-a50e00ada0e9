.education {
  background-color: var(--color-background);
  padding: var(--space-32) 0;
}

.education-grid {
  max-width: var(--container-lg);
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
  padding: var(--space-24);
}

.education-card {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  height: 100%;
}

.card-title {
  color: var(--color-text);
  font-size: var(--font-size-xl);
  margin-bottom: var(--space-24);
  padding-bottom: var(--space-12);
  border-bottom: 1px solid var(--color-border);
}

.certification-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.certification-list li {
  color: var(--color-text);
  margin-bottom: var(--space-16);
  padding-left: var(--space-24);
  position: relative;
}

.certification-list li::before {
  content: '🏆';
  position: absolute;
  left: 0;
  top: 2px;
}

.cert-date {
  color: var(--color-text);
  font-size: var(--font-size-sm);
  margin-left: var(--space-8);
}

.award-item {
  margin-bottom: var(--space-24);
  padding-bottom: var(--space-16);
  border-bottom: 1px solid var(--color-border);
}

.award-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.award-item h4 {
  color: var(--color-text);
  margin-bottom: var(--space-8);
}

.award-item p {
  color: var(--color-text);
  font-size: var(--font-size-base);
  margin-bottom: var(--space-8);
}

.award-date {
  display: block;
  color: var(--color-text);
  font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
  .education-grid {
    grid-template-columns: 1fr;
  }
}