.skills-section {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, var(--oxford-blue) 0%, var(--prussian-blue) 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.skills-container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.skills-header {
  text-align: center;
  margin-bottom: 1rem;
}

.skills-header h2 {
  font-size: 2.5rem;
  color: #fff;
  margin-bottom: 1rem;
  font-weight: 600;
}

.skills-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto;
}

.category-tabs {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  padding: 0 1rem;
}

.category-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  border: none;
  border-radius: 12px;
  background: var(--prussian-blue);
  color: var(--columbia-blue);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1rem;
  font-weight: 500;
}

.category-tab svg {
  font-size: 1.2rem;
}

.category-tab:hover {
  background: var(--bice-blue);
  transform: translateY(-2px);
}

.category-tab.active {
  background: var(--sapphire);
  box-shadow: 0 8px 20px var(--sapphire);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
}

.skill-card {
  background: var(--prussian-blue);
  border-radius: 16px;
  padding: 1.5rem;
  color: var(--columbia-blue);
  backdrop-filter: blur(10px);
  border: 1px solid var(--sapphire);
  display: flex;
  flex-direction: column;
  gap: 1rem;
  cursor: pointer;
  will-change: transform;
  transition: border-color 0.3s ease;
}

.skill-card:hover {
  border-color: var(--bice-blue);
}

.skill-icon {
  font-size: 2rem;
  color: var(--sapphire);
  margin-bottom: 0.75rem;
}

.skill-card h3 {
  font-size: 1.25rem;
  margin: 0;
  color: var(--columbia-blue);
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.nav-button {
  background: var(--prussian-blue);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--columbia-blue);
  transition: all 0.3s ease;
}

.nav-button:hover:not(:disabled) {
  background: var(--bice-blue);
  transform: scale(1.1);
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-indicator {
  color: var(--columbia-blue);
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .skills-section {
    padding: 2rem 1rem;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .category-tab {
    padding: 0.8rem 1rem;
    font-size: 0.9rem;
  }
}


.skill-tag:hover .skill-hover-card {
  opacity: 1;
  transform: translateX(-50%) translateY(-0.5rem);
}

.category-label {
  display: block;
  margin-top: 0.5rem;
  color: #00ff88;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .skills-controls {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .tag-cloud-container {
    min-height: 50vh;
  }
}