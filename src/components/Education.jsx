
import React from 'react';
import FadeInUp from './FadeInUp.jsx';
import resumeData from '../services/resumeData.jsx';
import './Education.css';

const Education = () => {
  const education = resumeData.getEducation();
  const certifications = resumeData.getFormattedCertificates();
  const awards = resumeData.getAwards();

  return (
    <section id="education" className="education">
      <div className="container">
        <h2 className="section-title">Certifications & Awards</h2>
        <div className="education-grid">

          <FadeInUp delay={0.2}>
            <div className="education-card">
              <h3 className="card-title">Certifications</h3>
              <ul className="certification-list">
                {certifications.map((cert, index) => (
                  <li key={index}>
                    <strong>{cert.name}</strong>
                    {cert.issuer && <span> - {cert.issuer}</span>}
                    {cert.date && <span className="cert-date"> ({cert.date})</span>}
                  </li>
                ))}
              </ul>
            </div>
          </FadeInUp>
          <FadeInUp delay={0.4}>
            <div className="education-card">
              <h3 className="card-title">Awards</h3>
              {awards.map((award, index) => (
                <div key={index} className="award-item">
                  <h4>{award.title}</h4>
                  <p>{award.summary}</p>
                  {award.date && <span className="award-date">{award.date}</span>}
                </div>
              ))}
            </div>
          </FadeInUp>
        </div>
      </div>
    </section>
  );
};

export default Education;
