.publications {
  background-color: var(--color-background);
  padding: var(--space-32) 0;
}

.publications-grid {
  max-width: var(--container-lg);
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-24);
  padding: var(--space-24);
}

.publication-item {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.publication-item:hover {
  transform: translateY(-4px);
  border-color: var(--color-primary);
}

.publication-item h3 {
  color: var(--color-text);
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-12);
}

.publisher {
  color: var(--color-text);
  font-size: var(--font-size-base);
  margin-bottom: var(--space-16);
}

.publication-link {
  display: inline-flex;
  align-items: center;
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: color var(--duration-normal) var(--ease-standard);
}

.publication-link:hover {
  color: var(--color-primary-hover);
}

.publication-link::after {
  content: '→';
  margin-left: var(--space-8);
  transition: transform var(--duration-normal) var(--ease-standard);
}

.publication-link:hover::after {
  transform: translateX(4px);
}

@media (max-width: 768px) {
  .publications-grid {
    grid-template-columns: 1fr;
  }
}