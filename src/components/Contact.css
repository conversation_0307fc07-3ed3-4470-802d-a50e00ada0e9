.contact {
  background-color: var(--color-background);
  padding: var(--space-32) 0;
}

.contact-content {
  max-width: var(--container-lg);
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--space-32);
  padding: var(--space-24);
}

.contact-info-section {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.contact-details {
  margin-top: var(--space-16);
}

.contact-detail {
  color: var(--color-text);
  margin-bottom: var(--space-12);
}

.contact-detail a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-normal) var(--ease-standard);
}

.contact-detail a:hover {
  color: var(--color-primary-hover);
}

.contact-form-section {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
  padding: var(--space-24);
}

.form-group {
  margin-bottom: var(--space-16);
}

.form-label {
  display: block;
  color: var(--color-text);
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
}

.form-control {
  width: 100%;
  padding: var(--space-12);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  background-color: var(--color-background);
  color: var(--color-text);
  transition: border-color var(--duration-normal) var(--ease-standard);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: var(--focus-ring);
}

.btn--full-width {
  width: 100%;
}

.btn--loading {
  position: relative;
  cursor: not-allowed;
  opacity: 0.8;
}

.btn-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: var(--space-8);
  border: 2px solid var(--color-text);
  border-top-color: transparent;
  border-radius: 50%;
  animation: spinner 0.6s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
  }
}