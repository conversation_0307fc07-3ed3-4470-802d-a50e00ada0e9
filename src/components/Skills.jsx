
import React, { useState, useRef, useEffect } from 'react';
import { useSpring, animated, to } from '@react-spring/web';
import { useGesture } from '@use-gesture/react';
import resumeData from '../services/resumeData.jsx';
import './Skills.css';
import { FaCloud, FaCogs, FaServer, FaBrain, FaCode, FaChartLine, FaSitemap, FaTools } from 'react-icons/fa';
import { BiChevronLeft, BiChevronRight } from 'react-icons/bi';

const getSkillIcon = (category) => {
  const icons = {
    'Cloud & DevOps': FaCloud,
    'Backend': FaServer,
    'Frontend': FaCode,
    'AI & ML': FaBrain,
    'Architecture': FaSitemap,
    'Tools': FaTools,
    'Analytics': FaChartLine,
    'System Design': FaCogs
  };
  const Icon = icons[category] || FaCode;
  return <Icon />;
};

const SkillCard = ({ skill, category }) => {
  const [props, api] = useSpring(() => ({
    xys: [0, 0, 1],
    config: { mass: 5, tension: 350, friction: 40 }
  }));

  const calc = (x, y) => [
    -(y - window.innerHeight / 2) / 20,
    (x - window.innerWidth / 2) / 20,
    1.1
  ];

  const trans = (x, y, s) =>
    `perspective(600px) rotateX(${x}deg) rotateY(${y}deg) scale(${s})`;

  return (
    <animated.div
      className="skill-card"
      onMouseMove={({ clientX: x, clientY: y }) => api.start({ xys: calc(x, y) })}
      onMouseLeave={() => api.start({ xys: [0, 0, 1] })}
      style={{
        transform: props.xys.to(trans)
      }}
    >
      <div className="skill-icon">
        {getSkillIcon(category)}
      </div>
      <h3>{skill}</h3>
    </animated.div>
  );
};

const Skills = () => {
  const skills = resumeData.getFormattedSkills();
  const [activeCategory, setActiveCategory] = useState(Object.keys(skills)[0]);
  const [currentPage, setCurrentPage] = useState(0);
  const containerRef = useRef(null);

  const itemsPerPage = 6;
  const currentSkills = skills[activeCategory] || [];
  const totalPages = Math.ceil(currentSkills.length / itemsPerPage);

  const nextPage = () => {
    setCurrentPage((prev) => (prev + 1) % totalPages);
  };

  const prevPage = () => {
    setCurrentPage((prev) => (prev - 1 + totalPages) % totalPages);
  };

  const displayedSkills = currentSkills
    .slice(currentPage * itemsPerPage, (currentPage + 1) * itemsPerPage)
    .map(skill => ({
      skill: skill
    }));

  return (
    <section id="skills" className="skills-section">
      <div className="skills-container" ref={containerRef}>
        <div className="skills-header">
          <h2>Technical Skills</h2>
          <p>Explore my technical expertise across different domains</p>
        </div>
        <div className="category-tabs">
          {Object.keys(skills).map(category => (
            <button
              key={category}
              className={`category-tab ${activeCategory === category ? 'active' : ''}`}
              onClick={() => {
                setActiveCategory(category);
                setCurrentPage(0);
              }}
            >
              {getSkillIcon(category)}
              <span>{category}</span>
            </button>
          ))}
        </div>

        <div className="skills-grid">
          {displayedSkills.map(({ skill, proficiency }, index) => (
            <SkillCard
              key={`${skill}-${index}`}
              skill={skill}
              category={activeCategory}
            />
          ))}
        </div>

        {totalPages > 1 && (
          <div className="pagination-controls">
            <button
              className="nav-button prev"
              onClick={prevPage}
              disabled={currentPage === 0}
            >
              <BiChevronLeft />
            </button>
            <span className="page-indicator">
              {currentPage + 1} / {totalPages}
            </span>
            <button
              className="nav-button next"
              onClick={nextPage}
              disabled={currentPage === totalPages - 1}
            >
              <BiChevronRight />
            </button>
          </div>
        )}
      </div>
    </section>
  );
};

export default Skills;
