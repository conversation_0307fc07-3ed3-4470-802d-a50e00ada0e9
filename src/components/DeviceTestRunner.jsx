import React, { useState, useEffect } from 'react';
import { generateTestReport, deviceBreakpoints } from '../utils/deviceTesting.js';

const DeviceTestRunner = () => {
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedViewport, setSelectedViewport] = useState('auto');

  const runTests = async () => {
    setIsRunning(true);
    
    // Small delay to show loading state
    await new Promise(resolve => setTimeout(resolve, 500));
    
    try {
      const results = generateTestReport();
      setTestResults(results);
    } catch (error) {
      console.error('Test execution failed:', error);
      setTestResults({
        summary: { total: 0, passed: 0, failed: 1 },
        results: [],
        error: error.message
      });
    } finally {
      setIsRunning(false);
    }
  };

  const simulateViewport = (width, height) => {
    // This is a visual simulation only - actual viewport testing
    // would require browser dev tools or testing frameworks
    const app = document.querySelector('.App');
    if (app) {
      app.style.maxWidth = `${width}px`;
      app.style.margin = '0 auto';
      app.style.border = '2px solid #3b82f6';
      app.style.boxShadow = '0 0 20px rgba(59, 130, 246, 0.3)';
    }
  };

  const resetViewport = () => {
    const app = document.querySelector('.App');
    if (app) {
      app.style.maxWidth = '';
      app.style.margin = '';
      app.style.border = '';
      app.style.boxShadow = '';
    }
  };

  const handleViewportChange = (value) => {
    setSelectedViewport(value);
    
    if (value === 'auto') {
      resetViewport();
    } else {
      const [category, size] = value.split('-');
      const config = deviceBreakpoints[category]?.[size];
      if (config) {
        simulateViewport(config.width, config.height);
      }
    }
  };

  useEffect(() => {
    // Cleanup on unmount
    return () => {
      resetViewport();
    };
  }, []);

  if (process.env.NODE_ENV !== 'development') {
    return null; // Only show in development
  }

  return (
    <div className="device-test-runner">
      <div className="test-controls">
        <h3>🧪 Device Testing</h3>
        
        <div className="viewport-selector">
          <label htmlFor="viewport-select">Simulate Viewport:</label>
          <select 
            id="viewport-select"
            value={selectedViewport}
            onChange={(e) => handleViewportChange(e.target.value)}
          >
            <option value="auto">Auto (Current)</option>
            <optgroup label="Mobile">
              <option value="mobile-small">iPhone SE (320×568)</option>
              <option value="mobile-medium">iPhone 8 (375×667)</option>
              <option value="mobile-large">iPhone 11 Pro Max (414×896)</option>
            </optgroup>
            <optgroup label="Tablet">
              <option value="tablet-portrait">iPad Portrait (768×1024)</option>
              <option value="tablet-landscape">iPad Landscape (1024×768)</option>
            </optgroup>
            <optgroup label="Desktop">
              <option value="desktop-small">Small Desktop (1024×768)</option>
              <option value="desktop-medium">Medium Desktop (1440×900)</option>
              <option value="desktop-large">Large Desktop (1920×1080)</option>
            </optgroup>
          </select>
        </div>

        <button 
          onClick={runTests}
          disabled={isRunning}
          className="btn btn--primary"
        >
          {isRunning ? 'Running Tests...' : 'Run Compatibility Tests'}
        </button>
      </div>

      {testResults && (
        <div className="test-results">
          <div className="test-summary">
            <h4>Test Summary</h4>
            <div className="summary-stats">
              <span className="stat passed">
                ✅ {testResults.summary.passed} Passed
              </span>
              <span className="stat failed">
                ❌ {testResults.summary.failed} Failed
              </span>
              <span className="stat total">
                📊 {testResults.summary.total} Total
              </span>
            </div>
          </div>

          {testResults.error && (
            <div className="test-error">
              <h4>Error</h4>
              <p>{testResults.error}</p>
            </div>
          )}

          {testResults.results && testResults.results.length > 0 && (
            <div className="test-details">
              <h4>Detailed Results</h4>
              {testResults.results.map((result, index) => (
                <div 
                  key={index} 
                  className={`test-result ${result.passed ? 'passed' : 'failed'}`}
                >
                  <div className="result-header">
                    <span className="result-icon">
                      {result.passed ? '✅' : '❌'}
                    </span>
                    <span className="result-device">
                      {result.device} ({result.viewport})
                    </span>
                  </div>
                  
                  {!result.passed && (
                    <div className="result-issues">
                      {result.error && (
                        <div className="error-details">
                          <strong>Error:</strong> {result.error}
                        </div>
                      )}
                      
                      {['responsive', 'navigation', 'layout', 'performance'].map(testType => {
                        const test = result[testType];
                        if (test && !test.passed && test.issues.length > 0) {
                          return (
                            <div key={testType} className="issue-group">
                              <strong>{testType} issues:</strong>
                              <ul>
                                {test.issues.map((issue, i) => (
                                  <li key={i}>{issue}</li>
                                ))}
                              </ul>
                            </div>
                          );
                        }
                        return null;
                      })}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DeviceTestRunner;
