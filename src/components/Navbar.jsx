
import React, { useState, useEffect } from 'react';
import { useActiveNavLink } from '../hooks.jsx';
import { useMobileDetection, useScrollDirection } from '../hooks/useMobileGestures.jsx';
import resumeData from '../services/resumeData.jsx';

const Navbar = () => {
  const activeLink = useActiveNavLink();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isMobile } = useMobileDetection();
  const { scrollDirection, scrollY } = useScrollDirection();

  // Hide navbar on scroll down for mobile
  const shouldHideNavbar = isMobile && scrollDirection === 'down' && scrollY > 100;

  const convertJsonToMarkdown = (data) => {
    let md = `# ${data.basics.name}\n\n`;
    md += `${data.basics.label} | ${data.basics.email} | ${data.basics.phone} | ${data.basics.url}\n\n`;
    md += `## Summary\n\n${data.basics.summary}\n\n`;

    if (data.work && data.work.length > 0) {
      md += `## Experience\n\n`;
      data.work.forEach(job => {
        md += `### ${job.position} at ${job.name}\n`;
        md += `${job.startDate} - ${job.endDate}\n\n`;
        md += `${job.summary}\n\n`;
      });
    }

    if (data.education && data.education.length > 0) {
      md += `## Education\n\n`;
      data.education.forEach(edu => {
        md += `### ${edu.studyType} from ${edu.institution}\n`;
        md += `${edu.startDate} - ${edu.endDate}\n\n`;
      });
    }

    if (data.skills && data.skills.length > 0) {
      md += `## Skills\n\n`;
      const formattedSkills = resumeData.getFormattedSkills();
      Object.keys(formattedSkills).forEach(category => {
        md += `### ${category}\n`;
        md += formattedSkills[category].map(skill => `- ${skill}`).join('\n') + '\n\n';
      });
    }

    if (data.certificates && data.certificates.length > 0) {
      md += `## Certifications\n\n`;
      data.certificates.forEach(cert => {
        md += `- ${cert.name} (${cert.issuer})\n`;
      });
      md += '\n';
    }

    if (data.publications && data.publications.length > 0) {
      md += `## Publications\n\n`;
      data.publications.forEach(pub => {
        md += `### [${pub.name}](${pub.url})\n`;
        md += `${pub.publisher}, ${pub.releaseDate}\n\n`;
        md += `${pub.summary}\n\n`;
      });
    }

    return md;
  };

  const handleDownload = (format) => {
    const resumeJsonData = resumeData.data;
    let filename = `Nicholas_Gerasimatos_Resume.${format}`;
    let content;
    let type;

    if (format === 'json') {
      content = JSON.stringify(resumeJsonData, null, 2);
      type = 'application/json';
    } else if (format === 'md') {
      content = convertJsonToMarkdown(resumeJsonData);
      type = 'text/markdown';
    } else {
      return;
    }

    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const toggleMobileMenu = () => {
    const newState = !isMobileMenuOpen;
    setIsMobileMenuOpen(newState);

    // Lock/unlock body scroll on mobile
    if (isMobile) {
      if (newState) {
        document.body.classList.add('mobile-menu-open');
      } else {
        document.body.classList.remove('mobile-menu-open');
      }
    }
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
    document.body.classList.remove('mobile-menu-open');
  };

  // Close mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      const navbar = document.getElementById('navbar');
      const hamburger = document.getElementById('hamburger');

      if (isMobileMenuOpen && navbar && !navbar.contains(event.target)) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscapeKey = (event) => {
      if (event.key === 'Escape' && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscapeKey);
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isMobileMenuOpen]);

  return (
    <nav
      className={`navbar ${shouldHideNavbar ? 'navbar--hidden' : ''} ${isMobileMenuOpen ? 'navbar--menu-open' : ''}`}
      id="navbar"
      role="navigation"
      aria-label="Main navigation"
    >
      <div className="nav-container">
        <div className="nav-logo">
          <span className="nav-name" role="banner">Nicholas Gerasimatos</span>
        </div>
        <ul
          className={`nav-menu ${isMobileMenuOpen ? 'active' : ''}`}
          id="nav-menu"
          role="menubar"
          aria-label="Main menu"
          aria-hidden={!isMobileMenuOpen && isMobile}
        >
          <li role="none"><a href="#hero" className={`nav-link ${activeLink === 'hero' ? 'active' : ''}`} onClick={closeMobileMenu} role="menuitem" aria-current={activeLink === 'hero' ? 'page' : undefined}>Home</a></li>
          <li role="none"><a href="#about" className={`nav-link ${activeLink === 'about' ? 'active' : ''}`} onClick={closeMobileMenu} role="menuitem" aria-current={activeLink === 'about' ? 'page' : undefined}>About</a></li>
          <li role="none"><a href="#experience" className={`nav-link ${activeLink === 'experience' ? 'active' : ''}`} onClick={closeMobileMenu} role="menuitem" aria-current={activeLink === 'experience' ? 'page' : undefined}>Experience</a></li>
          <li role="none"><a href="#skills" className={`nav-link ${activeLink === 'skills' ? 'active' : ''}`} onClick={closeMobileMenu} role="menuitem" aria-current={activeLink === 'skills' ? 'page' : undefined}>Skills</a></li>
          <li role="none"><a href="#education" className={`nav-link ${activeLink === 'education' ? 'active' : ''}`} onClick={closeMobileMenu} role="menuitem" aria-current={activeLink === 'education' ? 'page' : undefined}>Education</a></li>
          <li role="none"><a href="#publications" className={`nav-link ${activeLink === 'publications' ? 'active' : ''}`} onClick={closeMobileMenu} role="menuitem" aria-current={activeLink === 'publications' ? 'page' : undefined}>Publications</a></li>
          <li role="none"><a href="#contact" className={`nav-link ${activeLink === 'contact' ? 'active' : ''}`} onClick={closeMobileMenu} role="menuitem" aria-current={activeLink === 'contact' ? 'page' : undefined}>Contact</a></li>
          <li className="download-buttons" role="none">
            <button onClick={() => handleDownload('json')} className="btn btn--primary btn--sm" aria-label="Download resume as JSON file">Download JSON</button>
            <button onClick={() => handleDownload('md')} className="btn btn--secondary btn--sm" aria-label="Download resume as Markdown file">Download Markdown</button>
          </li>
        </ul>
        <button
          className={`hamburger ${isMobileMenuOpen ? 'active' : ''}`}
          id="hamburger"
          onClick={toggleMobileMenu}
          aria-label={isMobileMenuOpen ? 'Close mobile menu' : 'Open mobile menu'}
          aria-expanded={isMobileMenuOpen}
          aria-controls="nav-menu"
        >
          <span aria-hidden="true"></span>
          <span aria-hidden="true"></span>
          <span aria-hidden="true"></span>
        </button>
      </div>
    </nav>
  );
};

export default Navbar;
