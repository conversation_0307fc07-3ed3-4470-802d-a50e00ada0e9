.hero {
  position: relative;
  background-color: var(--color-background);
  padding: var(--space-32) 0;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.video-iframe {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 75vw; /* 25% smaller than viewport width */
  height: 42.19vw; /* 16:9 aspect ratio for 75vw width */
  min-height: 75vh; /* 25% smaller than viewport height */
  min-width: 133.33vh; /* 16:9 aspect ratio for 75vh height */
  transform: translate(-50%, -50%);
  object-fit: cover;
  border-radius: 12px; /* Optional: adds rounded corners */
}

.video-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 75vw;
  height: 42.19vw;
  min-height: 75vh;
  min-width: 133.33vh;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1;
  border-radius: 12px; /* Match iframe border radius */
}

.hero-banner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 200px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  opacity: 0.1;
  z-index: 1;
}

.hero-container {
  max-width: var(--container-lg);
  margin: 0 auto;
  padding: 0 var(--space-24);
  position: relative;
  z-index: 2; /* Ensure content stays above video */
  width: 100%;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: var(--font-size-4xl);
  color: var(--color-text);
  margin-bottom: var(--space-16);
}

.hero-headline {
  font-size: var(--font-size-xl);
  color: var(--color-text);
  margin-bottom: var(--space-24);
  font-weight: var(--font-weight-medium);
}

.hero-value-prop {
  font-size: var(--font-size-lg);
  color: var(--color-text);
  margin-bottom: var(--space-32);
}

.hero-buttons {
  display: flex;
  gap: var(--space-16);
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-12) var(--space-24);
  border-radius: var(--radius-base);
  font-weight: var(--font-weight-medium);
  transition: all var(--duration-normal) var(--ease-standard);
}

.btn--primary {
  background-color: var(--color-primary);
  color: var(--color-text);
  border: none;
}

.btn--primary:hover {
  background-color: var(--color-primary-hover);
}

.btn--outline {
  background-color: transparent;
  color: var(--color-text);
  border: 1px solid var(--color-border);
}

.btn--outline:hover {
  background-color: var(--color-surface);
  border-color: var(--color-primary);
}

.btn--linkedin {
  background-color: var(--color-linkedin);
  color: var(--color-text);
  border: none;
}

.btn--linkedin:hover {
  background-color: var(--color-linkedin-hover);
}



@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
  }

  .hero-buttons {
    flex-wrap: wrap;
  }

  .btn {
    width: 100%;
  }
}