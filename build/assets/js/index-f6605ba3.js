import{r as q,a as yl,c as zn,g as _u,b as xu,R as ku}from"./vendor-c28bf532.js";import{B as Su,a as Cu,F as qa,b as Eu,c as Ou,d as Nu,e as Pu,f as Tu,g as <PERSON>,h as Au}from"./icons-2f7eca53.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function r(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(i){if(i.ep)return;i.ep=!0;const o=r(i);fetch(i.href,o)}})();var _l={exports:{}},Pi={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ru=q,Iu=Symbol.for("react.element"),$u=Symbol.for("react.fragment"),zu=Object.prototype.hasOwnProperty,ju=Ru.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Lu={key:!0,ref:!0,__self:!0,__source:!0};function xl(t,e,r){var n,i={},o=null,a=null;r!==void 0&&(o=""+r),e.key!==void 0&&(o=""+e.key),e.ref!==void 0&&(a=e.ref);for(n in e)zu.call(e,n)&&!Lu.hasOwnProperty(n)&&(i[n]=e[n]);if(t&&t.defaultProps)for(n in e=t.defaultProps,e)i[n]===void 0&&(i[n]=e[n]);return{$$typeof:Iu,type:t,key:o,ref:a,props:i,_owner:ju.current}}Pi.Fragment=$u;Pi.jsx=xl;Pi.jsxs=xl;_l.exports=Pi;var g=_l.exports,yo={},Ua=yl;yo.createRoot=Ua.createRoot,yo.hydrateRoot=Ua.hydrateRoot;const Du=()=>{const[t,e]=q.useState("hero");return q.useEffect(()=>{const r=()=>{const n=document.querySelectorAll("section"),i=document.getElementById("navbar").offsetHeight,o=window.scrollY+i+50;n.forEach(a=>{const s=a.offsetTop,l=a.offsetHeight,c=a.getAttribute("id");o>=s&&o<s+l&&e(c)})};return window.addEventListener("scroll",r),()=>window.removeEventListener("scroll",r)},[]),t},kl=()=>{const[t,e]=q.useState(!1),[r,n]=q.useState(!1),[i,o]=q.useState({width:typeof window<"u"?window.innerWidth:0,height:typeof window<"u"?window.innerHeight:0});return q.useEffect(()=>{const a=()=>{const s=window.innerWidth,l=window.innerHeight;o({width:s,height:l}),e(s<=768),n(s>768&&s<=1024)};return a(),window.addEventListener("resize",a),window.addEventListener("orientationchange",a),()=>{window.removeEventListener("resize",a),window.removeEventListener("orientationchange",a)}},[]),{isMobile:t,isTablet:r,isDesktop:!t&&!r,screenSize:i,orientation:i.width>i.height?"landscape":"portrait"}},Fu=()=>{const[t,e]=q.useState("up"),[r,n]=q.useState(0),i=q.useRef(0);return q.useEffect(()=>{const o=()=>{const a=window.scrollY,s=a>i.current?"down":"up";s!==t&&Math.abs(a-i.current)>10&&e(s),n(a),i.current=a>0?a:0};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)},[t]),{scrollDirection:t,scrollY:r}},Hu="https://raw.githubusercontent.com/jsonresume/resume-schema/v1.0.0/schema.json",Bu={name:"Nicholas Gerasimatos",label:"Principal Technical Consultant | Cloud and Platform Engineering",image:"",email:"<EMAIL>",phone:"************",url:"https://www.linkedin.com/in/nicholas-gerasimatos",summary:"Specializing in innovative cloud and platform solutions, I align technology with client goals to drive impactful results. With deep expertise in open-source and cloud technologies, I communicate complex systems in a clear, accessible manner and prioritize a client-centered approach. I bring a proven track record in designing and implementing scalable, secure, resilient solutions, leading modernization initiatives, and developing actionable architectures that achieve measurable business outcomes.",location:{countryCode:"US",address:"United States"},profiles:[{network:"LinkedIn",username:"ngerasimatos",url:"https://www.linkedin.com/in/ngerasimatos/"}]},Wu=[{name:"AHEAD",position:"Principal - Platform Engineering and Modernization",startDate:"2024-05-31",endDate:"2024-12-31",highlights:[],summary:`Emerging Technologies Expertise:

Maintain a leading-edge understanding of emerging technologies, with a particular focus on artificial intelligence, and machine learning. Implement AI-driven solutions that streamline operations, and unlock new business value.

Advanced Solution Development:

Architect, design, and implement cutting-edge solutions leveraging open-source frameworks and cloud-native technologies, including Infrastructure as Code (IaC), Platform as a Service (PaaS), and Infrastructure as a Service (IaaS). Integrate advanced AI/ML and AI capabilities to enhance system intelligence, automate workflows, and drive improvements in performance, scalability, and cost efficiency.

Cloud and Hybrid Architecture Optimization:

Optimize enterprise cloud and hybrid infrastructures by applying distributed computing principles and best practices. Conduct in-depth architectural assessments to identify bottlenecks, reduce latency, and maximize system uptime. Deliver robust, resilient solutions that support critical business operations and ensure seamless scalability for large-scale enterprise clients.

Effective Technical Communication:

Translate complex technical concepts into clear, actionable insights for both technical and non-technical stakeholders. Develop and deliver compelling presentations and technical documentation to secure executive buy-in, expedite project approvals, and accelerate delivery timelines. 

Leadership in Cross-Functional Delivery:

Lead diverse, cross-functional teams through the full project lifecycle, from initial scoping to final delivery. Utilize agile methodologies and GitOps practices to drive collaboration, transparency, and continuous improvement.

Advocacy for Best Practices:

Actively champion industry best practices in cloud architecture and software delivery. Mentor teams, develop and disseminate technical guidelines, and establish governance frameworks to elevate the quality and consistency of project outcomes.`,url:"https://www.linkedin.com/company/ahead_2/",location:"West Coast"},{name:"Amazon Web Services (AWS)",position:"Partner Cloud Architect",startDate:"2022-12-31",endDate:"2024-05-31",highlights:[],summary:`Cloud Solution Architecture and Optimization:

Design, implement, and optimize scalable, secure, and cost-effective AWS cloud solutions with a focus on resiliency, high availability, performance, and cost efficiency. Leverage ROSA/OpenShift, AWS Bedrock, and various other AWS services to deliver intelligent, automated solutions that drive business value.

Cross-Functional Collaboration:

Collaborate with partners, customers, and cross-functional teams to understand diverse requirements and translate them into robust cloud architectures.

Complex Cloud Infrastructure Deployment:

Architect and deploy sophisticated cloud infrastructures tailored to client needs, ensuring seamless integration of AI/ML and generative AI solutions for improved performance and automation.

Training and Enablement Leadership:

Lead training sessions and workshops for internal teams and clients, focusing on Red Hat, Open-Source, and AWS architecture best practices, security compliance, and the adoption of emerging technologies. 

Partner and Community Engagement:

Conduct internal and partner training, participate in industry events, and engage in global webinars and roadshows to showcase innovative AWS and partner solutions. 

Technical Curriculum Development:

Build and implement comprehensive education programs for technical teams, including Immersion Days, Game Days, workshops, and technical documentation that highlight AWS services and partner integrations.

Sales Enablement and Competitive Positioning:

Create concise battle cards to showcase partner solutions differentiators and value. Develop playbooks outlining synergies between partner offerings and AWS services. 

Customer Advocacy and Success Story Development:

Collect and curate customer references and success stories, including co-branded content and showcases of GSI/ISV-developed solutions that demonstrate the impact of AWS and partner driven cloud transformations.`,url:"https://www.linkedin.com/company/amazon-web-services/"},{name:"Red Hat",position:" Emerging Technologies Cloud Engineer ",startDate:"2015-12-31",endDate:"2022-12-31",highlights:[],summary:`Strategic Cloud Modernization and Digital Transformation:

Lead modernization and digital transformation initiatives by driving strategic cloud adoption. Unlock new business opportunities and deliver measurable business benefits through the integration of emerging technologies, blockchain, edge computing, OpenShift, AI, ML, and serverless architectures. Enable organizations to become more agile and future-ready.

Stakeholder Engagement and Consensus Building:

Clearly articulate the value of cloud solutions and emerging technologies to a wide range of stakeholders. Address concerns, answer technical and business questions, and build consensus to ensure buy-in from executive, technical, and operational teams.

Seamless Transition to Cloud-Centric Operating Models:

Facilitate smooth transitions to cloud-centric operating models by ensuring effective integration of advanced technologies such as GenAI, and AI/ML into existing and new workflows. Oversee the adoption of hybrid and multi-cloud strategies to maximize flexibility and support ongoing innovation.

Cloud Readiness Assessments and Migration Strategy:

Develop and implement tailored migration strategies and multiphase roadmaps that align short-term wins with long-term business objectives. Ensure each migration is smooth and cost-effective.

Thought Leadership and Industry Influence:

Establish thought leadership by publishing technical articles, delivering presentations at industry conferences, and actively participating in cloud and emerging technology communities. Share insights on digital transformation, cloud adoption, and practical applications of AI, and ML. 

Collaboration and Innovation Culture:

Build strong partnerships with internal teams, cross-functional stakeholders, and technology vendors to drive continuous innovation. Foster a culture of learning and collaboration that encourages exploration and adoption of emerging technologies in cloud-based solutions.`,url:"https://www.linkedin.com/company/red-hat/"},{name:"FICO",position:"Director of Cloud Service Platforms",startDate:"2013-12-31",endDate:"2015-12-31",highlights:[],summary:`Strategic Cloud Platform Planning and Implementation:

Lead the strategic planning, design, and implementation of cloud service platforms for organizations of varying scale and complexity. Align cloud solutions with business needs to ensure robust, scalable, and secure platforms that support growth and innovation.

Digital Transformation and Service Delivery:

Drive digital transformation initiatives by optimizing infrastructure performance and ensuring seamless delivery of cloud services to both internal and external stakeholders. Focus on enhancing agility, reliability, and user experience across the organization.

Comprehensive Cloud Strategy Development:

Develop and execute comprehensive cloud strategies that align with organizational objectives. Lead cross-functional teams to deliver cloud services that meet business goals, enhance operational efficiency, and foster a culture of innovation.

Expertise in Cloud Service Models:

Demonstrate deep expertise in infrastructure-as-a-service (IaaS), platform-as-a-service (PaaS), and software-as-a-service (SaaS) models. Select and implement effective service models to meet diverse business requirements.

Team Leadership and Talent Development:

Foster a high-performance culture by building collaborative teams, recruiting top talent, and providing mentorship and professional development opportunities. Empower team members to excel in cloud technologies and deliver outstanding results.

Security, Compliance, and Data Protection:
Ensure compliance with industry standards and regulations by establishing robust security controls and data protection measures. Proactively manage risk and safeguard organizational data across all cloud environments.

Vendor and Partner Relationship Management:
Establish and maintain strong relationships with cloud service providers, vendors, and partners. Manage service level agreements (SLAs), and drive value-added partnerships that support organizational goals and deliver long-term value.`,url:"https://www.linkedin.com/company/amazon-web-services"},{name:"American Express",position:"Senior Data Architect",startDate:"2010-12-31",endDate:"2014-12-31",highlights:[],summary:`Designed and implemented scalable, performant, and secure data solutions driving data-driven insights and business value

Applied data architecture principles and a wide range of technologies to solve complex business challenges

Collaborated with cross-functional teams to translate diverse requirements into robust, actionable data solutions aligned with strategic organizational goals

Crafted conceptual, logical, and physical data models ensuring data integrity, accessibility, and optimal organization for diverse analytical workloads

Aligned data architectures with enterprise-wide data governance standards and incorporated long-term scalability considerations

Designed big data environments (Hadoop, Spark, etc.) enabling acquisition, transformation, and analysis of high-volume, high-velocity, and high-variety datasets

Architected solutions empowering advanced analytics, machine learning initiatives, and real-time decision-making

Demonstrated expertise in data privacy regulations, security frameworks, and access control methodologies

Extensive experience with cloud-native or hybrid data solutions (AWS, Azure, GCP) optimizing cost and performance through strategic selection of cloud services and technologies

Identified root causes of data-related bottlenecks and inefficiencies, proposing innovative solutions balancing immediate needs with long-term sustainability`,url:"https://www.linkedin.com/company/american-express/"},{name:"VCE",position:"Principal Architect",startDate:"2009-12-31",endDate:"2012-12-31",highlights:[],summary:`Led the design and implementation of complex and innovative solutions, collaborating with cross-functional teams and stakeholders to align technology initiatives with business goals, enhance performance, and drive organizational growth.

Demonstrated expertise in architecting scalable, reliable, and secure solutions across a wide range of domains and technologies, with a deep understanding of architectural patterns, best practices, and industry standards.

Developed technology roadmaps and long-term architectural visions, leading and mentoring teams to foster a culture of innovation and drive initiatives that deliver value to the organization.

Possessed comprehensive knowledge of various technologies, frameworks, and platforms across cloud computing, distributed systems, microservices, and enterprise architecture, leveraging emerging technologies to drive business innovation and competitive advantage.

Designed and implemented large-scale, distributed, and highly available systems, integrating disparate systems and ensuring interoperability, with proficiency in scalability, performance optimization, and capacity planning.

Demonstrated excellent communication and interpersonal skills, engaging and influencing stakeholders at all levels of the organization, building relationships, fostering collaboration, and presenting complex technical concepts in a clear and concise manner.`,url:"https://www.linkedin.com/company/vce/"},{name:"Microsoft",position:"Senior System Engineer",startDate:"2006-12-31",endDate:"2009-12-31",highlights:[],summary:`Designed, implemented, and managed the Microsoft CORE software development infrastructure platform

Leveraged Microsoft and open-source technologies to optimize system performance, enhance security, and streamline operations

Drove successful projects and collaborated with cross-functional teams to deliver innovative solutions aligned with business objectives

Demonstrated deep knowledge and hands-on experience with Microsoft technologies such as Windows Server, Active Directory, Microsoft Exchange, Microsoft 365, Azure, and PowerShell

Proficient in designing, configuring, and managing Microsoft-based systems and services   Architected and designed scalable and resilient systems based on Microsoft technologies

Skilled in capacity planning, performance tuning, and optimizing infrastructure for maximum efficiency and availability

Integrated on-premises systems with Microsoft Azure and proficient in hybrid cloud design, migration strategies, and managing cloud-based infrastructure

Strong understanding of Microsoft security technologies and best practices, including Identity and Access Management, Active Directory security, and data protection

Implemented security controls and ensured compliance with industry standards

Demonstrated exceptional analytical and problem-solving skills to diagnose and resolve`,url:"https://www.linkedin.com/company/microsoft/",location:"Greater Seattle Area"}],Vu=[{organization:"HUMANE SOCIETY",position:"Volunteer",startDate:"2010-01-01",endDate:"2015-12-31",summary:"Volunteer work supporting animal welfare and community outreach programs.",highlights:[],url:"https://www.linkedin.com/company/9539229"}],qu=[{title:"RED HAT HONORS OUTSTANDING ACHIEVEMENTS IN OPEN SOURCE WITH NINTH ANNUAL RED HAT INNOVATION AWARDS",date:"2015-10-26",awarder:"Red Hat",summary:`Recognizing striking flexibility, scalability, cost effectiveness, performance, and security within an infrastructure.

Winner: FICO
Leading analytics software company FICO helps businesses in more than 90 countries make better decisions that drive higher levels of growth, profitability and customer satisfaction. FICO wanted to extend its successful, high-end analytics business into new industries and markets. To capitalize on the growing demand for Big Data analytics among companies of all sizes, the company sought to complement its on-premise solution with a web-based service. Using OpenShift Enterprise, the company developed FICO® Analytic Cloud. The new Platform-as-a-Service (PaaS) offering has driven more than US$10 million in sales in a short time frame, winning business from companies that otherwise couldn't have afforded the time or cost of implementing FICO’s sophisticated analytics solutions. Using the Red Hat technology, FICO reduced infrastructure operations staffing by 20 percent, saved hundreds of thousands of dollars in hardware costs, and improved time to market by 70 percent.`}],Uu=[{name:"Certified to architect, deploy and implement Vblock 100/200/300/700 infrastructure systems",issuer:"VCE"},{name:"VMware Certified Professional - Data Center Virtualization",issuer:"VMware"},{name:"VMware Sales Professional, Application Modernization, Data Management, Business Continuity, Virtualization of Business Critical Applications, Management, Cloud IaaS, Desktop Virtualization",issuer:"VMware"},{name:"VMware Technical Sales Professional - Business Continuity, Virtualization of Business Critical Applications, Data Management,  Infrastructure Virtualization",issuer:"VMware"},{name:"VMware Certified Associate - Data Center Virtualization, Cloud, Workforce Mobility",issuer:"VMware"},{name:"Red Hat Certificate of Expertise in Platform-as-a-Service",issuer:"Red Hat"},{name:"Red Hat Certificate of Expertise in Data Virtualization",issuer:"Red Hat"},{name:"Red Hat Certificate of Expertise in Clustering and Storage Management",issuer:"Red Hat"},{name:"AWS Certified Solutions Architect - Associate",issuer:"Amazon Web Services (AWS)",endDate:"2025-02-28",startDate:"2023-02-28",url:"https://www.credly.com/badges/3f30bd86-6157-4c5d-9ff5-5b47f38cdb07/public_url"},{name:"Google Cloud - Introduction to Generative AI",issuer:"Google",startDate:"2023-06-30",url:"https://cdn.qwiklabs.com/%2FTntrCzBhpKkF9LHUgvevvIKQb2%2Bufpupa1zPSlY%2Fcs%3D"}],Gu=[{name:"DELL-EMC MERGER LEAVES IT PROS HOPEFUL AND CONCERNED",publisher:"Search Storage",releaseDate:"2015-12-31",summary:`Nick Gerasimatos, director of cloud service and engineering at Fair Isaac Corp. (FICO), sees the merger as a way for Dell to "finally get enterprise-grade storage" because "Compellent was not really enterprise-ready." He also said the deal is also good for VMware to "hopefully allow them to spin and eliminate the influence of EMC and increase their innovation."

But Gerasimatos doesn't see the merger having an impact on FICO's purchasing plans. He said the company is shifting to OpenStack, Ceph software-defined storage and public cloud options to try to move away from VMware Enterprise, vCloud Director and EMC's VMAX, VNX and Avamar within two to three years. He said the main impediment is waiting for hardware depreciation.

"The ROI is not there" with VMware/EMC products, Gerasimatos wrote in an e-mail. He added that FICO staff prefers OpenStack options because they can participate in the software evolution and modify source code to fit their specific needs. "Also, EMC and VMware have a very poor OpenStack story that is less than impressive," he added.

FICO uses SolidFire all-flash arrays for workloads that need high performance or replication and Cisco's UCS as its hardware standard. Only legacy environments use EMC and NetApp, according to Gerasimatos. `,url:"http://searchstorage.techtarget.com/news/4500255416/Dell-EMC-merger-leaves-IT-pros-hopeful-and-concerned"},{name:"FICO CHOOSES RED HAT TO DEPLOY OPENSTACK, MANAGEMENT, AND STORAGE SOLUTIONS FOR AGILE CLOUD INFRASTRUCTURE",publisher:"Market Watch",releaseDate:"2015-12-31",summary:`Nick Gerasimatos, Cloud Development Director, FICO 
“With Red Hat's OpenStack on Cisco UCS and Ceph, we've been able to create an elastic scalable infrastructure that delivers all of the benefits of cloud – speed of innovation, agility, the ability to deliver Software-as-a-Service – but with the ability to securely manage our resources in a private datacenter. This gives us the cloud platform we need to create FICO's offerings, but with control of our data, workloads, compliance and security.”`,url:"http://www.marketwatch.com/story/fico-chooses-red-hat-to-deploy-openstack-management-and-storage-solutions-for-agile-cloud-infrastructure-2015-10-26"},{name:"WHAT’S BEHIND THE ODD COUPLE MICROSOFT-RED HAT PARTNERSHIP",publisher:"Network World",releaseDate:"2015-12-31",summary:"Red Hat customers seemed to embrace the news too. “I think it’s a big win for both companies but a bigger win for Red Hat since Microsoft is now ‘all in’ with their distribution and technologies,” says Nicholas Gerasimatos, director of cloud services engineering at FICO, a big Red Hat user.",url:"http://www.networkworld.com/article/3001391/microsoft-subnet/what-s-behind-the-odd-couple-microsoft-red-hat-partnership.html"},{name:"WILL OPEN SOURCE STORAGE MAKE THE HYPER SCALE DREAM REAL?",publisher:"The Register",releaseDate:"2015-12-31",summary:`Open-source software has become an important presence in many areas of IT, and now, as storage increasingly becomes software-defined storage, it is storage's turn. The darling of the open-source storage movement – though it is by no means the only viable and popular option – is Ceph.

A unified storage platform originally developed for a PhD dissertation in California, Ceph has become one of the most popular, if not THE most popular, storage layers for OpenStack deployments, and with OpenStack leading the cloud computing charge, Ceph is benefiting considerably.`,url:"http://www.theregister.co.uk/2015/11/09/open_source_hyperscale_storage/?mt=1447100084309"},{name:"FICO EMBRACES OPENSTACK | #OPENSTACK",publisher:"SiliconANGLE",releaseDate:"2015-08-21",summary:`FICO’s agressive adoption model

“We adopted it aggressively,” said Gerasimatos when asked how FICO made the transition to OpenStack. FICO’s timeframe from zero to a fully functioning solution was just 12 months.
“What we needed to do was to become a little more agile as we were going global, so that pushed us to go toward the more OpenStack design,” he said. Gerasimatos credits FICO’s close relationship with Red Hat, Inc. as being mutually beneficial.
‘Don’t be afraid; embrace it!’

Gerasimatos sees the benefits of OpenStack as the low point of entry, scalability, software-defined networking and storage without having to pay the penalties. He lists problems encountered during the transition, including how the FICO operations team struggled to get their heads around the distributed scale-out design, as well as difficulties finding qualified engineers with open-source experience. But despite the challenges getting up and going, Gerasimatos said that FICO is very happy with open source and encourages others to follow in the company’s footsteps.

“Everyone is learning as they go along,” he stated. “Every major company, even including Microsoft, is embracing containers and the new scale-out design architecture. I would say don’t be afraid; embrace it!”`,url:"http://siliconangle.com/blog/2015/08/21/fico-embraces-openstack-openstack/"},{name:"FICO PROVES THE MAINSTREAM OPENSTACK ADOPTION POINT",publisher:"Forbes",releaseDate:"2015-06-09",summary:"Analysis of FICO's successful OpenStack adoption as a mainstream enterprise use case, demonstrating the maturity and viability of OpenStack for large-scale production environments.",url:"http://www.forbes.com/sites/benkepes/2015/06/09/fico-proves-the-mainstream-openstack-adoption-point/"},{name:"FICO SAYS OPENSTACK ENTERPRISE IS READY FOR PRIMETIME",publisher:"SearchCloudComputing",releaseDate:"2015-05-01",summary:`FICO, an analytics software company in San Jose, Calif., uses Red Hat to deploy its OpenStack private cloud as opposed to one of the larger public cloud options because it's a more distributed architecture with a higher number of availability zones internationally, said Nick Gerasimatos, director of engineering and cloud services.

The workloads have been in production for 90 days and everything has gone smoothly so far, but there are still issues with some of the projects not maturing fast enough, Gerasimatos said.

"The extensibility of Neutron, it's more for flat networks and it's not great for large scalable infrastructure," Gerasimatos said.  "A lot of that is supposedly changing in Kilo but we would have liked to have seen that a few versions ago."`,url:"http://searchcloudcomputing.techtarget.com/news/4500246812/OpenStack-enterprise-users-say-its-ready-for-primetime"},{name:"HOW STORAGE WORKS IN CONTAINERS",publisher:"OpenStack Superuser",releaseDate:"2015-04-01",summary:"Nick Gerasimatos, senior director of cloud services engineering at FICO, dives into the lack of persistent storage with containers and how Docker volumes and data containers provide a fix.",url:"http://superuser.openstack.org/articles/how-storage-works-in-containers?awesm=awe.sm_jOEqS"},{name:"HOW TO BUILD A LARGE SCALE MULTI-TENANT CLOUD SOLUTION",publisher:"LinkedIn",releaseDate:"2015-03-01",summary:`Its not terribly difficult to design and build a turnkey integrated configured SDDC ready to use solution. 
However building one that completely abstracts the compute, storage and network physical resources and provides multiple tenants a pool of logical resources along with all the necessary management, operational and application level services and allows to scale resources with seamless addition of new rack units. `,url:"https://www.linkedin.com/pulse/how-build-large-scale-multi-tenant-cloud-solution-gerasimatos?trk=prof-post"},{name:"THINK FICO IS A CREDIT SCORING COMPANY? NOPE: IT'S ABOUT LARGE-SCALE ANALYTICS",publisher:"OpenStack Superuser",releaseDate:"2015-02-01",summary:`“We’re always known as a credit-scoring company, but we’re a actually large-scale analytics company,” says Nick Gerasimatos, director of engineering and cloud services at FICO. While 90 percent of all lending decisions in the U.S. currently rely on FICO scores, in 2013 the company launched the FICO analytic cloud for creating, customizing and deploying analytic-driven applications and services. “There are a lot of financial and government institutions that we integrate with and we spend a lot of time deploying in different countries.”

In just the last 12-18 months, he says that the company has tripled in size and number of deployments thanks to OpenStack — reaching Australia, Turkey South Africa, China, Japan in addition to spanning the United States.`,url:"http://superuser.openstack.org/articles/think-fico-is-a-credit-scoring-company-nope-it-s-about-large-scale-analytics"}],Zu=[{name:"OpenStack",level:"",keywords:[]},{name:"Cloud Computing IaaS",level:"",keywords:[]},{name:"Continuous Integration and Continuous Delivery (CI/CD)",level:"",keywords:[]},{name:"GitOps",level:"",keywords:[]},{name:"Business Ownership",level:"",keywords:[]},{name:"Containers",level:"",keywords:[]},{name:"OpenShift",level:"",keywords:[]},{name:"Amazon EKS",level:"",keywords:[]},{name:"Load Balancing",level:"",keywords:[]},{name:"Docker",level:"",keywords:[]},{name:"Hybrid Cloud",level:"",keywords:[]},{name:"Red Hat Linux",level:"",keywords:[]},{name:"VMware",level:"",keywords:[]},{name:"Amazon Web Services (AWS)",level:"",keywords:[]},{name:"Private Cloud",level:"",keywords:[]},{name:"Converged Infrastructure",level:"",keywords:[]},{name:"Cloud Development",level:"",keywords:[]},{name:"Research",level:"",keywords:[]},{name:"Defining Requirements",level:"",keywords:[]},{name:"Cloud Security",level:"",keywords:[]},{name:"Kubernetes",level:"",keywords:[]},{name:"Cloud Applications",level:"",keywords:[]},{name:"Engineering",level:"",keywords:[]},{name:"Red Hat Enterprise Linux (RHEL)",level:"",keywords:[]},{name:"Requirements Gathering",level:"",keywords:[]},{name:"Large Language Models (LLM)",level:"",keywords:[]},{name:"Open Systems Architecture",level:"",keywords:[]},{name:"Amazon Bedrock",level:"",keywords:[]},{name:"Distributed Systems",level:"",keywords:[]},{name:"Systems Engineering",level:"",keywords:[]},{name:"Professional Services",level:"",keywords:[]},{name:"Virtualization",level:"",keywords:[]},{name:"Windows Server",level:"",keywords:[]},{name:"Management",level:"",keywords:[]},{name:"Software Defined Storage",level:"",keywords:[]},{name:"Linux",level:"",keywords:[]},{name:"Cloud Computing",level:"",keywords:[]},{name:"Software Defined Networking",level:"",keywords:[]},{name:"Open-Source Software",level:"",keywords:[]},{name:"Release Engineering",level:"",keywords:[]},{name:"Capacity Planning",level:"",keywords:[]},{name:"DevOps",level:"",keywords:[]},{name:"Software Quality Assurance",level:"",keywords:[]},{name:"IaaS",level:"",keywords:[]},{name:"Machine Learning",level:"",keywords:[]},{name:"Vagrant",level:"",keywords:[]},{name:"Software Development",level:"",keywords:[]},{name:"PaaS",level:"",keywords:[]},{name:"Artificial Intelligence (AI)",level:"",keywords:[]}],Yu=[{fluency:"Native Speaker",language:"English"}],Ku=[],Qu=[{name:"David Mitrany",reference:"Nicholas Gerasimatos is a very bright individual in high demand due to his extensive leadership and problem solving skills.  You only need to glance at the list of high profile companies Nicholas has worked for - which is impressive in itself but once you interview Nicholas, that is when you realize Nicholas is the real deal."},{name:"Al Eskew",reference:"I had the opportunity and privilege of working with Nick during his time with Amex.  His technical expertise is of the highest caliber and I would highly recommend him in any of his listed skill sets.  "},{name:"Tony Peters",reference:"I have had the opportunity to work with Nick at FICO for the past two years designing the architecture of FICO's cloud infrastructure.  There are few people out in the industry that equal Nick's combined knowledge of Cloud, Virtualization, Compute and Storage.  The most impressive part of working with Nick is not just that he is knowledgeable but more importantly he's an outstanding communicator and leader, both for the teams he represents as well as working with FICO's business partners, which I am fortunate to take part in."},{name:"Todd N Marinich",reference:"I had pleasure to have Nicholas on our team at AMEX. Nicholas is a dedicated technologist and trusted advisor in his field. His dedication and commitment to his craft is very impressive. Always striving to achieve the client’s goals and a team player. I would recommend Nicholas to anyone looking to solve challenging objectives."},{name:"Justin Watson",reference:"Nick is one of the most talented professionals I have had the honor to work with in technology.  He came in to address performance issues with the virtual environment and a mis-configured UCS system.  Nick hit the ground running, extremely knowledgeable and confident.  He proposed bold changes and produced big results very quickly.  He is a subject matter expert across many disciples and continues to embrace emerging technologies."},{name:"Kyle Bardet",reference:"I had the pleasure of working with Nick during his Residency at PayPal for VCE. Nick was instrumental in the initial implementation of the Vblock infrastructure, as well as leading the Managed Services team that was onsite. Nick is the consummate professional and worked very closely with the customer, understanding their needs and offering direction, when required. He worked well with the other team members to ensure the highest level of customer satisfaction. He brought a lot to the table on the technical side, applying his vast experience to this new implementation. Nick is a hard worker and will stay with an issue until it becomes fully resolved, no matter the timeframe or the effort required."}],Xu=[],Ju={version:"v1.0.0",canonical:"https://github.com/jsonresume/resume-schema/blob/v1.0.0/schema.json"},ep={$schema:Hu,basics:Bu,work:Wu,volunteer:Vu,awards:qu,certificates:Uu,publications:Gu,skills:Zu,languages:Yu,interests:Ku,references:Qu,projects:Xu,meta:Ju};class tp{constructor(){this.data=ep}getBasics(){return this.data.basics}getWork(){return this.data.work}getSkills(){return this.data.skills}getEducation(){return this.data.education||[]}getCertificates(){return this.data.certificates}getPublications(){return this.data.publications}getAwards(){return this.data.awards}getReferences(){return this.data.references}getFormattedWorkExperience(){return this.data.work.map(e=>({title:e.position,company:e.name,duration:this.formatDateRange(e.startDate,e.endDate),location:e.location,responsibilities:e.summary.split(`

`).filter(r=>r.trim()),url:e.url}))}getFormattedSkills(){const e={"Cloud & Virtualization":[],"DevOps & Containerization":[],"Operating Systems & Infrastructure":[],"AI & Data":[],"Software Development & Methodologies":[],"Business & Strategy":[],"Architecture & Core Engineering":[],"Tools & Other":[]};return this.data.skills.forEach(r=>{const n=r.name;let i=!1;n.includes("Cloud")||n.includes("AWS")||n.includes("Amazon Web Services")||n.includes("Azure")||n.includes("Hybrid Cloud")||n.includes("VMware")||n.includes("Private Cloud")||n.includes("IaaS")||n.includes("PaaS")||n.includes("OpenStack")||n.includes("Cloud Security")||n.includes("Virtualization")||n.includes("Cloud Computing")?(e["Cloud & Virtualization"].push(n),i=!0):n.includes("CI/CD")||n.includes("Continuous Integration")||n.includes("GitOps")||n.includes("Docker")||n.includes("Containers")||n.includes("OpenShift")||n.includes("EKS")||n.includes("Amazon EKS")||n.includes("Kubernetes")||n.includes("DevOps")||n.includes("Release Engineering")||n.includes("Vagrant")?(e["DevOps & Containerization"].push(n),i=!0):n.includes("Red Hat Linux")||n.includes("Red Hat Enterprise Linux (RHEL)")||n.includes("Windows Server")||n.includes("Load Balancing")||n.includes("Software Defined Storage")||n.includes("Software Defined Networking")||n.includes("Linux")||n.includes("Converged Infrastructure")?(e["Operating Systems & Infrastructure"].push(n),i=!0):n.includes("AI")||n.includes("Machine Learning")||n.includes("LLM")||n.includes("Amazon Bedrock")||n.includes("Artificial Intelligence")||n.includes("Large Language Models")?(e["AI & Data"].push(n),i=!0):n.includes("Software Development")||n.includes("Software Quality Assurance")||n.includes("Open-Source Software")?(e["Software Development & Methodologies"].push(n),i=!0):n.includes("Business Ownership")||n.includes("Professional Services")||n.includes("Management")||n.includes("Research")||n.includes("Defining Requirements")||n.includes("Requirements Gathering")?(e["Business & Strategy"].push(n),i=!0):(n.includes("Open Systems Architecture")||n.includes("Distributed Systems")||n.includes("Systems Engineering")||n.includes("Capacity Planning")||n.includes("Engineering"))&&(e["Architecture & Core Engineering"].push(n),i=!0),i||e["Tools & Other"].push(n)}),Object.keys(e).forEach(r=>{e[r].length===0&&delete e[r]}),e}getFormattedCertificates(){return this.data.certificates.map(e=>({name:e.name,issuer:e.issuer,date:e.date}))}getFormattedPublications(){return this.data.publications.map(e=>({name:e.name,publisher:e.publisher,releaseDate:e.releaseDate,url:e.url,summary:e.summary}))}formatDateRange(e,r){const n=new Date(e),i=r?new Date(r):null,o=a=>a.toLocaleDateString("en-US",{year:"numeric",month:"short"});return i?`${o(n)} - ${o(i)}`:`${o(n)} - Present`}}const qe=new tp,rp=()=>{const t=Du(),[e,r]=q.useState(!1),{isMobile:n}=kl(),{scrollDirection:i,scrollY:o}=Fu(),a=n&&i==="down"&&o>100,s=d=>{let p=`# ${d.basics.name}

`;if(p+=`${d.basics.label} | ${d.basics.email} | ${d.basics.phone} | ${d.basics.url}

`,p+=`## Summary

${d.basics.summary}

`,d.work&&d.work.length>0&&(p+=`## Experience

`,d.work.forEach(h=>{p+=`### ${h.position} at ${h.name}
`,p+=`${h.startDate} - ${h.endDate}

`,p+=`${h.summary}

`})),d.education&&d.education.length>0&&(p+=`## Education

`,d.education.forEach(h=>{p+=`### ${h.studyType} from ${h.institution}
`,p+=`${h.startDate} - ${h.endDate}

`})),d.skills&&d.skills.length>0){p+=`## Skills

`;const h=qe.getFormattedSkills();Object.keys(h).forEach(b=>{p+=`### ${b}
`,p+=h[b].map(f=>`- ${f}`).join(`
`)+`

`})}return d.certificates&&d.certificates.length>0&&(p+=`## Certifications

`,d.certificates.forEach(h=>{p+=`- ${h.name} (${h.issuer})
`}),p+=`
`),d.publications&&d.publications.length>0&&(p+=`## Publications

`,d.publications.forEach(h=>{p+=`### [${h.name}](${h.url})
`,p+=`${h.publisher}, ${h.releaseDate}

`,p+=`${h.summary}

`})),p},l=d=>{const p=qe.data;let h=`Nicholas_Gerasimatos_Resume.${d}`,b,f;if(d==="json")b=JSON.stringify(p,null,2),f="application/json";else if(d==="md")b=s(p),f="text/markdown";else return;const w=new Blob([b],{type:f}),y=URL.createObjectURL(w),v=document.createElement("a");v.href=y,v.download=h,document.body.appendChild(v),v.click(),document.body.removeChild(v),URL.revokeObjectURL(y)},c=()=>{const d=!e;r(d),n&&(d?document.body.classList.add("mobile-menu-open"):document.body.classList.remove("mobile-menu-open"))},u=()=>{r(!1),document.body.classList.remove("mobile-menu-open")};return q.useEffect(()=>{const d=p=>{const h=document.getElementById("navbar");document.getElementById("hamburger"),e&&h&&!h.contains(p.target)&&r(!1)};return document.addEventListener("mousedown",d),()=>{document.removeEventListener("mousedown",d)}},[e]),q.useEffect(()=>{const d=p=>{p.key==="Escape"&&e&&r(!1)};return document.addEventListener("keydown",d),()=>{document.removeEventListener("keydown",d)}},[e]),g.jsx("nav",{className:`navbar ${a?"navbar--hidden":""} ${e?"navbar--menu-open":""}`,id:"navbar",role:"navigation","aria-label":"Main navigation",children:g.jsxs("div",{className:"nav-container",children:[g.jsx("div",{className:"nav-logo",children:g.jsx("span",{className:"nav-name",role:"banner",children:"Nicholas Gerasimatos"})}),g.jsxs("ul",{className:`nav-menu ${e?"active":""}`,id:"nav-menu",role:"menubar","aria-label":"Main menu","aria-hidden":!e&&n,children:[g.jsx("li",{role:"none",children:g.jsx("a",{href:"#hero",className:`nav-link ${t==="hero"?"active":""}`,onClick:u,role:"menuitem","aria-current":t==="hero"?"page":void 0,children:"Home"})}),g.jsx("li",{role:"none",children:g.jsx("a",{href:"#about",className:`nav-link ${t==="about"?"active":""}`,onClick:u,role:"menuitem","aria-current":t==="about"?"page":void 0,children:"About"})}),g.jsx("li",{role:"none",children:g.jsx("a",{href:"#experience",className:`nav-link ${t==="experience"?"active":""}`,onClick:u,role:"menuitem","aria-current":t==="experience"?"page":void 0,children:"Experience"})}),g.jsx("li",{role:"none",children:g.jsx("a",{href:"#skills",className:`nav-link ${t==="skills"?"active":""}`,onClick:u,role:"menuitem","aria-current":t==="skills"?"page":void 0,children:"Skills"})}),g.jsx("li",{role:"none",children:g.jsx("a",{href:"#education",className:`nav-link ${t==="education"?"active":""}`,onClick:u,role:"menuitem","aria-current":t==="education"?"page":void 0,children:"Education"})}),g.jsx("li",{role:"none",children:g.jsx("a",{href:"#publications",className:`nav-link ${t==="publications"?"active":""}`,onClick:u,role:"menuitem","aria-current":t==="publications"?"page":void 0,children:"Publications"})}),g.jsx("li",{role:"none",children:g.jsx("a",{href:"#contact",className:`nav-link ${t==="contact"?"active":""}`,onClick:u,role:"menuitem","aria-current":t==="contact"?"page":void 0,children:"Contact"})}),g.jsxs("li",{className:"download-buttons",role:"none",children:[g.jsx("button",{onClick:()=>l("json"),className:"btn btn--primary btn--sm","aria-label":"Download resume as JSON file",children:"Download JSON"}),g.jsx("button",{onClick:()=>l("md"),className:"btn btn--secondary btn--sm","aria-label":"Download resume as Markdown file",children:"Download Markdown"})]})]}),g.jsxs("button",{className:`hamburger ${e?"active":""}`,id:"hamburger",onClick:c,"aria-label":e?"Close mobile menu":"Open mobile menu","aria-expanded":e,"aria-controls":"nav-menu",children:[g.jsx("span",{"aria-hidden":"true"}),g.jsx("span",{"aria-hidden":"true"}),g.jsx("span",{"aria-hidden":"true"})]})]})})};var _o=new Map,jn=new WeakMap,Ga=0,np=void 0;function ip(t){return t?(jn.has(t)||(Ga+=1,jn.set(t,Ga.toString())),jn.get(t)):"0"}function op(t){return Object.keys(t).sort().filter(e=>t[e]!==void 0).map(e=>`${e}_${e==="root"?ip(t.root):t[e]}`).toString()}function ap(t){const e=op(t);let r=_o.get(e);if(!r){const n=new Map;let i;const o=new IntersectionObserver(a=>{a.forEach(s=>{var l;const c=s.isIntersecting&&i.some(u=>s.intersectionRatio>=u);t.trackVisibility&&typeof s.isVisible>"u"&&(s.isVisible=c),(l=n.get(s.target))==null||l.forEach(u=>{u(c,s)})})},t);i=o.thresholds||(Array.isArray(t.threshold)?t.threshold:[t.threshold||0]),r={id:e,observer:o,elements:n},_o.set(e,r)}return r}function sp(t,e,r={},n=np){if(typeof window.IntersectionObserver>"u"&&n!==void 0){const l=t.getBoundingClientRect();return e(n,{isIntersecting:n,target:t,intersectionRatio:typeof r.threshold=="number"?r.threshold:0,time:0,boundingClientRect:l,intersectionRect:l,rootBounds:l}),()=>{}}const{id:i,observer:o,elements:a}=ap(r),s=a.get(t)||[];return a.has(t)||a.set(t,s),s.push(e),o.observe(t),function(){s.splice(s.indexOf(e),1),s.length===0&&(a.delete(t),o.unobserve(t)),a.size===0&&(o.disconnect(),_o.delete(i))}}function lp({threshold:t,delay:e,trackVisibility:r,rootMargin:n,root:i,triggerOnce:o,skip:a,initialInView:s,fallbackInView:l,onChange:c}={}){var u;const[d,p]=q.useState(null),h=q.useRef(c),[b,f]=q.useState({inView:!!s,entry:void 0});h.current=c,q.useEffect(()=>{if(a||!d)return;let _;return _=sp(d,(S,E)=>{f({inView:S,entry:E}),h.current&&h.current(S,E),E.isIntersecting&&o&&_&&(_(),_=void 0)},{root:i,rootMargin:n,threshold:t,trackVisibility:r,delay:e},l),()=>{_&&_()}},[Array.isArray(t)?t.toString():t,d,i,n,o,a,r,l,e]);const w=(u=b.entry)==null?void 0:u.target,y=q.useRef(void 0);!d&&w&&!o&&!a&&y.current!==w&&(y.current=w,f({inView:!!s,entry:void 0}));const v=[p,b.inView,b.entry];return v.ref=v[0],v.inView=v[1],v.entry=v[2],v}const it=({children:t,delay:e=0})=>{const{ref:r,inView:n}=lp({triggerOnce:!0,threshold:.1});return g.jsx("div",{ref:r,className:n?"fade-in-up":"",style:{animationDelay:`${e}s`},children:t})};const cp=()=>{const t=qe.getBasics(),e=t.profiles.find(n=>n.network==="LinkedIn"),r=["Cloud Architecture","AI/ML Integration","Platform Engineering","Digital Transformation"];return g.jsxs("section",{id:"hero",className:"hero","aria-labelledby":"hero-title",children:[g.jsxs("div",{className:"video-background",children:[g.jsx("iframe",{src:"https://www.youtube.com/embed/tXxzOXwaf1c?autoplay=1&mute=1&loop=1&playlist=tXxzOXwaf1c&start=87&controls=0&showinfo=0&rel=0",title:"Background Video",frameBorder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,className:"video-iframe"}),g.jsx("div",{className:"video-overlay"})]}),g.jsx("div",{className:"hero-banner","aria-hidden":"true",children:g.jsx("div",{className:"banner-pattern"})}),g.jsx("div",{className:"hero-container",children:g.jsx("div",{className:"hero-content",children:g.jsxs("div",{className:"hero-text",children:[g.jsx(it,{delay:.1,children:g.jsx("h1",{id:"hero-title",className:"hero-title",children:t.name})}),g.jsx(it,{delay:.2,children:g.jsxs("h2",{className:"hero-headline",children:[t.label.split("|")[0].trim()," • ",r.slice(0,3).join(" • ")]})}),g.jsx(it,{delay:.3,children:g.jsx("p",{className:"hero-value-prop",children:"Driving business value through innovative cloud solutions and AI integration"})}),g.jsx(it,{delay:.4,children:g.jsxs("div",{className:"hero-buttons",children:[g.jsxs("a",{href:"#contact",className:"btn btn--primary",children:[g.jsx("span",{children:"Start a Conversation"}),g.jsx("span",{className:"btn-icon",children:"→"})]}),e&&g.jsxs("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"btn btn--linkedin",children:[g.jsx("span",{children:"LinkedIn Profile"}),g.jsx("span",{className:"btn-icon",children:"↗"})]})]})})]})})})]})};const dp=()=>{qe.getBasics().profiles.find(n=>n.network==="LinkedIn");const e=[{icon:"🚀",title:"Innovation Driver",description:"Led FICO's $10M+ analytics platform transformation with 70% faster time-to-market"},{icon:"☁️",title:"Cloud Expert",description:"AWS Partner Cloud Architect with 15+ years in enterprise cloud solutions"},{icon:"🤖",title:"AI Integration",description:"Implementing cutting-edge AI/ML solutions for Fortune 500 companies"}],r=[{quote:"Nicholas delivered exceptional results on our cloud transformation, reducing costs by 20% while improving performance.",author:"Enterprise Client",role:"Fortune 500 Company"},{quote:"His expertise in AI/ML integration helped us unlock new business opportunities worth millions.",author:"Technology Partner",role:"Cloud Solutions Provider"}];return g.jsx("section",{id:"about",className:"about","aria-labelledby":"about-title",children:g.jsxs("div",{className:"container",children:[g.jsx("h2",{id:"about-title",className:"section-title",children:"About Me"}),g.jsxs("div",{className:"about-content",children:[g.jsxs("div",{className:"about-intro",children:[g.jsx("p",{className:"about-text",children:"With 15+ years of experience in cloud and platform solutions, I specialize in transforming complex technical challenges into measurable business value. My expertise spans AI/ML integration, multi-cloud architectures, and enterprise modernization initiatives."}),g.jsxs("p",{className:"about-highlight",children:["🏆 ",g.jsx("strong",{children:"Red Hat Innovation Award Winner"})," • Recognized for delivering $10M+ in business value through innovative cloud solutions"]})]}),g.jsx("div",{className:"value-props-grid",children:e.map((n,i)=>g.jsxs("div",{className:"value-prop-card",children:[g.jsx("span",{className:"value-prop-icon",children:n.icon}),g.jsx("h3",{className:"value-prop-title",children:n.title}),g.jsx("p",{className:"value-prop-description",children:n.description})]},i))}),g.jsxs("div",{className:"testimonials-section",children:[g.jsx("h3",{className:"testimonials-title",children:"What Clients Say"}),g.jsx("div",{className:"testimonials-grid",children:r.map((n,i)=>g.jsxs("div",{className:"testimonial-card",children:[g.jsxs("p",{className:"testimonial-quote",children:['"',n.quote,'"']}),g.jsxs("div",{className:"testimonial-author",children:[g.jsx("span",{className:"author-name",children:n.author}),g.jsx("span",{className:"author-role",children:n.role})]})]},i))})]})]})]})})};var Sl={},Cl={},Ti={},El={};(function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e={animating:!1,autoplaying:null,currentDirection:0,currentLeft:null,currentSlide:0,direction:1,dragging:!1,edgeDragged:!1,initialized:!1,lazyLoadedList:[],listHeight:null,listWidth:null,scrolling:!1,slideCount:null,slideHeight:null,slideWidth:null,swipeLeft:null,swiped:!1,swiping:!1,touchObject:{startX:0,startY:0,curX:0,curY:0},trackStyle:{},trackWidth:0,targetSlide:0};t.default=e})(El);var up="Expected a function",Za=0/0,pp="[object Symbol]",hp=/^\s+|\s+$/g,fp=/^[-+]0x[0-9a-f]+$/i,mp=/^0b[01]+$/i,gp=/^0o[0-7]+$/i,vp=parseInt,bp=typeof zn=="object"&&zn&&zn.Object===Object&&zn,wp=typeof self=="object"&&self&&self.Object===Object&&self,yp=bp||wp||Function("return this")(),_p=Object.prototype,xp=_p.toString,kp=Math.max,Sp=Math.min,Zi=function(){return yp.Date.now()};function Cp(t,e,r){var n,i,o,a,s,l,c=0,u=!1,d=!1,p=!0;if(typeof t!="function")throw new TypeError(up);e=Ya(e)||0,xo(r)&&(u=!!r.leading,d="maxWait"in r,o=d?kp(Ya(r.maxWait)||0,e):o,p="trailing"in r?!!r.trailing:p);function h(I){var H=n,R=i;return n=i=void 0,c=I,a=t.apply(R,H),a}function b(I){return c=I,s=setTimeout(y,e),u?h(I):a}function f(I){var H=I-l,R=I-c,k=e-H;return d?Sp(k,o-R):k}function w(I){var H=I-l,R=I-c;return l===void 0||H>=e||H<0||d&&R>=o}function y(){var I=Zi();if(w(I))return v(I);s=setTimeout(y,f(I))}function v(I){return s=void 0,p&&n?h(I):(n=i=void 0,a)}function _(){s!==void 0&&clearTimeout(s),c=0,n=l=i=s=void 0}function S(){return s===void 0?a:v(Zi())}function E(){var I=Zi(),H=w(I);if(n=arguments,i=this,l=I,H){if(s===void 0)return b(l);if(d)return s=setTimeout(y,e),h(l)}return s===void 0&&(s=setTimeout(y,e)),a}return E.cancel=_,E.flush=S,E}function xo(t){var e=typeof t;return!!t&&(e=="object"||e=="function")}function Ep(t){return!!t&&typeof t=="object"}function Op(t){return typeof t=="symbol"||Ep(t)&&xp.call(t)==pp}function Ya(t){if(typeof t=="number")return t;if(Op(t))return Za;if(xo(t)){var e=typeof t.valueOf=="function"?t.valueOf():t;t=xo(e)?e+"":e}if(typeof t!="string")return t===0?t:+t;t=t.replace(hp,"");var r=mp.test(t);return r||gp.test(t)?vp(t.slice(2),r?2:8):fp.test(t)?Za:+t}var Np=Cp,Ol={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/(function(t){(function(){var e={}.hasOwnProperty;function r(){for(var o="",a=0;a<arguments.length;a++){var s=arguments[a];s&&(o=i(o,n(s)))}return o}function n(o){if(typeof o=="string"||typeof o=="number")return o;if(typeof o!="object")return"";if(Array.isArray(o))return r.apply(null,o);if(o.toString!==Object.prototype.toString&&!o.toString.toString().includes("[native code]"))return o.toString();var a="";for(var s in o)e.call(o,s)&&o[s]&&(a=i(a,s));return a}function i(o,a){return a?o?o+" "+a:o+a:o}t.exports?(r.default=r,t.exports=r):window.classNames=r})()})(Ol);var Mi=Ol.exports,M={},ca={};(function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e=r(q);function r(i){return i&&i.__esModule?i:{default:i}}var n={accessibility:!0,adaptiveHeight:!1,afterChange:null,appendDots:function(o){return e.default.createElement("ul",{style:{display:"block"}},o)},arrows:!0,autoplay:!1,autoplaySpeed:3e3,beforeChange:null,centerMode:!1,centerPadding:"50px",className:"",cssEase:"ease",customPaging:function(o){return e.default.createElement("button",null,o+1)},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",edgeFriction:.35,fade:!1,focusOnSelect:!1,infinite:!0,initialSlide:0,lazyLoad:null,nextArrow:null,onEdge:null,onInit:null,onLazyLoadError:null,onReInit:null,pauseOnDotsHover:!1,pauseOnFocus:!1,pauseOnHover:!0,prevArrow:null,responsive:null,rows:1,rtl:!1,slide:"div",slidesPerRow:1,slidesToScroll:1,slidesToShow:1,speed:500,swipe:!0,swipeEvent:null,swipeToSlide:!1,touchMove:!0,touchThreshold:5,useCSS:!0,useTransform:!0,variableWidth:!1,vertical:!1,waitForAnimate:!0,asNavFor:null,unslick:!1};t.default=n})(ca);Object.defineProperty(M,"__esModule",{value:!0});M.checkSpecKeys=M.checkNavigable=M.changeSlide=M.canUseDOM=M.canGoNext=void 0;M.clamp=Pl;M.extractObject=void 0;M.filterSettings=Wp;M.validSettings=M.swipeStart=M.swipeMove=M.swipeEnd=M.slidesOnRight=M.slidesOnLeft=M.slideHandler=M.siblingDirection=M.safePreventDefault=M.lazyStartIndex=M.lazySlidesOnRight=M.lazySlidesOnLeft=M.lazyEndIndex=M.keyHandler=M.initializedState=M.getWidth=M.getTrackLeft=M.getTrackCSS=M.getTrackAnimateCSS=M.getTotalSlides=M.getSwipeDirection=M.getSlideCount=M.getRequiredLazySlides=M.getPreClones=M.getPostClones=M.getOnDemandLazySlides=M.getNavigableIndexes=M.getHeight=void 0;var Pp=Nl(q),Tp=Nl(ca);function Nl(t){return t&&t.__esModule?t:{default:t}}function Qr(t){"@babel/helpers - typeof";return Qr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Qr(t)}function Ka(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Se(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?Ka(Object(r),!0).forEach(function(n){Mp(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ka(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Mp(t,e,r){return e=Ap(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ap(t){var e=Rp(t,"string");return Qr(e)=="symbol"?e:String(e)}function Rp(t,e){if(Qr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(Qr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Pl(t,e,r){return Math.max(e,Math.min(t,r))}var ur=M.safePreventDefault=function(e){var r=["onTouchStart","onTouchMove","onWheel"];r.includes(e._reactName)||e.preventDefault()},Tl=M.getOnDemandLazySlides=function(e){for(var r=[],n=Ml(e),i=Al(e),o=n;o<i;o++)e.lazyLoadedList.indexOf(o)<0&&r.push(o);return r};M.getRequiredLazySlides=function(e){for(var r=[],n=Ml(e),i=Al(e),o=n;o<i;o++)r.push(o);return r};var Ml=M.lazyStartIndex=function(e){return e.currentSlide-Ip(e)},Al=M.lazyEndIndex=function(e){return e.currentSlide+$p(e)},Ip=M.lazySlidesOnLeft=function(e){return e.centerMode?Math.floor(e.slidesToShow/2)+(parseInt(e.centerPadding)>0?1:0):0},$p=M.lazySlidesOnRight=function(e){return e.centerMode?Math.floor((e.slidesToShow-1)/2)+1+(parseInt(e.centerPadding)>0?1:0):e.slidesToShow},ko=M.getWidth=function(e){return e&&e.offsetWidth||0},Rl=M.getHeight=function(e){return e&&e.offsetHeight||0},Il=M.getSwipeDirection=function(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n,i,o,a;return n=e.startX-e.curX,i=e.startY-e.curY,o=Math.atan2(i,n),a=Math.round(o*180/Math.PI),a<0&&(a=360-Math.abs(a)),a<=45&&a>=0||a<=360&&a>=315?"left":a>=135&&a<=225?"right":r===!0?a>=35&&a<=135?"up":"down":"vertical"},$l=M.canGoNext=function(e){var r=!0;return e.infinite||(e.centerMode&&e.currentSlide>=e.slideCount-1||e.slideCount<=e.slidesToShow||e.currentSlide>=e.slideCount-e.slidesToShow)&&(r=!1),r};M.extractObject=function(e,r){var n={};return r.forEach(function(i){return n[i]=e[i]}),n};M.initializedState=function(e){var r=Pp.default.Children.count(e.children),n=e.listRef,i=Math.ceil(ko(n)),o=e.trackRef&&e.trackRef.node,a=Math.ceil(ko(o)),s;if(e.vertical)s=i;else{var l=e.centerMode&&parseInt(e.centerPadding)*2;typeof e.centerPadding=="string"&&e.centerPadding.slice(-1)==="%"&&(l*=i/100),s=Math.ceil((i-l)/e.slidesToShow)}var c=n&&Rl(n.querySelector('[data-index="0"]')),u=c*e.slidesToShow,d=e.currentSlide===void 0?e.initialSlide:e.currentSlide;e.rtl&&e.currentSlide===void 0&&(d=r-1-e.initialSlide);var p=e.lazyLoadedList||[],h=Tl(Se(Se({},e),{},{currentSlide:d,lazyLoadedList:p}));p=p.concat(h);var b={slideCount:r,slideWidth:s,listWidth:i,trackWidth:a,currentSlide:d,slideHeight:c,listHeight:u,lazyLoadedList:p};return e.autoplaying===null&&e.autoplay&&(b.autoplaying="playing"),b};M.slideHandler=function(e){var r=e.waitForAnimate,n=e.animating,i=e.fade,o=e.infinite,a=e.index,s=e.slideCount,l=e.lazyLoad,c=e.currentSlide,u=e.centerMode,d=e.slidesToScroll,p=e.slidesToShow,h=e.useCSS,b=e.lazyLoadedList;if(r&&n)return{};var f=a,w,y,v,_={},S={},E=o?a:Pl(a,0,s-1);if(i){if(!o&&(a<0||a>=s))return{};a<0?f=a+s:a>=s&&(f=a-s),l&&b.indexOf(f)<0&&(b=b.concat(f)),_={animating:!0,currentSlide:f,lazyLoadedList:b,targetSlide:f},S={animating:!1,targetSlide:f}}else w=f,f<0?(w=f+s,o?s%d!==0&&(w=s-s%d):w=0):!$l(e)&&f>c?f=w=c:u&&f>=s?(f=o?s:s-1,w=o?0:s-1):f>=s&&(w=f-s,o?s%d!==0&&(w=0):w=s-p),!o&&f+p>=s&&(w=s-p),y=ti(Se(Se({},e),{},{slideIndex:f})),v=ti(Se(Se({},e),{},{slideIndex:w})),o||(y===v&&(f=w),y=v),l&&(b=b.concat(Tl(Se(Se({},e),{},{currentSlide:f})))),h?(_={animating:!0,currentSlide:w,trackStyle:zl(Se(Se({},e),{},{left:y})),lazyLoadedList:b,targetSlide:E},S={animating:!1,currentSlide:w,trackStyle:ei(Se(Se({},e),{},{left:v})),swipeLeft:null,targetSlide:E}):_={currentSlide:w,trackStyle:ei(Se(Se({},e),{},{left:v})),lazyLoadedList:b,targetSlide:E};return{state:_,nextState:S}};M.changeSlide=function(e,r){var n,i,o,a,s,l=e.slidesToScroll,c=e.slidesToShow,u=e.slideCount,d=e.currentSlide,p=e.targetSlide,h=e.lazyLoad,b=e.infinite;if(a=u%l!==0,n=a?0:(u-d)%l,r.message==="previous")o=n===0?l:c-n,s=d-o,h&&!b&&(i=d-o,s=i===-1?u-1:i),b||(s=p-l);else if(r.message==="next")o=n===0?l:n,s=d+o,h&&!b&&(s=(d+l)%u+n),b||(s=p+l);else if(r.message==="dots")s=r.index*r.slidesToScroll;else if(r.message==="children"){if(s=r.index,b){var f=Dp(Se(Se({},e),{},{targetSlide:s}));s>r.currentSlide&&f==="left"?s=s-u:s<r.currentSlide&&f==="right"&&(s=s+u)}}else r.message==="index"&&(s=Number(r.index));return s};M.keyHandler=function(e,r,n){return e.target.tagName.match("TEXTAREA|INPUT|SELECT")||!r?"":e.keyCode===37?n?"next":"previous":e.keyCode===39?n?"previous":"next":""};M.swipeStart=function(e,r,n){return e.target.tagName==="IMG"&&ur(e),!r||!n&&e.type.indexOf("mouse")!==-1?"":{dragging:!0,touchObject:{startX:e.touches?e.touches[0].pageX:e.clientX,startY:e.touches?e.touches[0].pageY:e.clientY,curX:e.touches?e.touches[0].pageX:e.clientX,curY:e.touches?e.touches[0].pageY:e.clientY}}};M.swipeMove=function(e,r){var n=r.scrolling,i=r.animating,o=r.vertical,a=r.swipeToSlide,s=r.verticalSwiping,l=r.rtl,c=r.currentSlide,u=r.edgeFriction,d=r.edgeDragged,p=r.onEdge,h=r.swiped,b=r.swiping,f=r.slideCount,w=r.slidesToScroll,y=r.infinite,v=r.touchObject,_=r.swipeEvent,S=r.listHeight,E=r.listWidth;if(!n){if(i)return ur(e);o&&a&&s&&ur(e);var I,H={},R=ti(r);v.curX=e.touches?e.touches[0].pageX:e.clientX,v.curY=e.touches?e.touches[0].pageY:e.clientY,v.swipeLength=Math.round(Math.sqrt(Math.pow(v.curX-v.startX,2)));var k=Math.round(Math.sqrt(Math.pow(v.curY-v.startY,2)));if(!s&&!b&&k>10)return{scrolling:!0};s&&(v.swipeLength=k);var O=(l?-1:1)*(v.curX>v.startX?1:-1);s&&(O=v.curY>v.startY?1:-1);var $=Math.ceil(f/w),j=Il(r.touchObject,s),N=v.swipeLength;return y||(c===0&&(j==="right"||j==="down")||c+1>=$&&(j==="left"||j==="up")||!$l(r)&&(j==="left"||j==="up"))&&(N=v.swipeLength*u,d===!1&&p&&(p(j),H.edgeDragged=!0)),!h&&_&&(_(j),H.swiped=!0),o?I=R+N*(S/E)*O:l?I=R-N*O:I=R+N*O,s&&(I=R+N*O),H=Se(Se({},H),{},{touchObject:v,swipeLeft:I,trackStyle:ei(Se(Se({},r),{},{left:I}))}),Math.abs(v.curX-v.startX)<Math.abs(v.curY-v.startY)*.8||v.swipeLength>10&&(H.swiping=!0,ur(e)),H}};M.swipeEnd=function(e,r){var n=r.dragging,i=r.swipe,o=r.touchObject,a=r.listWidth,s=r.touchThreshold,l=r.verticalSwiping,c=r.listHeight,u=r.swipeToSlide,d=r.scrolling,p=r.onSwipe,h=r.targetSlide,b=r.currentSlide,f=r.infinite;if(!n)return i&&ur(e),{};var w=l?c/s:a/s,y=Il(o,l),v={dragging:!1,edgeDragged:!1,scrolling:!1,swiping:!1,swiped:!1,swipeLeft:null,touchObject:{}};if(d||!o.swipeLength)return v;if(o.swipeLength>w){ur(e),p&&p(y);var _,S,E=f?b:h;switch(y){case"left":case"up":S=E+Xa(r),_=u?Qa(r,S):S,v.currentDirection=0;break;case"right":case"down":S=E-Xa(r),_=u?Qa(r,S):S,v.currentDirection=1;break;default:_=E}v.triggerSlideHandler=_}else{var I=ti(r);v.trackStyle=zl(Se(Se({},r),{},{left:I}))}return v};var zp=M.getNavigableIndexes=function(e){for(var r=e.infinite?e.slideCount*2:e.slideCount,n=e.infinite?e.slidesToShow*-1:0,i=e.infinite?e.slidesToShow*-1:0,o=[];n<r;)o.push(n),n=i+e.slidesToScroll,i+=Math.min(e.slidesToScroll,e.slidesToShow);return o},Qa=M.checkNavigable=function(e,r){var n=zp(e),i=0;if(r>n[n.length-1])r=n[n.length-1];else for(var o in n){if(r<n[o]){r=i;break}i=n[o]}return r},Xa=M.getSlideCount=function(e){var r=e.centerMode?e.slideWidth*Math.floor(e.slidesToShow/2):0;if(e.swipeToSlide){var n,i=e.listRef,o=i.querySelectorAll&&i.querySelectorAll(".slick-slide")||[];if(Array.from(o).every(function(l){if(e.vertical){if(l.offsetTop+Rl(l)/2>e.swipeLeft*-1)return n=l,!1}else if(l.offsetLeft-r+ko(l)/2>e.swipeLeft*-1)return n=l,!1;return!0}),!n)return 0;var a=e.rtl===!0?e.slideCount-e.currentSlide:e.currentSlide,s=Math.abs(n.dataset.index-a)||1;return s}else return e.slidesToScroll},da=M.checkSpecKeys=function(e,r){return r.reduce(function(n,i){return n&&e.hasOwnProperty(i)},!0)?null:console.error("Keys Missing:",e)},ei=M.getTrackCSS=function(e){da(e,["left","variableWidth","slideCount","slidesToShow","slideWidth"]);var r,n,i=e.slideCount+2*e.slidesToShow;e.vertical?n=i*e.slideHeight:r=Lp(e)*e.slideWidth;var o={opacity:1,transition:"",WebkitTransition:""};if(e.useTransform){var a=e.vertical?"translate3d(0px, "+e.left+"px, 0px)":"translate3d("+e.left+"px, 0px, 0px)",s=e.vertical?"translate3d(0px, "+e.left+"px, 0px)":"translate3d("+e.left+"px, 0px, 0px)",l=e.vertical?"translateY("+e.left+"px)":"translateX("+e.left+"px)";o=Se(Se({},o),{},{WebkitTransform:a,transform:s,msTransform:l})}else e.vertical?o.top=e.left:o.left=e.left;return e.fade&&(o={opacity:1}),r&&(o.width=r),n&&(o.height=n),window&&!window.addEventListener&&window.attachEvent&&(e.vertical?o.marginTop=e.left+"px":o.marginLeft=e.left+"px"),o},zl=M.getTrackAnimateCSS=function(e){da(e,["left","variableWidth","slideCount","slidesToShow","slideWidth","speed","cssEase"]);var r=ei(e);return e.useTransform?(r.WebkitTransition="-webkit-transform "+e.speed+"ms "+e.cssEase,r.transition="transform "+e.speed+"ms "+e.cssEase):e.vertical?r.transition="top "+e.speed+"ms "+e.cssEase:r.transition="left "+e.speed+"ms "+e.cssEase,r},ti=M.getTrackLeft=function(e){if(e.unslick)return 0;da(e,["slideIndex","trackRef","infinite","centerMode","slideCount","slidesToShow","slidesToScroll","slideWidth","listWidth","variableWidth","slideHeight"]);var r=e.slideIndex,n=e.trackRef,i=e.infinite,o=e.centerMode,a=e.slideCount,s=e.slidesToShow,l=e.slidesToScroll,c=e.slideWidth,u=e.listWidth,d=e.variableWidth,p=e.slideHeight,h=e.fade,b=e.vertical,f=0,w,y,v=0;if(h||e.slideCount===1)return 0;var _=0;if(i?(_=-Yn(e),a%l!==0&&r+l>a&&(_=-(r>a?s-(r-a):a%l)),o&&(_+=parseInt(s/2))):(a%l!==0&&r+l>a&&(_=s-a%l),o&&(_=parseInt(s/2))),f=_*c,v=_*p,b?w=r*p*-1+v:w=r*c*-1+f,d===!0){var S,E=n&&n.node;if(S=r+Yn(e),y=E&&E.childNodes[S],w=y?y.offsetLeft*-1:0,o===!0){S=i?r+Yn(e):r,y=E&&E.children[S],w=0;for(var I=0;I<S;I++)w-=E&&E.children[I]&&E.children[I].offsetWidth;w-=parseInt(e.centerPadding),w+=y&&(u-y.offsetWidth)/2}}return w},Yn=M.getPreClones=function(e){return e.unslick||!e.infinite?0:e.variableWidth?e.slideCount:e.slidesToShow+(e.centerMode?1:0)},jp=M.getPostClones=function(e){return e.unslick||!e.infinite?0:e.slideCount},Lp=M.getTotalSlides=function(e){return e.slideCount===1?1:Yn(e)+e.slideCount+jp(e)},Dp=M.siblingDirection=function(e){return e.targetSlide>e.currentSlide?e.targetSlide>e.currentSlide+Fp(e)?"left":"right":e.targetSlide<e.currentSlide-Hp(e)?"right":"left"},Fp=M.slidesOnRight=function(e){var r=e.slidesToShow,n=e.centerMode,i=e.rtl,o=e.centerPadding;if(n){var a=(r-1)/2+1;return parseInt(o)>0&&(a+=1),i&&r%2===0&&(a+=1),a}return i?0:r-1},Hp=M.slidesOnLeft=function(e){var r=e.slidesToShow,n=e.centerMode,i=e.rtl,o=e.centerPadding;if(n){var a=(r-1)/2+1;return parseInt(o)>0&&(a+=1),!i&&r%2===0&&(a+=1),a}return i?r-1:0};M.canUseDOM=function(){return!!(typeof window<"u"&&window.document&&window.document.createElement)};var Bp=M.validSettings=Object.keys(Tp.default);function Wp(t){return Bp.reduce(function(e,r){return t.hasOwnProperty(r)&&(e[r]=t[r]),e},{})}var Ai={};Object.defineProperty(Ai,"__esModule",{value:!0});Ai.Track=void 0;var Rt=jl(q),Yi=jl(Mi),Ki=M;function jl(t){return t&&t.__esModule?t:{default:t}}function vr(t){"@babel/helpers - typeof";return vr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vr(t)}function So(){return So=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},So.apply(this,arguments)}function Vp(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Ja(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Dl(n.key),n)}}function qp(t,e,r){return e&&Ja(t.prototype,e),r&&Ja(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Up(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Co(t,e)}function Co(t,e){return Co=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Co(t,e)}function Gp(t){var e=Ll();return function(){var n=ri(t),i;if(e){var o=ri(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return Zp(this,i)}}function Zp(t,e){if(e&&(vr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return Eo(t)}function Eo(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Ll(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Ll=function(){return!!t})()}function ri(t){return ri=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ri(t)}function es(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function We(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?es(Object(r),!0).forEach(function(n){Oo(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):es(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Oo(t,e,r){return e=Dl(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Dl(t){var e=Yp(t,"string");return vr(e)=="symbol"?e:String(e)}function Yp(t,e){if(vr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(vr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Qi=function(e){var r,n,i,o,a;e.rtl?a=e.slideCount-1-e.index:a=e.index,i=a<0||a>=e.slideCount,e.centerMode?(o=Math.floor(e.slidesToShow/2),n=(a-e.currentSlide)%e.slideCount===0,a>e.currentSlide-o-1&&a<=e.currentSlide+o&&(r=!0)):r=e.currentSlide<=a&&a<e.currentSlide+e.slidesToShow;var s;e.targetSlide<0?s=e.targetSlide+e.slideCount:e.targetSlide>=e.slideCount?s=e.targetSlide-e.slideCount:s=e.targetSlide;var l=a===s;return{"slick-slide":!0,"slick-active":r,"slick-center":n,"slick-cloned":i,"slick-current":l}},Kp=function(e){var r={};return(e.variableWidth===void 0||e.variableWidth===!1)&&(r.width=e.slideWidth),e.fade&&(r.position="relative",e.vertical?r.top=-e.index*parseInt(e.slideHeight):r.left=-e.index*parseInt(e.slideWidth),r.opacity=e.currentSlide===e.index?1:0,r.zIndex=e.currentSlide===e.index?999:998,e.useCSS&&(r.transition="opacity "+e.speed+"ms "+e.cssEase+", visibility "+e.speed+"ms "+e.cssEase)),r},Xi=function(e,r){return e.key||r},Qp=function(e){var r,n=[],i=[],o=[],a=Rt.default.Children.count(e.children),s=(0,Ki.lazyStartIndex)(e),l=(0,Ki.lazyEndIndex)(e);return Rt.default.Children.forEach(e.children,function(c,u){var d,p={message:"children",index:u,slidesToScroll:e.slidesToScroll,currentSlide:e.currentSlide};!e.lazyLoad||e.lazyLoad&&e.lazyLoadedList.indexOf(u)>=0?d=c:d=Rt.default.createElement("div",null);var h=Kp(We(We({},e),{},{index:u})),b=d.props.className||"",f=Qi(We(We({},e),{},{index:u}));if(n.push(Rt.default.cloneElement(d,{key:"original"+Xi(d,u),"data-index":u,className:(0,Yi.default)(f,b),tabIndex:"-1","aria-hidden":!f["slick-active"],style:We(We({outline:"none"},d.props.style||{}),h),onClick:function(v){d.props&&d.props.onClick&&d.props.onClick(v),e.focusOnSelect&&e.focusOnSelect(p)}})),e.infinite&&e.fade===!1){var w=a-u;w<=(0,Ki.getPreClones)(e)&&(r=-w,r>=s&&(d=c),f=Qi(We(We({},e),{},{index:r})),i.push(Rt.default.cloneElement(d,{key:"precloned"+Xi(d,r),"data-index":r,tabIndex:"-1",className:(0,Yi.default)(f,b),"aria-hidden":!f["slick-active"],style:We(We({},d.props.style||{}),h),onClick:function(v){d.props&&d.props.onClick&&d.props.onClick(v),e.focusOnSelect&&e.focusOnSelect(p)}}))),r=a+u,r<l&&(d=c),f=Qi(We(We({},e),{},{index:r})),o.push(Rt.default.cloneElement(d,{key:"postcloned"+Xi(d,r),"data-index":r,tabIndex:"-1",className:(0,Yi.default)(f,b),"aria-hidden":!f["slick-active"],style:We(We({},d.props.style||{}),h),onClick:function(v){d.props&&d.props.onClick&&d.props.onClick(v),e.focusOnSelect&&e.focusOnSelect(p)}}))}}),e.rtl?i.concat(n,o).reverse():i.concat(n,o)};Ai.Track=function(t){Up(r,t);var e=Gp(r);function r(){var n;Vp(this,r);for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];return n=e.call.apply(e,[this].concat(o)),Oo(Eo(n),"node",null),Oo(Eo(n),"handleRef",function(s){n.node=s}),n}return qp(r,[{key:"render",value:function(){var i=Qp(this.props),o=this.props,a=o.onMouseEnter,s=o.onMouseOver,l=o.onMouseLeave,c={onMouseEnter:a,onMouseOver:s,onMouseLeave:l};return Rt.default.createElement("div",So({ref:this.handleRef,className:"slick-track",style:this.props.trackStyle},c),i)}}]),r}(Rt.default.PureComponent);var Ri={};function br(t){"@babel/helpers - typeof";return br=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},br(t)}Object.defineProperty(Ri,"__esModule",{value:!0});Ri.Dots=void 0;var Ln=Fl(q),Xp=Fl(Mi),ts=M;function Fl(t){return t&&t.__esModule?t:{default:t}}function rs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Jp(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?rs(Object(r),!0).forEach(function(n){eh(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rs(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function eh(t,e,r){return e=Hl(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function th(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ns(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Hl(n.key),n)}}function rh(t,e,r){return e&&ns(t.prototype,e),r&&ns(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Hl(t){var e=nh(t,"string");return br(e)=="symbol"?e:String(e)}function nh(t,e){if(br(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(br(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ih(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&No(t,e)}function No(t,e){return No=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},No(t,e)}function oh(t){var e=Bl();return function(){var n=ni(t),i;if(e){var o=ni(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return ah(this,i)}}function ah(t,e){if(e&&(br(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return sh(t)}function sh(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Bl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Bl=function(){return!!t})()}function ni(t){return ni=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ni(t)}var lh=function(e){var r;return e.infinite?r=Math.ceil(e.slideCount/e.slidesToScroll):r=Math.ceil((e.slideCount-e.slidesToShow)/e.slidesToScroll)+1,r};Ri.Dots=function(t){ih(r,t);var e=oh(r);function r(){return th(this,r),e.apply(this,arguments)}return rh(r,[{key:"clickHandler",value:function(i,o){o.preventDefault(),this.props.clickHandler(i)}},{key:"render",value:function(){for(var i=this.props,o=i.onMouseEnter,a=i.onMouseOver,s=i.onMouseLeave,l=i.infinite,c=i.slidesToScroll,u=i.slidesToShow,d=i.slideCount,p=i.currentSlide,h=lh({slideCount:d,slidesToScroll:c,slidesToShow:u,infinite:l}),b={onMouseEnter:o,onMouseOver:a,onMouseLeave:s},f=[],w=0;w<h;w++){var y=(w+1)*c-1,v=l?y:(0,ts.clamp)(y,0,d-1),_=v-(c-1),S=l?_:(0,ts.clamp)(_,0,d-1),E=(0,Xp.default)({"slick-active":l?p>=S&&p<=v:p===S}),I={message:"dots",index:w,slidesToScroll:c,currentSlide:p},H=this.clickHandler.bind(this,I);f=f.concat(Ln.default.createElement("li",{key:w,className:E},Ln.default.cloneElement(this.props.customPaging(w),{onClick:H})))}return Ln.default.cloneElement(this.props.appendDots(f),Jp({className:this.props.dotsClass},b))}}]),r}(Ln.default.PureComponent);var wr={};function yr(t){"@babel/helpers - typeof";return yr=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},yr(t)}Object.defineProperty(wr,"__esModule",{value:!0});wr.PrevArrow=wr.NextArrow=void 0;var pr=Vl(q),Wl=Vl(Mi),ch=M;function Vl(t){return t&&t.__esModule?t:{default:t}}function ii(){return ii=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ii.apply(this,arguments)}function is(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function oi(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?is(Object(r),!0).forEach(function(n){dh(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):is(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function dh(t,e,r){return e=Gl(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ql(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function os(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Gl(n.key),n)}}function Ul(t,e,r){return e&&os(t.prototype,e),r&&os(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Gl(t){var e=uh(t,"string");return yr(e)=="symbol"?e:String(e)}function uh(t,e){if(yr(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(yr(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Zl(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Po(t,e)}function Po(t,e){return Po=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Po(t,e)}function Yl(t){var e=Kl();return function(){var n=ai(t),i;if(e){var o=ai(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return ph(this,i)}}function ph(t,e){if(e&&(yr(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return hh(t)}function hh(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Kl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Kl=function(){return!!t})()}function ai(t){return ai=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},ai(t)}wr.PrevArrow=function(t){Zl(r,t);var e=Yl(r);function r(){return ql(this,r),e.apply(this,arguments)}return Ul(r,[{key:"clickHandler",value:function(i,o){o&&o.preventDefault(),this.props.clickHandler(i,o)}},{key:"render",value:function(){var i={"slick-arrow":!0,"slick-prev":!0},o=this.clickHandler.bind(this,{message:"previous"});!this.props.infinite&&(this.props.currentSlide===0||this.props.slideCount<=this.props.slidesToShow)&&(i["slick-disabled"]=!0,o=null);var a={key:"0","data-role":"none",className:(0,Wl.default)(i),style:{display:"block"},onClick:o},s={currentSlide:this.props.currentSlide,slideCount:this.props.slideCount},l;return this.props.prevArrow?l=pr.default.cloneElement(this.props.prevArrow,oi(oi({},a),s)):l=pr.default.createElement("button",ii({key:"0",type:"button"},a)," ","Previous"),l}}]),r}(pr.default.PureComponent);wr.NextArrow=function(t){Zl(r,t);var e=Yl(r);function r(){return ql(this,r),e.apply(this,arguments)}return Ul(r,[{key:"clickHandler",value:function(i,o){o&&o.preventDefault(),this.props.clickHandler(i,o)}},{key:"render",value:function(){var i={"slick-arrow":!0,"slick-next":!0},o=this.clickHandler.bind(this,{message:"next"});(0,ch.canGoNext)(this.props)||(i["slick-disabled"]=!0,o=null);var a={key:"1","data-role":"none",className:(0,Wl.default)(i),style:{display:"block"},onClick:o},s={currentSlide:this.props.currentSlide,slideCount:this.props.slideCount},l;return this.props.nextArrow?l=pr.default.cloneElement(this.props.nextArrow,oi(oi({},a),s)):l=pr.default.createElement("button",ii({key:"1",type:"button"},a)," ","Next"),l}}]),r}(pr.default.PureComponent);var Ql=function(){if(typeof Map<"u")return Map;function t(e,r){var n=-1;return e.some(function(i,o){return i[0]===r?(n=o,!0):!1}),n}return function(){function e(){this.__entries__=[]}return Object.defineProperty(e.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),e.prototype.get=function(r){var n=t(this.__entries__,r),i=this.__entries__[n];return i&&i[1]},e.prototype.set=function(r,n){var i=t(this.__entries__,r);~i?this.__entries__[i][1]=n:this.__entries__.push([r,n])},e.prototype.delete=function(r){var n=this.__entries__,i=t(n,r);~i&&n.splice(i,1)},e.prototype.has=function(r){return!!~t(this.__entries__,r)},e.prototype.clear=function(){this.__entries__.splice(0)},e.prototype.forEach=function(r,n){n===void 0&&(n=null);for(var i=0,o=this.__entries__;i<o.length;i++){var a=o[i];r.call(n,a[1],a[0])}},e}()}(),To=typeof window<"u"&&typeof document<"u"&&window.document===document,si=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),fh=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(si):function(t){return setTimeout(function(){return t(Date.now())},1e3/60)}}(),mh=2;function gh(t,e){var r=!1,n=!1,i=0;function o(){r&&(r=!1,t()),n&&s()}function a(){fh(o)}function s(){var l=Date.now();if(r){if(l-i<mh)return;n=!0}else r=!0,n=!1,setTimeout(a,e);i=l}return s}var vh=20,bh=["top","right","bottom","left","width","height","size","weight"],wh=typeof MutationObserver<"u",yh=function(){function t(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=gh(this.refresh.bind(this),vh)}return t.prototype.addObserver=function(e){~this.observers_.indexOf(e)||this.observers_.push(e),this.connected_||this.connect_()},t.prototype.removeObserver=function(e){var r=this.observers_,n=r.indexOf(e);~n&&r.splice(n,1),!r.length&&this.connected_&&this.disconnect_()},t.prototype.refresh=function(){var e=this.updateObservers_();e&&this.refresh()},t.prototype.updateObservers_=function(){var e=this.observers_.filter(function(r){return r.gatherActive(),r.hasActive()});return e.forEach(function(r){return r.broadcastActive()}),e.length>0},t.prototype.connect_=function(){!To||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),wh?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},t.prototype.disconnect_=function(){!To||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},t.prototype.onTransitionEnd_=function(e){var r=e.propertyName,n=r===void 0?"":r,i=bh.some(function(o){return!!~n.indexOf(o)});i&&this.refresh()},t.getInstance=function(){return this.instance_||(this.instance_=new t),this.instance_},t.instance_=null,t}(),Xl=function(t,e){for(var r=0,n=Object.keys(e);r<n.length;r++){var i=n[r];Object.defineProperty(t,i,{value:e[i],enumerable:!1,writable:!1,configurable:!0})}return t},_r=function(t){var e=t&&t.ownerDocument&&t.ownerDocument.defaultView;return e||si},Jl=Ii(0,0,0,0);function li(t){return parseFloat(t)||0}function as(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];return e.reduce(function(n,i){var o=t["border-"+i+"-width"];return n+li(o)},0)}function _h(t){for(var e=["top","right","bottom","left"],r={},n=0,i=e;n<i.length;n++){var o=i[n],a=t["padding-"+o];r[o]=li(a)}return r}function xh(t){var e=t.getBBox();return Ii(0,0,e.width,e.height)}function kh(t){var e=t.clientWidth,r=t.clientHeight;if(!e&&!r)return Jl;var n=_r(t).getComputedStyle(t),i=_h(n),o=i.left+i.right,a=i.top+i.bottom,s=li(n.width),l=li(n.height);if(n.boxSizing==="border-box"&&(Math.round(s+o)!==e&&(s-=as(n,"left","right")+o),Math.round(l+a)!==r&&(l-=as(n,"top","bottom")+a)),!Ch(t)){var c=Math.round(s+o)-e,u=Math.round(l+a)-r;Math.abs(c)!==1&&(s-=c),Math.abs(u)!==1&&(l-=u)}return Ii(i.left,i.top,s,l)}var Sh=function(){return typeof SVGGraphicsElement<"u"?function(t){return t instanceof _r(t).SVGGraphicsElement}:function(t){return t instanceof _r(t).SVGElement&&typeof t.getBBox=="function"}}();function Ch(t){return t===_r(t).document.documentElement}function Eh(t){return To?Sh(t)?xh(t):kh(t):Jl}function Oh(t){var e=t.x,r=t.y,n=t.width,i=t.height,o=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,a=Object.create(o.prototype);return Xl(a,{x:e,y:r,width:n,height:i,top:r,right:e+n,bottom:i+r,left:e}),a}function Ii(t,e,r,n){return{x:t,y:e,width:r,height:n}}var Nh=function(){function t(e){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Ii(0,0,0,0),this.target=e}return t.prototype.isActive=function(){var e=Eh(this.target);return this.contentRect_=e,e.width!==this.broadcastWidth||e.height!==this.broadcastHeight},t.prototype.broadcastRect=function(){var e=this.contentRect_;return this.broadcastWidth=e.width,this.broadcastHeight=e.height,e},t}(),Ph=function(){function t(e,r){var n=Oh(r);Xl(this,{target:e,contentRect:n})}return t}(),Th=function(){function t(e,r,n){if(this.activeObservations_=[],this.observations_=new Ql,typeof e!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=e,this.controller_=r,this.callbackCtx_=n}return t.prototype.observe=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(e instanceof _r(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var r=this.observations_;r.has(e)||(r.set(e,new Nh(e)),this.controller_.addObserver(this),this.controller_.refresh())}},t.prototype.unobserve=function(e){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(e instanceof _r(e).Element))throw new TypeError('parameter 1 is not of type "Element".');var r=this.observations_;r.has(e)&&(r.delete(e),r.size||this.controller_.removeObserver(this))}},t.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},t.prototype.gatherActive=function(){var e=this;this.clearActive(),this.observations_.forEach(function(r){r.isActive()&&e.activeObservations_.push(r)})},t.prototype.broadcastActive=function(){if(this.hasActive()){var e=this.callbackCtx_,r=this.activeObservations_.map(function(n){return new Ph(n.target,n.broadcastRect())});this.callback_.call(e,r,e),this.clearActive()}},t.prototype.clearActive=function(){this.activeObservations_.splice(0)},t.prototype.hasActive=function(){return this.activeObservations_.length>0},t}(),ec=typeof WeakMap<"u"?new WeakMap:new Ql,tc=function(){function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var r=yh.getInstance(),n=new Th(e,r,this);ec.set(this,n)}return t}();["observe","unobserve","disconnect"].forEach(function(t){tc.prototype[t]=function(){var e;return(e=ec.get(this))[t].apply(e,arguments)}});var Mh=function(){return typeof si.ResizeObserver<"u"?si.ResizeObserver:tc}();const Ah=Object.freeze(Object.defineProperty({__proto__:null,default:Mh},Symbol.toStringTag,{value:"Module"})),Rh=_u(Ah);Object.defineProperty(Ti,"__esModule",{value:!0});Ti.InnerSlider=void 0;var De=kn(q),Ih=kn(El),$h=kn(Np),zh=kn(Mi),Ce=M,jh=Ai,Lh=Ri,ss=wr,Dh=kn(Rh);function kn(t){return t&&t.__esModule?t:{default:t}}function er(t){"@babel/helpers - typeof";return er=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},er(t)}function ci(){return ci=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},ci.apply(this,arguments)}function Fh(t,e){if(t==null)return{};var r=Hh(t,e),n,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(i=0;i<o.length;i++)n=o[i],!(e.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(t,n)&&(r[n]=t[n])}return r}function Hh(t,e){if(t==null)return{};var r={},n=Object.keys(t),i,o;for(o=0;o<n.length;o++)i=n[o],!(e.indexOf(i)>=0)&&(r[i]=t[i]);return r}function ls(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function Y(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?ls(Object(r),!0).forEach(function(n){ae(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ls(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function Bh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function cs(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nc(n.key),n)}}function Wh(t,e,r){return e&&cs(t.prototype,e),r&&cs(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function Vh(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Mo(t,e)}function Mo(t,e){return Mo=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Mo(t,e)}function qh(t){var e=rc();return function(){var n=di(t),i;if(e){var o=di(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return Uh(this,i)}}function Uh(t,e){if(e&&(er(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return oe(t)}function oe(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function rc(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(rc=function(){return!!t})()}function di(t){return di=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(r){return r.__proto__||Object.getPrototypeOf(r)},di(t)}function ae(t,e,r){return e=nc(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nc(t){var e=Gh(t,"string");return er(e)=="symbol"?e:String(e)}function Gh(t,e){if(er(t)!="object"||!t)return t;var r=t[Symbol.toPrimitive];if(r!==void 0){var n=r.call(t,e||"default");if(er(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}Ti.InnerSlider=function(t){Vh(r,t);var e=qh(r);function r(n){var i;Bh(this,r),i=e.call(this,n),ae(oe(i),"listRefHandler",function(a){return i.list=a}),ae(oe(i),"trackRefHandler",function(a){return i.track=a}),ae(oe(i),"adaptHeight",function(){if(i.props.adaptiveHeight&&i.list){var a=i.list.querySelector('[data-index="'.concat(i.state.currentSlide,'"]'));i.list.style.height=(0,Ce.getHeight)(a)+"px"}}),ae(oe(i),"componentDidMount",function(){if(i.props.onInit&&i.props.onInit(),i.props.lazyLoad){var a=(0,Ce.getOnDemandLazySlides)(Y(Y({},i.props),i.state));a.length>0&&(i.setState(function(l){return{lazyLoadedList:l.lazyLoadedList.concat(a)}}),i.props.onLazyLoad&&i.props.onLazyLoad(a))}var s=Y({listRef:i.list,trackRef:i.track},i.props);i.updateState(s,!0,function(){i.adaptHeight(),i.props.autoplay&&i.autoPlay("update")}),i.props.lazyLoad==="progressive"&&(i.lazyLoadTimer=setInterval(i.progressiveLazyLoad,1e3)),i.ro=new Dh.default(function(){i.state.animating?(i.onWindowResized(!1),i.callbackTimers.push(setTimeout(function(){return i.onWindowResized()},i.props.speed))):i.onWindowResized()}),i.ro.observe(i.list),document.querySelectorAll&&Array.prototype.forEach.call(document.querySelectorAll(".slick-slide"),function(l){l.onfocus=i.props.pauseOnFocus?i.onSlideFocus:null,l.onblur=i.props.pauseOnFocus?i.onSlideBlur:null}),window.addEventListener?window.addEventListener("resize",i.onWindowResized):window.attachEvent("onresize",i.onWindowResized)}),ae(oe(i),"componentWillUnmount",function(){i.animationEndCallback&&clearTimeout(i.animationEndCallback),i.lazyLoadTimer&&clearInterval(i.lazyLoadTimer),i.callbackTimers.length&&(i.callbackTimers.forEach(function(a){return clearTimeout(a)}),i.callbackTimers=[]),window.addEventListener?window.removeEventListener("resize",i.onWindowResized):window.detachEvent("onresize",i.onWindowResized),i.autoplayTimer&&clearInterval(i.autoplayTimer),i.ro.disconnect()}),ae(oe(i),"componentDidUpdate",function(a){if(i.checkImagesLoad(),i.props.onReInit&&i.props.onReInit(),i.props.lazyLoad){var s=(0,Ce.getOnDemandLazySlides)(Y(Y({},i.props),i.state));s.length>0&&(i.setState(function(u){return{lazyLoadedList:u.lazyLoadedList.concat(s)}}),i.props.onLazyLoad&&i.props.onLazyLoad(s))}i.adaptHeight();var l=Y(Y({listRef:i.list,trackRef:i.track},i.props),i.state),c=i.didPropsChange(a);c&&i.updateState(l,c,function(){i.state.currentSlide>=De.default.Children.count(i.props.children)&&i.changeSlide({message:"index",index:De.default.Children.count(i.props.children)-i.props.slidesToShow,currentSlide:i.state.currentSlide}),i.props.autoplay?i.autoPlay("update"):i.pause("paused")})}),ae(oe(i),"onWindowResized",function(a){i.debouncedResize&&i.debouncedResize.cancel(),i.debouncedResize=(0,$h.default)(function(){return i.resizeWindow(a)},50),i.debouncedResize()}),ae(oe(i),"resizeWindow",function(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,s=!!(i.track&&i.track.node);if(s){var l=Y(Y({listRef:i.list,trackRef:i.track},i.props),i.state);i.updateState(l,a,function(){i.props.autoplay?i.autoPlay("update"):i.pause("paused")}),i.setState({animating:!1}),clearTimeout(i.animationEndCallback),delete i.animationEndCallback}}),ae(oe(i),"updateState",function(a,s,l){var c=(0,Ce.initializedState)(a);a=Y(Y(Y({},a),c),{},{slideIndex:c.currentSlide});var u=(0,Ce.getTrackLeft)(a);a=Y(Y({},a),{},{left:u});var d=(0,Ce.getTrackCSS)(a);(s||De.default.Children.count(i.props.children)!==De.default.Children.count(a.children))&&(c.trackStyle=d),i.setState(c,l)}),ae(oe(i),"ssrInit",function(){if(i.props.variableWidth){var a=0,s=0,l=[],c=(0,Ce.getPreClones)(Y(Y(Y({},i.props),i.state),{},{slideCount:i.props.children.length})),u=(0,Ce.getPostClones)(Y(Y(Y({},i.props),i.state),{},{slideCount:i.props.children.length}));i.props.children.forEach(function(H){l.push(H.props.style.width),a+=H.props.style.width});for(var d=0;d<c;d++)s+=l[l.length-1-d],a+=l[l.length-1-d];for(var p=0;p<u;p++)a+=l[p];for(var h=0;h<i.state.currentSlide;h++)s+=l[h];var b={width:a+"px",left:-s+"px"};if(i.props.centerMode){var f="".concat(l[i.state.currentSlide],"px");b.left="calc(".concat(b.left," + (100% - ").concat(f,") / 2 ) ")}return{trackStyle:b}}var w=De.default.Children.count(i.props.children),y=Y(Y(Y({},i.props),i.state),{},{slideCount:w}),v=(0,Ce.getPreClones)(y)+(0,Ce.getPostClones)(y)+w,_=100/i.props.slidesToShow*v,S=100/v,E=-S*((0,Ce.getPreClones)(y)+i.state.currentSlide)*_/100;i.props.centerMode&&(E+=(100-S*_/100)/2);var I={width:_+"%",left:E+"%"};return{slideWidth:S+"%",trackStyle:I}}),ae(oe(i),"checkImagesLoad",function(){var a=i.list&&i.list.querySelectorAll&&i.list.querySelectorAll(".slick-slide img")||[],s=a.length,l=0;Array.prototype.forEach.call(a,function(c){var u=function(){return++l&&l>=s&&i.onWindowResized()};if(!c.onclick)c.onclick=function(){return c.parentNode.focus()};else{var d=c.onclick;c.onclick=function(p){d(p),c.parentNode.focus()}}c.onload||(i.props.lazyLoad?c.onload=function(){i.adaptHeight(),i.callbackTimers.push(setTimeout(i.onWindowResized,i.props.speed))}:(c.onload=u,c.onerror=function(){u(),i.props.onLazyLoadError&&i.props.onLazyLoadError()}))})}),ae(oe(i),"progressiveLazyLoad",function(){for(var a=[],s=Y(Y({},i.props),i.state),l=i.state.currentSlide;l<i.state.slideCount+(0,Ce.getPostClones)(s);l++)if(i.state.lazyLoadedList.indexOf(l)<0){a.push(l);break}for(var c=i.state.currentSlide-1;c>=-(0,Ce.getPreClones)(s);c--)if(i.state.lazyLoadedList.indexOf(c)<0){a.push(c);break}a.length>0?(i.setState(function(u){return{lazyLoadedList:u.lazyLoadedList.concat(a)}}),i.props.onLazyLoad&&i.props.onLazyLoad(a)):i.lazyLoadTimer&&(clearInterval(i.lazyLoadTimer),delete i.lazyLoadTimer)}),ae(oe(i),"slideHandler",function(a){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l=i.props,c=l.asNavFor,u=l.beforeChange,d=l.onLazyLoad,p=l.speed,h=l.afterChange,b=i.state.currentSlide,f=(0,Ce.slideHandler)(Y(Y(Y({index:a},i.props),i.state),{},{trackRef:i.track,useCSS:i.props.useCSS&&!s})),w=f.state,y=f.nextState;if(w){u&&u(b,w.currentSlide);var v=w.lazyLoadedList.filter(function(_){return i.state.lazyLoadedList.indexOf(_)<0});d&&v.length>0&&d(v),!i.props.waitForAnimate&&i.animationEndCallback&&(clearTimeout(i.animationEndCallback),h&&h(b),delete i.animationEndCallback),i.setState(w,function(){c&&i.asNavForIndex!==a&&(i.asNavForIndex=a,c.innerSlider.slideHandler(a)),y&&(i.animationEndCallback=setTimeout(function(){var _=y.animating,S=Fh(y,["animating"]);i.setState(S,function(){i.callbackTimers.push(setTimeout(function(){return i.setState({animating:_})},10)),h&&h(w.currentSlide),delete i.animationEndCallback})},p))})}}),ae(oe(i),"changeSlide",function(a){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l=Y(Y({},i.props),i.state),c=(0,Ce.changeSlide)(l,a);if(!(c!==0&&!c)&&(s===!0?i.slideHandler(c,s):i.slideHandler(c),i.props.autoplay&&i.autoPlay("update"),i.props.focusOnSelect)){var u=i.list.querySelectorAll(".slick-current");u[0]&&u[0].focus()}}),ae(oe(i),"clickHandler",function(a){i.clickable===!1&&(a.stopPropagation(),a.preventDefault()),i.clickable=!0}),ae(oe(i),"keyHandler",function(a){var s=(0,Ce.keyHandler)(a,i.props.accessibility,i.props.rtl);s!==""&&i.changeSlide({message:s})}),ae(oe(i),"selectHandler",function(a){i.changeSlide(a)}),ae(oe(i),"disableBodyScroll",function(){var a=function(l){l=l||window.event,l.preventDefault&&l.preventDefault(),l.returnValue=!1};window.ontouchmove=a}),ae(oe(i),"enableBodyScroll",function(){window.ontouchmove=null}),ae(oe(i),"swipeStart",function(a){i.props.verticalSwiping&&i.disableBodyScroll();var s=(0,Ce.swipeStart)(a,i.props.swipe,i.props.draggable);s!==""&&i.setState(s)}),ae(oe(i),"swipeMove",function(a){var s=(0,Ce.swipeMove)(a,Y(Y(Y({},i.props),i.state),{},{trackRef:i.track,listRef:i.list,slideIndex:i.state.currentSlide}));s&&(s.swiping&&(i.clickable=!1),i.setState(s))}),ae(oe(i),"swipeEnd",function(a){var s=(0,Ce.swipeEnd)(a,Y(Y(Y({},i.props),i.state),{},{trackRef:i.track,listRef:i.list,slideIndex:i.state.currentSlide}));if(s){var l=s.triggerSlideHandler;delete s.triggerSlideHandler,i.setState(s),l!==void 0&&(i.slideHandler(l),i.props.verticalSwiping&&i.enableBodyScroll())}}),ae(oe(i),"touchEnd",function(a){i.swipeEnd(a),i.clickable=!0}),ae(oe(i),"slickPrev",function(){i.callbackTimers.push(setTimeout(function(){return i.changeSlide({message:"previous"})},0))}),ae(oe(i),"slickNext",function(){i.callbackTimers.push(setTimeout(function(){return i.changeSlide({message:"next"})},0))}),ae(oe(i),"slickGoTo",function(a){var s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(a=Number(a),isNaN(a))return"";i.callbackTimers.push(setTimeout(function(){return i.changeSlide({message:"index",index:a,currentSlide:i.state.currentSlide},s)},0))}),ae(oe(i),"play",function(){var a;if(i.props.rtl)a=i.state.currentSlide-i.props.slidesToScroll;else if((0,Ce.canGoNext)(Y(Y({},i.props),i.state)))a=i.state.currentSlide+i.props.slidesToScroll;else return!1;i.slideHandler(a)}),ae(oe(i),"autoPlay",function(a){i.autoplayTimer&&clearInterval(i.autoplayTimer);var s=i.state.autoplaying;if(a==="update"){if(s==="hovered"||s==="focused"||s==="paused")return}else if(a==="leave"){if(s==="paused"||s==="focused")return}else if(a==="blur"&&(s==="paused"||s==="hovered"))return;i.autoplayTimer=setInterval(i.play,i.props.autoplaySpeed+50),i.setState({autoplaying:"playing"})}),ae(oe(i),"pause",function(a){i.autoplayTimer&&(clearInterval(i.autoplayTimer),i.autoplayTimer=null);var s=i.state.autoplaying;a==="paused"?i.setState({autoplaying:"paused"}):a==="focused"?(s==="hovered"||s==="playing")&&i.setState({autoplaying:"focused"}):s==="playing"&&i.setState({autoplaying:"hovered"})}),ae(oe(i),"onDotsOver",function(){return i.props.autoplay&&i.pause("hovered")}),ae(oe(i),"onDotsLeave",function(){return i.props.autoplay&&i.state.autoplaying==="hovered"&&i.autoPlay("leave")}),ae(oe(i),"onTrackOver",function(){return i.props.autoplay&&i.pause("hovered")}),ae(oe(i),"onTrackLeave",function(){return i.props.autoplay&&i.state.autoplaying==="hovered"&&i.autoPlay("leave")}),ae(oe(i),"onSlideFocus",function(){return i.props.autoplay&&i.pause("focused")}),ae(oe(i),"onSlideBlur",function(){return i.props.autoplay&&i.state.autoplaying==="focused"&&i.autoPlay("blur")}),ae(oe(i),"render",function(){var a=(0,zh.default)("slick-slider",i.props.className,{"slick-vertical":i.props.vertical,"slick-initialized":!0}),s=Y(Y({},i.props),i.state),l=(0,Ce.extractObject)(s,["fade","cssEase","speed","infinite","centerMode","focusOnSelect","currentSlide","lazyLoad","lazyLoadedList","rtl","slideWidth","slideHeight","listHeight","vertical","slidesToShow","slidesToScroll","slideCount","trackStyle","variableWidth","unslick","centerPadding","targetSlide","useCSS"]),c=i.props.pauseOnHover;l=Y(Y({},l),{},{onMouseEnter:c?i.onTrackOver:null,onMouseLeave:c?i.onTrackLeave:null,onMouseOver:c?i.onTrackOver:null,focusOnSelect:i.props.focusOnSelect&&i.clickable?i.selectHandler:null});var u;if(i.props.dots===!0&&i.state.slideCount>=i.props.slidesToShow){var d=(0,Ce.extractObject)(s,["dotsClass","slideCount","slidesToShow","currentSlide","slidesToScroll","clickHandler","children","customPaging","infinite","appendDots"]),p=i.props.pauseOnDotsHover;d=Y(Y({},d),{},{clickHandler:i.changeSlide,onMouseEnter:p?i.onDotsLeave:null,onMouseOver:p?i.onDotsOver:null,onMouseLeave:p?i.onDotsLeave:null}),u=De.default.createElement(Lh.Dots,d)}var h,b,f=(0,Ce.extractObject)(s,["infinite","centerMode","currentSlide","slideCount","slidesToShow","prevArrow","nextArrow"]);f.clickHandler=i.changeSlide,i.props.arrows&&(h=De.default.createElement(ss.PrevArrow,f),b=De.default.createElement(ss.NextArrow,f));var w=null;i.props.vertical&&(w={height:i.state.listHeight});var y=null;i.props.vertical===!1?i.props.centerMode===!0&&(y={padding:"0px "+i.props.centerPadding}):i.props.centerMode===!0&&(y={padding:i.props.centerPadding+" 0px"});var v=Y(Y({},w),y),_=i.props.touchMove,S={className:"slick-list",style:v,onClick:i.clickHandler,onMouseDown:_?i.swipeStart:null,onMouseMove:i.state.dragging&&_?i.swipeMove:null,onMouseUp:_?i.swipeEnd:null,onMouseLeave:i.state.dragging&&_?i.swipeEnd:null,onTouchStart:_?i.swipeStart:null,onTouchMove:i.state.dragging&&_?i.swipeMove:null,onTouchEnd:_?i.touchEnd:null,onTouchCancel:i.state.dragging&&_?i.swipeEnd:null,onKeyDown:i.props.accessibility?i.keyHandler:null},E={className:a,dir:"ltr",style:i.props.style};return i.props.unslick&&(S={className:"slick-list"},E={className:a}),De.default.createElement("div",E,i.props.unslick?"":h,De.default.createElement("div",ci({ref:i.listRefHandler},S),De.default.createElement(jh.Track,ci({ref:i.trackRefHandler},l),i.props.children)),i.props.unslick?"":b,i.props.unslick?"":u)}),i.list=null,i.track=null,i.state=Y(Y({},Ih.default),{},{currentSlide:i.props.initialSlide,targetSlide:i.props.initialSlide?i.props.initialSlide:0,slideCount:De.default.Children.count(i.props.children)}),i.callbackTimers=[],i.clickable=!0,i.debouncedResize=null;var o=i.ssrInit();return i.state=Y(Y({},i.state),o),i}return Wh(r,[{key:"didPropsChange",value:function(i){for(var o=!1,a=0,s=Object.keys(this.props);a<s.length;a++){var l=s[a];if(!i.hasOwnProperty(l)){o=!0;break}if(!(er(i[l])==="object"||typeof i[l]=="function"||isNaN(i[l]))&&i[l]!==this.props[l]){o=!0;break}}return o||De.default.Children.count(this.props.children)!==De.default.Children.count(i.children)}}]),r}(De.default.Component);var Zh=function(t){return t.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()}).toLowerCase()},Yh=Zh,Kh=Yh,Qh=function(t){var e=/[height|width]$/;return e.test(t)},ds=function(t){var e="",r=Object.keys(t);return r.forEach(function(n,i){var o=t[n];n=Kh(n),Qh(n)&&typeof o=="number"&&(o=o+"px"),o===!0?e+=n:o===!1?e+="not "+n:e+="("+n+": "+o+")",i<r.length-1&&(e+=" and ")}),e},Xh=function(t){var e="";return typeof t=="string"?t:t instanceof Array?(t.forEach(function(r,n){e+=ds(r),n<t.length-1&&(e+=", ")}),e):ds(t)},Jh=Xh,Ji,us;function ef(){if(us)return Ji;us=1;function t(e){this.options=e,!e.deferSetup&&this.setup()}return t.prototype={constructor:t,setup:function(){this.options.setup&&this.options.setup(),this.initialised=!0},on:function(){!this.initialised&&this.setup(),this.options.match&&this.options.match()},off:function(){this.options.unmatch&&this.options.unmatch()},destroy:function(){this.options.destroy?this.options.destroy():this.off()},equals:function(e){return this.options===e||this.options.match===e}},Ji=t,Ji}var eo,ps;function ic(){if(ps)return eo;ps=1;function t(n,i){var o=0,a=n.length,s;for(o;o<a&&(s=i(n[o],o),s!==!1);o++);}function e(n){return Object.prototype.toString.apply(n)==="[object Array]"}function r(n){return typeof n=="function"}return eo={isFunction:r,isArray:e,each:t},eo}var to,hs;function tf(){if(hs)return to;hs=1;var t=ef(),e=ic().each;function r(n,i){this.query=n,this.isUnconditional=i,this.handlers=[],this.mql=window.matchMedia(n);var o=this;this.listener=function(a){o.mql=a.currentTarget||a,o.assess()},this.mql.addListener(this.listener)}return r.prototype={constuctor:r,addHandler:function(n){var i=new t(n);this.handlers.push(i),this.matches()&&i.on()},removeHandler:function(n){var i=this.handlers;e(i,function(o,a){if(o.equals(n))return o.destroy(),!i.splice(a,1)})},matches:function(){return this.mql.matches||this.isUnconditional},clear:function(){e(this.handlers,function(n){n.destroy()}),this.mql.removeListener(this.listener),this.handlers.length=0},assess:function(){var n=this.matches()?"on":"off";e(this.handlers,function(i){i[n]()})}},to=r,to}var ro,fs;function rf(){if(fs)return ro;fs=1;var t=tf(),e=ic(),r=e.each,n=e.isFunction,i=e.isArray;function o(){if(!window.matchMedia)throw new Error("matchMedia not present, legacy browsers require a polyfill");this.queries={},this.browserIsIncapable=!window.matchMedia("only all").matches}return o.prototype={constructor:o,register:function(a,s,l){var c=this.queries,u=l&&this.browserIsIncapable;return c[a]||(c[a]=new t(a,u)),n(s)&&(s={match:s}),i(s)||(s=[s]),r(s,function(d){n(d)&&(d={match:d}),c[a].addHandler(d)}),this},unregister:function(a,s){var l=this.queries[a];return l&&(s?l.removeHandler(s):(l.clear(),delete this.queries[a])),this}},ro=o,ro}var no,ms;function nf(){if(ms)return no;ms=1;var t=rf();return no=new t,no}(function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e=a(q),r=Ti,n=a(Jh),i=a(ca),o=M;function a(k){return k&&k.__esModule?k:{default:k}}function s(k){"@babel/helpers - typeof";return s=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(O){return typeof O}:function(O){return O&&typeof Symbol=="function"&&O.constructor===Symbol&&O!==Symbol.prototype?"symbol":typeof O},s(k)}function l(){return l=Object.assign?Object.assign.bind():function(k){for(var O=1;O<arguments.length;O++){var $=arguments[O];for(var j in $)Object.prototype.hasOwnProperty.call($,j)&&(k[j]=$[j])}return k},l.apply(this,arguments)}function c(k,O){var $=Object.keys(k);if(Object.getOwnPropertySymbols){var j=Object.getOwnPropertySymbols(k);O&&(j=j.filter(function(N){return Object.getOwnPropertyDescriptor(k,N).enumerable})),$.push.apply($,j)}return $}function u(k){for(var O=1;O<arguments.length;O++){var $=arguments[O]!=null?arguments[O]:{};O%2?c(Object($),!0).forEach(function(j){E(k,j,$[j])}):Object.getOwnPropertyDescriptors?Object.defineProperties(k,Object.getOwnPropertyDescriptors($)):c(Object($)).forEach(function(j){Object.defineProperty(k,j,Object.getOwnPropertyDescriptor($,j))})}return k}function d(k,O){if(!(k instanceof O))throw new TypeError("Cannot call a class as a function")}function p(k,O){for(var $=0;$<O.length;$++){var j=O[$];j.enumerable=j.enumerable||!1,j.configurable=!0,"value"in j&&(j.writable=!0),Object.defineProperty(k,I(j.key),j)}}function h(k,O,$){return O&&p(k.prototype,O),$&&p(k,$),Object.defineProperty(k,"prototype",{writable:!1}),k}function b(k,O){if(typeof O!="function"&&O!==null)throw new TypeError("Super expression must either be null or a function");k.prototype=Object.create(O&&O.prototype,{constructor:{value:k,writable:!0,configurable:!0}}),Object.defineProperty(k,"prototype",{writable:!1}),O&&f(k,O)}function f(k,O){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(j,N){return j.__proto__=N,j},f(k,O)}function w(k){var O=_();return function(){var j=S(k),N;if(O){var T=S(this).constructor;N=Reflect.construct(j,arguments,T)}else N=j.apply(this,arguments);return y(this,N)}}function y(k,O){if(O&&(s(O)==="object"||typeof O=="function"))return O;if(O!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return v(k)}function v(k){if(k===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return k}function _(){try{var k=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(_=function(){return!!k})()}function S(k){return S=Object.setPrototypeOf?Object.getPrototypeOf.bind():function($){return $.__proto__||Object.getPrototypeOf($)},S(k)}function E(k,O,$){return O=I(O),O in k?Object.defineProperty(k,O,{value:$,enumerable:!0,configurable:!0,writable:!0}):k[O]=$,k}function I(k){var O=H(k,"string");return s(O)=="symbol"?O:String(O)}function H(k,O){if(s(k)!="object"||!k)return k;var $=k[Symbol.toPrimitive];if($!==void 0){var j=$.call(k,O||"default");if(s(j)!="object")return j;throw new TypeError("@@toPrimitive must return a primitive value.")}return(O==="string"?String:Number)(k)}var R=(0,o.canUseDOM)()&&nf();t.default=function(k){b($,k);var O=w($);function $(j){var N;return d(this,$),N=O.call(this,j),E(v(N),"innerSliderRefHandler",function(T){return N.innerSlider=T}),E(v(N),"slickPrev",function(){return N.innerSlider.slickPrev()}),E(v(N),"slickNext",function(){return N.innerSlider.slickNext()}),E(v(N),"slickGoTo",function(T){var pe=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return N.innerSlider.slickGoTo(T,pe)}),E(v(N),"slickPause",function(){return N.innerSlider.pause("paused")}),E(v(N),"slickPlay",function(){return N.innerSlider.autoPlay("play")}),N.state={breakpoint:null},N._responsiveMediaHandlers=[],N}return h($,[{key:"media",value:function(N,T){R.register(N,T),this._responsiveMediaHandlers.push({query:N,handler:T})}},{key:"componentDidMount",value:function(){var N=this;if(this.props.responsive){var T=this.props.responsive.map(function(Z){return Z.breakpoint});T.sort(function(Z,ge){return Z-ge}),T.forEach(function(Z,ge){var B;ge===0?B=(0,n.default)({minWidth:0,maxWidth:Z}):B=(0,n.default)({minWidth:T[ge-1]+1,maxWidth:Z}),(0,o.canUseDOM)()&&N.media(B,function(){N.setState({breakpoint:Z})})});var pe=(0,n.default)({minWidth:T.slice(-1)[0]});(0,o.canUseDOM)()&&this.media(pe,function(){N.setState({breakpoint:null})})}}},{key:"componentWillUnmount",value:function(){this._responsiveMediaHandlers.forEach(function(N){R.unregister(N.query,N.handler)})}},{key:"render",value:function(){var N=this,T,pe;this.state.breakpoint?(pe=this.props.responsive.filter(function(se){return se.breakpoint===N.state.breakpoint}),T=pe[0].settings==="unslick"?"unslick":u(u(u({},i.default),this.props),pe[0].settings)):T=u(u({},i.default),this.props),T.centerMode&&(T.slidesToScroll>1,T.slidesToScroll=1),T.fade&&(T.slidesToShow>1,T.slidesToScroll>1,T.slidesToShow=1,T.slidesToScroll=1);var Z=e.default.Children.toArray(this.props.children);Z=Z.filter(function(se){return typeof se=="string"?!!se.trim():!!se}),T.variableWidth&&(T.rows>1||T.slidesPerRow>1)&&(console.warn("variableWidth is not supported in case of rows > 1 or slidesPerRow > 1"),T.variableWidth=!1);for(var ge=[],B=null,G=0;G<Z.length;G+=T.rows*T.slidesPerRow){for(var fe=[],le=G;le<G+T.rows*T.slidesPerRow;le+=T.slidesPerRow){for(var U=[],L=le;L<le+T.slidesPerRow&&(T.variableWidth&&Z[L].props.style&&(B=Z[L].props.style.width),!(L>=Z.length));L+=1)U.push(e.default.cloneElement(Z[L],{key:100*G+10*le+L,tabIndex:-1,style:{width:"".concat(100/T.slidesPerRow,"%"),display:"inline-block"}}));fe.push(e.default.createElement("div",{key:10*G+le},U))}T.variableWidth?ge.push(e.default.createElement("div",{key:G,style:{width:B}},fe)):ge.push(e.default.createElement("div",{key:G},fe))}if(T==="unslick"){var te="regular slider "+(this.props.className||"");return e.default.createElement("div",{className:te},Z)}else ge.length<=T.slidesToShow&&!T.infinite&&(T.unslick=!0);return e.default.createElement(r.InnerSlider,l({style:this.props.style,ref:this.innerSliderRefHandler},(0,o.filterSettings)(T)),ge)}}]),$}(e.default.Component)})(Cl);(function(t){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var e=r(Cl);function r(n){return n&&n.__esModule?n:{default:n}}t.default=e.default})(Sl);const of=xu(Sl);const af=()=>{var r;qe.getAwards();const t=qe.getCertificates();qe.getBasics();const e=[{type:"achievement",title:"Red Hat Innovation Award Winner",description:"Recognized for outstanding achievements in open source with FICO's $10M+ analytics platform",icon:"🏆",link:"#awards",highlight:"Reduced infrastructure costs by 20% and improved time to market by 70%"},{type:"certification",title:"AWS Solutions Architect",description:"Current AWS certification demonstrating cloud architecture expertise",icon:"☁️",link:((r=t.find(n=>n.name.includes("AWS")))==null?void 0:r.url)||"#certificates",highlight:"Valid through 2025"},{type:"expertise",title:"AI/ML Integration Specialist",description:"Leading AI-driven solutions and automation implementations",icon:"🤖",link:"#experience",highlight:"Implementing cutting-edge AI solutions at AHEAD"},{type:"thought-leadership",title:"OpenStack Speakers Bureau",description:"Published articles and industry conference presentations",icon:"🎤",link:"#publications",highlight:"Recognized thought leader in cloud technologies"}];return g.jsx("section",{id:"featured",className:"featured","aria-labelledby":"featured-title",children:g.jsxs("div",{className:"container",children:[g.jsxs(it,{children:[g.jsx("h2",{id:"featured-title",className:"section-title",children:"Featured Highlights"}),g.jsx("p",{className:"section-subtitle",children:"Key achievements and expertise that drive results"})]}),g.jsx("div",{className:"featured-grid",children:e.map((n,i)=>g.jsx(it,{delay:.1*(i+1),children:g.jsxs("div",{className:"featured-card",children:[g.jsxs("div",{className:"featured-card-header",children:[g.jsx("span",{className:"featured-icon",children:n.icon}),g.jsx("div",{className:"featured-badge",children:n.type})]}),g.jsx("h3",{className:"featured-title",children:n.title}),g.jsx("p",{className:"featured-description",children:n.description})]})},i))}),g.jsx(it,{delay:.6,children:g.jsxs("div",{className:"social-proof",children:[g.jsx("h3",{className:"social-proof-title",children:"Trusted by Industry Leaders"}),g.jsx("div",{className:"company-logos-container",children:g.jsx(of,{className:"company-logos",dots:!0,infinite:!0,speed:500,slidesToShow:5,slidesToScroll:2,autoplay:!0,autoplaySpeed:3e3,pauseOnHover:!0,responsive:[{breakpoint:1024,settings:{slidesToShow:4,slidesToScroll:2}},{breakpoint:768,settings:{slidesToShow:3,slidesToScroll:1}},{breakpoint:480,settings:{slidesToShow:2,slidesToScroll:1}}],children:[{name:"AWS",src:"/Logos/Amazon_Web_Services-Logo.wine.svg"},{name:"Red Hat",src:"/Logos/Red_Hat-Logo.wine.svg"},{name:"Microsoft",src:"/Logos/Microsoft-Logo.wine.svg"},{name:"FICO",src:"/Logos/FICO_logo.svg.png"},{name:"American Express",src:"/Logos/American_Express-Logo.wine.svg"},{name:"JPMorgan Chase",src:"/Logos/JPMorgan_Chase-Logo.wine.svg"},{name:"PayPal",src:"/Logos/PayPal-Logo.wine.svg"},{name:"Visa",src:"/Logos/Visa_Inc.-Logo.wine.svg"},{name:"Canon",src:"/Logos/Canon_Inc.-Logo.wine.svg"}].map((n,i)=>g.jsx("div",{className:"company-logo-wrapper",children:g.jsx("div",{className:"company-logo",children:g.jsx("img",{src:n.src,alt:`${n.name} logo`,className:"logo-image",loading:"lazy",onError:o=>{console.error(`Failed to load logo for ${n.name}:`,n.src),o.target.style.display="none"}})})},i))})})]})})]})})};const sf=()=>{const[t,e]=q.useState(null),r=i=>{e(t===i?null:i)},n=qe.getFormattedWorkExperience();return g.jsx("section",{id:"experience",className:"experience",children:g.jsxs("div",{className:"container",children:[g.jsx("h2",{className:"section-title",children:"Experience"}),g.jsx("div",{className:"timeline",children:n.map((i,o)=>g.jsx(it,{children:g.jsx("div",{className:"timeline-item","data-company":i.company,children:g.jsxs("div",{className:"timeline-content",children:[g.jsxs("div",{className:"timeline-header",onClick:()=>r(o),children:[g.jsx("h3",{className:"job-title",children:i.title}),g.jsx("span",{className:"company",children:i.company}),g.jsx("span",{className:"duration",children:i.duration}),i.location&&g.jsx("span",{className:"location",children:i.location}),g.jsx("button",{className:`expand-btn ${t===o?"active":""}`,"aria-label":"Expand details",children:t===o?"−":"+"})]}),g.jsx("div",{className:`timeline-details ${t===o?"expanded":""}`,children:g.jsx("ul",{className:"responsibilities",children:i.responsibilities.map((a,s)=>g.jsx("li",{children:a},s))})})]})})},o))})]})})};var ua=Cn(),J=t=>Sn(t,ua),pa=Cn();J.write=t=>Sn(t,pa);var $i=Cn();J.onStart=t=>Sn(t,$i);var ha=Cn();J.onFrame=t=>Sn(t,ha);var fa=Cn();J.onFinish=t=>Sn(t,fa);var hr=[];J.setTimeout=(t,e)=>{const r=J.now()+e,n=()=>{const o=hr.findIndex(a=>a.cancel==n);~o&&hr.splice(o,1),$t-=~o?1:0},i={time:r,handler:t,cancel:n};return hr.splice(oc(r),0,i),$t+=1,ac(),i};var oc=t=>~(~hr.findIndex(e=>e.time>t)||~hr.length);J.cancel=t=>{$i.delete(t),ha.delete(t),fa.delete(t),ua.delete(t),pa.delete(t)};J.sync=t=>{Ao=!0,J.batchedUpdates(t),Ao=!1};J.throttle=t=>{let e;function r(){try{t(...e)}finally{e=null}}function n(...i){e=i,J.onStart(r)}return n.handler=t,n.cancel=()=>{$i.delete(r),e=null},n};var ma=typeof window<"u"?window.requestAnimationFrame:()=>{};J.use=t=>ma=t;J.now=typeof performance<"u"?()=>performance.now():Date.now;J.batchedUpdates=t=>t();J.catch=console.error;J.frameLoop="always";J.advance=()=>{J.frameLoop!=="demand"?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):lc()};var It=-1,$t=0,Ao=!1;function Sn(t,e){Ao?(e.delete(t),t(0)):(e.add(t),ac())}function ac(){It<0&&(It=0,J.frameLoop!=="demand"&&ma(sc))}function lf(){It=-1}function sc(){~It&&(ma(sc),J.batchedUpdates(lc))}function lc(){const t=It;It=J.now();const e=oc(It);if(e&&(cc(hr.splice(0,e),r=>r.handler()),$t-=e),!$t){lf();return}$i.flush(),ua.flush(t?Math.min(64,It-t):16.667),ha.flush(),pa.flush(),fa.flush()}function Cn(){let t=new Set,e=t;return{add(r){$t+=e==t&&!t.has(r)?1:0,t.add(r)},delete(r){return $t-=e==t&&t.has(r)?1:0,t.delete(r)},flush(r){e.size&&(t=new Set,$t-=e.size,cc(e,n=>n(r)&&t.add(n)),$t+=t.size,e=t)}}}function cc(t,e){t.forEach(r=>{try{e(r)}catch(n){J.catch(n)}})}var cf=Object.defineProperty,df=(t,e)=>{for(var r in e)cf(t,r,{get:e[r],enumerable:!0})},lt={};df(lt,{assign:()=>pf,colors:()=>zt,createStringInterpolator:()=>va,skipAnimation:()=>uc,to:()=>dc,willAdvance:()=>ba});function Ro(){}var uf=(t,e,r)=>Object.defineProperty(t,e,{value:r,writable:!0,configurable:!0}),P={arr:Array.isArray,obj:t=>!!t&&t.constructor.name==="Object",fun:t=>typeof t=="function",str:t=>typeof t=="string",num:t=>typeof t=="number",und:t=>t===void 0};function xt(t,e){if(P.arr(t)){if(!P.arr(e)||t.length!==e.length)return!1;for(let r=0;r<t.length;r++)if(t[r]!==e[r])return!1;return!0}return t===e}var de=(t,e)=>t.forEach(e);function mt(t,e,r){if(P.arr(t)){for(let n=0;n<t.length;n++)e.call(r,t[n],`${n}`);return}for(const n in t)t.hasOwnProperty(n)&&e.call(r,t[n],n)}var Qe=t=>P.und(t)?[]:P.arr(t)?t:[t];function qr(t,e){if(t.size){const r=Array.from(t);t.clear(),de(r,e)}}var Fr=(t,...e)=>qr(t,r=>r(...e)),ga=()=>typeof window>"u"||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),va,dc,zt=null,uc=!1,ba=Ro,pf=t=>{t.to&&(dc=t.to),t.now&&(J.now=t.now),t.colors!==void 0&&(zt=t.colors),t.skipAnimation!=null&&(uc=t.skipAnimation),t.createStringInterpolator&&(va=t.createStringInterpolator),t.requestAnimationFrame&&J.use(t.requestAnimationFrame),t.batchedUpdates&&(J.batchedUpdates=t.batchedUpdates),t.willAdvance&&(ba=t.willAdvance),t.frameLoop&&(J.frameLoop=t.frameLoop)},Ur=new Set,tt=[],io=[],ui=0,zi={get idle(){return!Ur.size&&!tt.length},start(t){ui>t.priority?(Ur.add(t),J.onStart(hf)):(pc(t),J(Io))},advance:Io,sort(t){if(ui)J.onFrame(()=>zi.sort(t));else{const e=tt.indexOf(t);~e&&(tt.splice(e,1),hc(t))}},clear(){tt=[],Ur.clear()}};function hf(){Ur.forEach(pc),Ur.clear(),J(Io)}function pc(t){tt.includes(t)||hc(t)}function hc(t){tt.splice(ff(tt,e=>e.priority>t.priority),0,t)}function Io(t){const e=io;for(let r=0;r<tt.length;r++){const n=tt[r];ui=n.priority,n.idle||(ba(n),n.advance(t),n.idle||e.push(n))}return ui=0,io=tt,io.length=0,tt=e,tt.length>0}function ff(t,e){const r=t.findIndex(e);return r<0?t.length:r}var mf=(t,e,r)=>Math.min(Math.max(r,t),e),gf={transparent:0,aliceblue:4042850303,antiquewhite:4209760255,aqua:16777215,aquamarine:2147472639,azure:4043309055,beige:4126530815,bisque:4293182719,black:255,blanchedalmond:4293643775,blue:65535,blueviolet:2318131967,brown:2771004159,burlywood:3736635391,burntsienna:3934150143,cadetblue:1604231423,chartreuse:2147418367,chocolate:3530104575,coral:4286533887,cornflowerblue:1687547391,cornsilk:4294499583,crimson:3692313855,cyan:16777215,darkblue:35839,darkcyan:9145343,darkgoldenrod:3095792639,darkgray:2846468607,darkgreen:6553855,darkgrey:2846468607,darkkhaki:3182914559,darkmagenta:2332068863,darkolivegreen:1433087999,darkorange:4287365375,darkorchid:2570243327,darkred:2332033279,darksalmon:3918953215,darkseagreen:2411499519,darkslateblue:1211993087,darkslategray:793726975,darkslategrey:793726975,darkturquoise:13554175,darkviolet:2483082239,deeppink:4279538687,deepskyblue:12582911,dimgray:1768516095,dimgrey:1768516095,dodgerblue:512819199,firebrick:2988581631,floralwhite:4294635775,forestgreen:579543807,fuchsia:4278255615,gainsboro:3705462015,ghostwhite:4177068031,gold:4292280575,goldenrod:3668254975,gray:2155905279,green:8388863,greenyellow:2919182335,grey:2155905279,honeydew:4043305215,hotpink:4285117695,indianred:3445382399,indigo:1258324735,ivory:4294963455,khaki:4041641215,lavender:3873897215,lavenderblush:4293981695,lawngreen:2096890111,lemonchiffon:4294626815,lightblue:2916673279,lightcoral:4034953471,lightcyan:3774873599,lightgoldenrodyellow:4210742015,lightgray:3553874943,lightgreen:2431553791,lightgrey:3553874943,lightpink:4290167295,lightsalmon:4288707327,lightseagreen:548580095,lightskyblue:2278488831,lightslategray:2005441023,lightslategrey:2005441023,lightsteelblue:2965692159,lightyellow:4294959359,lime:16711935,limegreen:852308735,linen:4210091775,magenta:4278255615,maroon:2147483903,mediumaquamarine:1724754687,mediumblue:52735,mediumorchid:3126187007,mediumpurple:2473647103,mediumseagreen:1018393087,mediumslateblue:2070474495,mediumspringgreen:16423679,mediumturquoise:1221709055,mediumvioletred:3340076543,midnightblue:421097727,mintcream:4127193855,mistyrose:4293190143,moccasin:4293178879,navajowhite:4292783615,navy:33023,oldlace:4260751103,olive:2155872511,olivedrab:1804477439,orange:4289003775,orangered:4282712319,orchid:3664828159,palegoldenrod:4008225535,palegreen:2566625535,paleturquoise:2951671551,palevioletred:3681588223,papayawhip:4293907967,peachpuff:4292524543,peru:3448061951,pink:4290825215,plum:3718307327,powderblue:2967529215,purple:2147516671,rebeccapurple:1714657791,red:4278190335,rosybrown:3163525119,royalblue:1097458175,saddlebrown:2336560127,salmon:4202722047,sandybrown:4104413439,seagreen:780883967,seashell:4294307583,sienna:2689740287,silver:3233857791,skyblue:2278484991,slateblue:1784335871,slategray:1887473919,slategrey:1887473919,snow:4294638335,springgreen:16744447,steelblue:1182971135,tan:3535047935,teal:8421631,thistle:3636451583,tomato:4284696575,turquoise:1088475391,violet:4001558271,wheat:4125012991,white:4294967295,whitesmoke:4126537215,yellow:4294902015,yellowgreen:2597139199},ot="[-+]?\\d*\\.?\\d+",pi=ot+"%";function ji(...t){return"\\(\\s*("+t.join(")\\s*,\\s*(")+")\\s*\\)"}var vf=new RegExp("rgb"+ji(ot,ot,ot)),bf=new RegExp("rgba"+ji(ot,ot,ot,ot)),wf=new RegExp("hsl"+ji(ot,pi,pi)),yf=new RegExp("hsla"+ji(ot,pi,pi,ot)),_f=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,xf=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,kf=/^#([0-9a-fA-F]{6})$/,Sf=/^#([0-9a-fA-F]{8})$/;function Cf(t){let e;return typeof t=="number"?t>>>0===t&&t>=0&&t<=4294967295?t:null:(e=kf.exec(t))?parseInt(e[1]+"ff",16)>>>0:zt&&zt[t]!==void 0?zt[t]:(e=vf.exec(t))?(ar(e[1])<<24|ar(e[2])<<16|ar(e[3])<<8|255)>>>0:(e=bf.exec(t))?(ar(e[1])<<24|ar(e[2])<<16|ar(e[3])<<8|bs(e[4]))>>>0:(e=_f.exec(t))?parseInt(e[1]+e[1]+e[2]+e[2]+e[3]+e[3]+"ff",16)>>>0:(e=Sf.exec(t))?parseInt(e[1],16)>>>0:(e=xf.exec(t))?parseInt(e[1]+e[1]+e[2]+e[2]+e[3]+e[3]+e[4]+e[4],16)>>>0:(e=wf.exec(t))?(gs(vs(e[1]),Dn(e[2]),Dn(e[3]))|255)>>>0:(e=yf.exec(t))?(gs(vs(e[1]),Dn(e[2]),Dn(e[3]))|bs(e[4]))>>>0:null}function oo(t,e,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+(e-t)*6*r:r<1/2?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function gs(t,e,r){const n=r<.5?r*(1+e):r+e-r*e,i=2*r-n,o=oo(i,n,t+1/3),a=oo(i,n,t),s=oo(i,n,t-1/3);return Math.round(o*255)<<24|Math.round(a*255)<<16|Math.round(s*255)<<8}function ar(t){const e=parseInt(t,10);return e<0?0:e>255?255:e}function vs(t){return(parseFloat(t)%360+360)%360/360}function bs(t){const e=parseFloat(t);return e<0?0:e>1?255:Math.round(e*255)}function Dn(t){const e=parseFloat(t);return e<0?0:e>100?1:e/100}function ws(t){let e=Cf(t);if(e===null)return t;e=e||0;const r=(e&4278190080)>>>24,n=(e&16711680)>>>16,i=(e&65280)>>>8,o=(e&255)/255;return`rgba(${r}, ${n}, ${i}, ${o})`}var Xr=(t,e,r)=>{if(P.fun(t))return t;if(P.arr(t))return Xr({range:t,output:e,extrapolate:r});if(P.str(t.output[0]))return va(t);const n=t,i=n.output,o=n.range||[0,1],a=n.extrapolateLeft||n.extrapolate||"extend",s=n.extrapolateRight||n.extrapolate||"extend",l=n.easing||(c=>c);return c=>{const u=Of(c,o);return Ef(c,o[u],o[u+1],i[u],i[u+1],l,a,s,n.map)}};function Ef(t,e,r,n,i,o,a,s,l){let c=l?l(t):t;if(c<e){if(a==="identity")return c;a==="clamp"&&(c=e)}if(c>r){if(s==="identity")return c;s==="clamp"&&(c=r)}return n===i?n:e===r?t<=e?n:i:(e===-1/0?c=-c:r===1/0?c=c-e:c=(c-e)/(r-e),c=o(c),n===-1/0?c=-c:i===1/0?c=c+n:c=c*(i-n)+n,c)}function Of(t,e){for(var r=1;r<e.length-1&&!(e[r]>=t);++r);return r-1}var Nf=(t,e="end")=>r=>{r=e==="end"?Math.min(r,.999):Math.max(r,.001);const n=r*t,i=e==="end"?Math.floor(n):Math.ceil(n);return mf(0,1,i/t)},hi=1.70158,Fn=hi*1.525,ys=hi+1,_s=2*Math.PI/3,xs=2*Math.PI/4.5,Hn=t=>t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375,Pf={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>1-(1-t)*(1-t),easeInOutQuad:t=>t<.5?2*t*t:1-Math.pow(-2*t+2,2)/2,easeInCubic:t=>t*t*t,easeOutCubic:t=>1-Math.pow(1-t,3),easeInOutCubic:t=>t<.5?4*t*t*t:1-Math.pow(-2*t+2,3)/2,easeInQuart:t=>t*t*t*t,easeOutQuart:t=>1-Math.pow(1-t,4),easeInOutQuart:t=>t<.5?8*t*t*t*t:1-Math.pow(-2*t+2,4)/2,easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>1-Math.pow(1-t,5),easeInOutQuint:t=>t<.5?16*t*t*t*t*t:1-Math.pow(-2*t+2,5)/2,easeInSine:t=>1-Math.cos(t*Math.PI/2),easeOutSine:t=>Math.sin(t*Math.PI/2),easeInOutSine:t=>-(Math.cos(Math.PI*t)-1)/2,easeInExpo:t=>t===0?0:Math.pow(2,10*t-10),easeOutExpo:t=>t===1?1:1-Math.pow(2,-10*t),easeInOutExpo:t=>t===0?0:t===1?1:t<.5?Math.pow(2,20*t-10)/2:(2-Math.pow(2,-20*t+10))/2,easeInCirc:t=>1-Math.sqrt(1-Math.pow(t,2)),easeOutCirc:t=>Math.sqrt(1-Math.pow(t-1,2)),easeInOutCirc:t=>t<.5?(1-Math.sqrt(1-Math.pow(2*t,2)))/2:(Math.sqrt(1-Math.pow(-2*t+2,2))+1)/2,easeInBack:t=>ys*t*t*t-hi*t*t,easeOutBack:t=>1+ys*Math.pow(t-1,3)+hi*Math.pow(t-1,2),easeInOutBack:t=>t<.5?Math.pow(2*t,2)*((Fn+1)*2*t-Fn)/2:(Math.pow(2*t-2,2)*((Fn+1)*(t*2-2)+Fn)+2)/2,easeInElastic:t=>t===0?0:t===1?1:-Math.pow(2,10*t-10)*Math.sin((t*10-10.75)*_s),easeOutElastic:t=>t===0?0:t===1?1:Math.pow(2,-10*t)*Math.sin((t*10-.75)*_s)+1,easeInOutElastic:t=>t===0?0:t===1?1:t<.5?-(Math.pow(2,20*t-10)*Math.sin((20*t-11.125)*xs))/2:Math.pow(2,-20*t+10)*Math.sin((20*t-11.125)*xs)/2+1,easeInBounce:t=>1-Hn(1-t),easeOutBounce:Hn,easeInOutBounce:t=>t<.5?(1-Hn(1-2*t))/2:(1+Hn(2*t-1))/2,steps:Nf},Jr=Symbol.for("FluidValue.get"),xr=Symbol.for("FluidValue.observers"),et=t=>!!(t&&t[Jr]),Ve=t=>t&&t[Jr]?t[Jr]():t,ks=t=>t[xr]||null;function Tf(t,e){t.eventObserved?t.eventObserved(e):t(e)}function en(t,e){const r=t[xr];r&&r.forEach(n=>{Tf(n,e)})}var fc=class{constructor(t){if(!t&&!(t=this.get))throw Error("Unknown getter");Mf(this,t)}},Mf=(t,e)=>mc(t,Jr,e);function Tr(t,e){if(t[Jr]){let r=t[xr];r||mc(t,xr,r=new Set),r.has(e)||(r.add(e),t.observerAdded&&t.observerAdded(r.size,e))}return e}function tn(t,e){const r=t[xr];if(r&&r.has(e)){const n=r.size-1;n?r.delete(e):t[xr]=null,t.observerRemoved&&t.observerRemoved(n,e)}}var mc=(t,e,r)=>Object.defineProperty(t,e,{value:r,writable:!0,configurable:!0}),Kn=/[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,Af=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi,Ss=new RegExp(`(${Kn.source})(%|[a-z]+)`,"i"),Rf=/rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi,Li=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/,gc=t=>{const[e,r]=If(t);if(!e||ga())return t;const n=window.getComputedStyle(document.documentElement).getPropertyValue(e);if(n)return n.trim();if(r&&r.startsWith("--")){const i=window.getComputedStyle(document.documentElement).getPropertyValue(r);return i||t}else{if(r&&Li.test(r))return gc(r);if(r)return r}return t},If=t=>{const e=Li.exec(t);if(!e)return[,];const[,r,n]=e;return[r,n]},ao,$f=(t,e,r,n,i)=>`rgba(${Math.round(e)}, ${Math.round(r)}, ${Math.round(n)}, ${i})`,vc=t=>{ao||(ao=zt?new RegExp(`(${Object.keys(zt).join("|")})(?!\\w)`,"g"):/^\b$/);const e=t.output.map(o=>Ve(o).replace(Li,gc).replace(Af,ws).replace(ao,ws)),r=e.map(o=>o.match(Kn).map(Number)),i=r[0].map((o,a)=>r.map(s=>{if(!(a in s))throw Error('The arity of each "output" value must be equal');return s[a]})).map(o=>Xr({...t,output:o}));return o=>{var l;const a=!Ss.test(e[0])&&((l=e.find(c=>Ss.test(c)))==null?void 0:l.replace(Kn,""));let s=0;return e[0].replace(Kn,()=>`${i[s++](o)}${a||""}`).replace(Rf,$f)}},wa="react-spring: ",bc=t=>{const e=t;let r=!1;if(typeof e!="function")throw new TypeError(`${wa}once requires a function parameter`);return(...n)=>{r||(e(...n),r=!0)}},zf=bc(console.warn);function jf(){zf(`${wa}The "interpolate" function is deprecated in v9 (use "to" instead)`)}var Lf=bc(console.warn);function Df(){Lf(`${wa}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`)}function Di(t){return P.str(t)&&(t[0]=="#"||/\d/.test(t)||!ga()&&Li.test(t)||t in(zt||{}))}var ya=ga()?q.useEffect:q.useLayoutEffect,Ff=()=>{const t=q.useRef(!1);return ya(()=>(t.current=!0,()=>{t.current=!1}),[]),t};function wc(){const t=q.useState()[1],e=Ff();return()=>{e.current&&t(Math.random())}}var yc=t=>q.useEffect(t,Hf),Hf=[];function Cs(t){const e=q.useRef(void 0);return q.useEffect(()=>{e.current=t}),e.current}var rn=Symbol.for("Animated:node"),Bf=t=>!!t&&t[rn]===t,ut=t=>t&&t[rn],_a=(t,e)=>uf(t,rn,e),Fi=t=>t&&t[rn]&&t[rn].getPayload(),_c=class{constructor(){_a(this,this)}getPayload(){return this.payload||[]}},Hi=class xc extends _c{constructor(e){super(),this._value=e,this.done=!0,this.durationProgress=0,P.num(this._value)&&(this.lastPosition=this._value)}static create(e){return new xc(e)}getPayload(){return[this]}getValue(){return this._value}setValue(e,r){return P.num(e)&&(this.lastPosition=e,r&&(e=Math.round(e/r)*r,this.done&&(this.lastPosition=e))),this._value===e?!1:(this._value=e,!0)}reset(){const{done:e}=this;this.done=!1,P.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,e&&(this.lastVelocity=null),this.v0=null)}},fi=class kc extends Hi{constructor(e){super(0),this._string=null,this._toString=Xr({output:[e,e]})}static create(e){return new kc(e)}getValue(){const e=this._string;return e??(this._string=this._toString(this._value))}setValue(e){if(P.str(e)){if(e==this._string)return!1;this._string=e,this._value=1}else if(super.setValue(e))this._string=null;else return!1;return!0}reset(e){e&&(this._toString=Xr({output:[this.getValue(),e]})),this._value=0,super.reset()}},mi={dependencies:null},Bi=class extends _c{constructor(t){super(),this.source=t,this.setValue(t)}getValue(t){const e={};return mt(this.source,(r,n)=>{Bf(r)?e[n]=r.getValue(t):et(r)?e[n]=Ve(r):t||(e[n]=r)}),e}setValue(t){this.source=t,this.payload=this._makePayload(t)}reset(){this.payload&&de(this.payload,t=>t.reset())}_makePayload(t){if(t){const e=new Set;return mt(t,this._addToPayload,e),Array.from(e)}}_addToPayload(t){mi.dependencies&&et(t)&&mi.dependencies.add(t);const e=Fi(t);e&&de(e,r=>this.add(r))}},Wf=class Sc extends Bi{constructor(e){super(e)}static create(e){return new Sc(e)}getValue(){return this.source.map(e=>e.getValue())}setValue(e){const r=this.getPayload();return e.length==r.length?r.map((n,i)=>n.setValue(e[i])).some(Boolean):(super.setValue(e.map(Vf)),!0)}};function Vf(t){return(Di(t)?fi:Hi).create(t)}function $o(t){const e=ut(t);return e?e.constructor:P.arr(t)?Wf:Di(t)?fi:Hi}var Es=(t,e)=>{const r=!P.fun(t)||t.prototype&&t.prototype.isReactComponent;return q.forwardRef((n,i)=>{const o=q.useRef(null),a=r&&q.useCallback(b=>{o.current=Gf(i,b)},[i]),[s,l]=Uf(n,e),c=wc(),u=()=>{const b=o.current;if(r&&!b)return;(b?e.applyAnimatedValues(b,s.getValue(!0)):!1)===!1&&c()},d=new qf(u,l),p=q.useRef(void 0);ya(()=>(p.current=d,de(l,b=>Tr(b,d)),()=>{p.current&&(de(p.current.deps,b=>tn(b,p.current)),J.cancel(p.current.update))})),q.useEffect(u,[]),yc(()=>()=>{const b=p.current;de(b.deps,f=>tn(f,b))});const h=e.getComponentProps(s.getValue());return q.createElement(t,{...h,ref:a})})},qf=class{constructor(t,e){this.update=t,this.deps=e}eventObserved(t){t.type=="change"&&J.write(this.update)}};function Uf(t,e){const r=new Set;return mi.dependencies=r,t.style&&(t={...t,style:e.createAnimatedStyle(t.style)}),t=new Bi(t),mi.dependencies=null,[t,r]}function Gf(t,e){return t&&(P.fun(t)?t(e):t.current=e),e}var Os=Symbol.for("AnimatedComponent"),Zf=(t,{applyAnimatedValues:e=()=>!1,createAnimatedStyle:r=i=>new Bi(i),getComponentProps:n=i=>i}={})=>{const i={applyAnimatedValues:e,createAnimatedStyle:r,getComponentProps:n},o=a=>{const s=Ns(a)||"Anonymous";return P.str(a)?a=o[a]||(o[a]=Es(a,i)):a=a[Os]||(a[Os]=Es(a,i)),a.displayName=`Animated(${s})`,a};return mt(t,(a,s)=>{P.arr(t)&&(s=Ns(a)),o[s]=o(a)}),{animated:o}},Ns=t=>P.str(t)?t:t&&P.str(t.displayName)?t.displayName:P.fun(t)&&t.name||null;function Yt(t,...e){return P.fun(t)?t(...e):t}var Gr=(t,e)=>t===!0||!!(e&&t&&(P.fun(t)?t(e):Qe(t).includes(e))),Cc=(t,e)=>P.obj(t)?e&&t[e]:t,Ec=(t,e)=>t.default===!0?t[e]:t.default?t.default[e]:void 0,Yf=t=>t,xa=(t,e=Yf)=>{let r=Kf;t.default&&t.default!==!0&&(t=t.default,r=Object.keys(t));const n={};for(const i of r){const o=e(t[i],i);P.und(o)||(n[i]=o)}return n},Kf=["config","onProps","onStart","onChange","onPause","onResume","onRest"],Qf={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function Xf(t){const e={};let r=0;if(mt(t,(n,i)=>{Qf[i]||(e[i]=n,r++)}),r)return e}function Oc(t){const e=Xf(t);if(e){const r={to:e};return mt(t,(n,i)=>i in e||(r[i]=n)),r}return{...t}}function nn(t){return t=Ve(t),P.arr(t)?t.map(nn):Di(t)?lt.createStringInterpolator({range:[0,1],output:[t,t]})(1):t}function Jf(t){for(const e in t)return!0;return!1}function zo(t){return P.fun(t)||P.arr(t)&&P.obj(t[0])}function em(t,e){var r;(r=t.ref)==null||r.delete(t),e==null||e.delete(t)}function tm(t,e){var r;e&&t.ref!==e&&((r=t.ref)==null||r.delete(t),e.add(t),t.ref=e)}var rm={default:{tension:170,friction:26},gentle:{tension:120,friction:14},wobbly:{tension:180,friction:12},stiff:{tension:210,friction:20},slow:{tension:280,friction:60},molasses:{tension:280,friction:120}},jo={...rm.default,mass:1,damping:1,easing:Pf.linear,clamp:!1},nm=class{constructor(){this.velocity=0,Object.assign(this,jo)}};function im(t,e,r){r&&(r={...r},Ps(r,e),e={...r,...e}),Ps(t,e),Object.assign(t,e);for(const a in jo)t[a]==null&&(t[a]=jo[a]);let{frequency:n,damping:i}=t;const{mass:o}=t;return P.und(n)||(n<.01&&(n=.01),i<0&&(i=0),t.tension=Math.pow(2*Math.PI/n,2)*o,t.friction=4*Math.PI*i*o/n),t}function Ps(t,e){if(!P.und(e.decay))t.duration=void 0;else{const r=!P.und(e.tension)||!P.und(e.friction);(r||!P.und(e.frequency)||!P.und(e.damping)||!P.und(e.mass))&&(t.duration=void 0,t.decay=void 0),r&&(t.frequency=void 0)}}var Ts=[],om=class{constructor(){this.changed=!1,this.values=Ts,this.toValues=null,this.fromValues=Ts,this.config=new nm,this.immediate=!1}};function Nc(t,{key:e,props:r,defaultProps:n,state:i,actions:o}){return new Promise((a,s)=>{let l,c,u=Gr(r.cancel??(n==null?void 0:n.cancel),e);if(u)h();else{P.und(r.pause)||(i.paused=Gr(r.pause,e));let b=n==null?void 0:n.pause;b!==!0&&(b=i.paused||Gr(b,e)),l=Yt(r.delay||0,e),b?(i.resumeQueue.add(p),o.pause()):(o.resume(),p())}function d(){i.resumeQueue.add(p),i.timeouts.delete(c),c.cancel(),l=c.time-J.now()}function p(){l>0&&!lt.skipAnimation?(i.delayed=!0,c=J.setTimeout(h,l),i.pauseQueue.add(d),i.timeouts.add(c)):h()}function h(){i.delayed&&(i.delayed=!1),i.pauseQueue.delete(d),i.timeouts.delete(c),t<=(i.cancelId||0)&&(u=!0);try{o.start({...r,callId:t,cancel:u},a)}catch(b){s(b)}}})}var ka=(t,e)=>e.length==1?e[0]:e.some(r=>r.cancelled)?fr(t.get()):e.every(r=>r.noop)?Pc(t.get()):nt(t.get(),e.every(r=>r.finished)),Pc=t=>({value:t,noop:!0,finished:!0,cancelled:!1}),nt=(t,e,r=!1)=>({value:t,finished:e,cancelled:r}),fr=t=>({value:t,cancelled:!0,finished:!1});function Tc(t,e,r,n){const{callId:i,parentId:o,onRest:a}=e,{asyncTo:s,promise:l}=r;return!o&&t===s&&!e.reset?l:r.promise=(async()=>{r.asyncId=i,r.asyncTo=t;const c=xa(e,(w,y)=>y==="onRest"?void 0:w);let u,d;const p=new Promise((w,y)=>(u=w,d=y)),h=w=>{const y=i<=(r.cancelId||0)&&fr(n)||i!==r.asyncId&&nt(n,!1);if(y)throw w.result=y,d(w),w},b=(w,y)=>{const v=new Ms,_=new As;return(async()=>{if(lt.skipAnimation)throw on(r),_.result=nt(n,!1),d(_),_;h(v);const S=P.obj(w)?{...w}:{...y,to:w};S.parentId=i,mt(c,(I,H)=>{P.und(S[H])&&(S[H]=I)});const E=await n.start(S);return h(v),r.paused&&await new Promise(I=>{r.resumeQueue.add(I)}),E})()};let f;if(lt.skipAnimation)return on(r),nt(n,!1);try{let w;P.arr(t)?w=(async y=>{for(const v of y)await b(v)})(t):w=Promise.resolve(t(b,n.stop.bind(n))),await Promise.all([w.then(u),p]),f=nt(n.get(),!0,!1)}catch(w){if(w instanceof Ms)f=w.result;else if(w instanceof As)f=w.result;else throw w}finally{i==r.asyncId&&(r.asyncId=o,r.asyncTo=o?s:void 0,r.promise=o?l:void 0)}return P.fun(a)&&J.batchedUpdates(()=>{a(f,n,n.item)}),f})()}function on(t,e){qr(t.timeouts,r=>r.cancel()),t.pauseQueue.clear(),t.resumeQueue.clear(),t.asyncId=t.asyncTo=t.promise=void 0,e&&(t.cancelId=e)}var Ms=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},As=class extends Error{constructor(){super("SkipAnimationSignal")}},Lo=t=>t instanceof Sa,am=1,Sa=class extends fc{constructor(){super(...arguments),this.id=am++,this._priority=0}get priority(){return this._priority}set priority(t){this._priority!=t&&(this._priority=t,this._onPriorityChange(t))}get(){const t=ut(this);return t&&t.getValue()}to(...t){return lt.to(this,t)}interpolate(...t){return jf(),lt.to(this,t)}toJSON(){return this.get()}observerAdded(t){t==1&&this._attach()}observerRemoved(t){t==0&&this._detach()}_attach(){}_detach(){}_onChange(t,e=!1){en(this,{type:"change",parent:this,value:t,idle:e})}_onPriorityChange(t){this.idle||zi.sort(this),en(this,{type:"priority",parent:this,priority:t})}},tr=Symbol.for("SpringPhase"),Mc=1,Do=2,Fo=4,so=t=>(t[tr]&Mc)>0,Pt=t=>(t[tr]&Do)>0,zr=t=>(t[tr]&Fo)>0,Rs=(t,e)=>e?t[tr]|=Do|Mc:t[tr]&=~Do,Is=(t,e)=>e?t[tr]|=Fo:t[tr]&=~Fo,sm=class extends Sa{constructor(t,e){if(super(),this.animation=new om,this.defaultProps={},this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._pendingCalls=new Set,this._lastCallId=0,this._lastToId=0,this._memoizedDuration=0,!P.und(t)||!P.und(e)){const r=P.obj(t)?{...t}:{...e,from:t};P.und(r.default)&&(r.default=!0),this.start(r)}}get idle(){return!(Pt(this)||this._state.asyncTo)||zr(this)}get goal(){return Ve(this.animation.to)}get velocity(){const t=ut(this);return t instanceof Hi?t.lastVelocity||0:t.getPayload().map(e=>e.lastVelocity||0)}get hasAnimated(){return so(this)}get isAnimating(){return Pt(this)}get isPaused(){return zr(this)}get isDelayed(){return this._state.delayed}advance(t){let e=!0,r=!1;const n=this.animation;let{toValues:i}=n;const{config:o}=n,a=Fi(n.to);!a&&et(n.to)&&(i=Qe(Ve(n.to))),n.values.forEach((c,u)=>{if(c.done)return;const d=c.constructor==fi?1:a?a[u].lastPosition:i[u];let p=n.immediate,h=d;if(!p){if(h=c.lastPosition,o.tension<=0){c.done=!0;return}let b=c.elapsedTime+=t;const f=n.fromValues[u],w=c.v0!=null?c.v0:c.v0=P.arr(o.velocity)?o.velocity[u]:o.velocity;let y;const v=o.precision||(f==d?.005:Math.min(1,Math.abs(d-f)*.001));if(P.und(o.duration))if(o.decay){const _=o.decay===!0?.998:o.decay,S=Math.exp(-(1-_)*b);h=f+w/(1-_)*(1-S),p=Math.abs(c.lastPosition-h)<=v,y=w*S}else{y=c.lastVelocity==null?w:c.lastVelocity;const _=o.restVelocity||v/10,S=o.clamp?0:o.bounce,E=!P.und(S),I=f==d?c.v0>0:f<d;let H,R=!1;const k=1,O=Math.ceil(t/k);for(let $=0;$<O&&(H=Math.abs(y)>_,!(!H&&(p=Math.abs(d-h)<=v,p)));++$){E&&(R=h==d||h>d==I,R&&(y=-y*S,h=d));const j=-o.tension*1e-6*(h-d),N=-o.friction*.001*y,T=(j+N)/o.mass;y=y+T*k,h=h+y*k}}else{let _=1;o.duration>0&&(this._memoizedDuration!==o.duration&&(this._memoizedDuration=o.duration,c.durationProgress>0&&(c.elapsedTime=o.duration*c.durationProgress,b=c.elapsedTime+=t)),_=(o.progress||0)+b/this._memoizedDuration,_=_>1?1:_<0?0:_,c.durationProgress=_),h=f+o.easing(_)*(d-f),y=(h-c.lastPosition)/t,p=_==1}c.lastVelocity=y,Number.isNaN(h)&&(console.warn("Got NaN while animating:",this),p=!0)}a&&!a[u].done&&(p=!1),p?c.done=!0:e=!1,c.setValue(h,o.round)&&(r=!0)});const s=ut(this),l=s.getValue();if(e){const c=Ve(n.to);(l!==c||r)&&!o.decay?(s.setValue(c),this._onChange(c)):r&&o.decay&&this._onChange(l),this._stop()}else r&&this._onChange(l)}set(t){return J.batchedUpdates(()=>{this._stop(),this._focus(t),this._set(t)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(Pt(this)){const{to:t,config:e}=this.animation;J.batchedUpdates(()=>{this._onStart(),e.decay||this._set(t,!1),this._stop()})}return this}update(t){return(this.queue||(this.queue=[])).push(t),this}start(t,e){let r;return P.und(t)?(r=this.queue||[],this.queue=[]):r=[P.obj(t)?t:{...e,to:t}],Promise.all(r.map(n=>this._update(n))).then(n=>ka(this,n))}stop(t){const{to:e}=this.animation;return this._focus(this.get()),on(this._state,t&&this._lastCallId),J.batchedUpdates(()=>this._stop(e,t)),this}reset(){this._update({reset:!0})}eventObserved(t){t.type=="change"?this._start():t.type=="priority"&&(this.priority=t.priority+1)}_prepareNode(t){const e=this.key||"";let{to:r,from:n}=t;r=P.obj(r)?r[e]:r,(r==null||zo(r))&&(r=void 0),n=P.obj(n)?n[e]:n,n==null&&(n=void 0);const i={to:r,from:n};return so(this)||(t.reverse&&([r,n]=[n,r]),n=Ve(n),P.und(n)?ut(this)||this._set(r):this._set(n)),i}_update({...t},e){const{key:r,defaultProps:n}=this;t.default&&Object.assign(n,xa(t,(a,s)=>/^on/.test(s)?Cc(a,r):a)),zs(this,t,"onProps"),Lr(this,"onProps",t,this);const i=this._prepareNode(t);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");const o=this._state;return Nc(++this._lastCallId,{key:r,props:t,defaultProps:n,state:o,actions:{pause:()=>{zr(this)||(Is(this,!0),Fr(o.pauseQueue),Lr(this,"onPause",nt(this,jr(this,this.animation.to)),this))},resume:()=>{zr(this)&&(Is(this,!1),Pt(this)&&this._resume(),Fr(o.resumeQueue),Lr(this,"onResume",nt(this,jr(this,this.animation.to)),this))},start:this._merge.bind(this,i)}}).then(a=>{if(t.loop&&a.finished&&!(e&&a.noop)){const s=Ac(t);if(s)return this._update(s,!0)}return a})}_merge(t,e,r){if(e.cancel)return this.stop(!0),r(fr(this));const n=!P.und(t.to),i=!P.und(t.from);if(n||i)if(e.callId>this._lastToId)this._lastToId=e.callId;else return r(fr(this));const{key:o,defaultProps:a,animation:s}=this,{to:l,from:c}=s;let{to:u=l,from:d=c}=t;i&&!n&&(!e.default||P.und(u))&&(u=d),e.reverse&&([u,d]=[d,u]);const p=!xt(d,c);p&&(s.from=d),d=Ve(d);const h=!xt(u,l);h&&this._focus(u);const b=zo(e.to),{config:f}=s,{decay:w,velocity:y}=f;(n||i)&&(f.velocity=0),e.config&&!b&&im(f,Yt(e.config,o),e.config!==a.config?Yt(a.config,o):void 0);let v=ut(this);if(!v||P.und(u))return r(nt(this,!0));const _=P.und(e.reset)?i&&!e.default:!P.und(d)&&Gr(e.reset,o),S=_?d:this.get(),E=nn(u),I=P.num(E)||P.arr(E)||Di(E),H=!b&&(!I||Gr(a.immediate||e.immediate,o));if(h){const $=$o(u);if($!==v.constructor)if(H)v=this._set(E);else throw Error(`Cannot animate between ${v.constructor.name} and ${$.name}, as the "to" prop suggests`)}const R=v.constructor;let k=et(u),O=!1;if(!k){const $=_||!so(this)&&p;(h||$)&&(O=xt(nn(S),E),k=!O),(!xt(s.immediate,H)&&!H||!xt(f.decay,w)||!xt(f.velocity,y))&&(k=!0)}if(O&&Pt(this)&&(s.changed&&!_?k=!0:k||this._stop(l)),!b&&((k||et(l))&&(s.values=v.getPayload(),s.toValues=et(u)?null:R==fi?[1]:Qe(E)),s.immediate!=H&&(s.immediate=H,!H&&!_&&this._set(l)),k)){const{onRest:$}=s;de(cm,N=>zs(this,e,N));const j=nt(this,jr(this,l));Fr(this._pendingCalls,j),this._pendingCalls.add(r),s.changed&&J.batchedUpdates(()=>{var N;s.changed=!_,$==null||$(j,this),_?Yt(a.onRest,j):(N=s.onStart)==null||N.call(s,j,this)})}_&&this._set(S),b?r(Tc(e.to,e,this._state,this)):k?this._start():Pt(this)&&!h?this._pendingCalls.add(r):r(Pc(S))}_focus(t){const e=this.animation;t!==e.to&&(ks(this)&&this._detach(),e.to=t,ks(this)&&this._attach())}_attach(){let t=0;const{to:e}=this.animation;et(e)&&(Tr(e,this),Lo(e)&&(t=e.priority+1)),this.priority=t}_detach(){const{to:t}=this.animation;et(t)&&tn(t,this)}_set(t,e=!0){const r=Ve(t);if(!P.und(r)){const n=ut(this);if(!n||!xt(r,n.getValue())){const i=$o(r);!n||n.constructor!=i?_a(this,i.create(r)):n.setValue(r),n&&J.batchedUpdates(()=>{this._onChange(r,e)})}}return ut(this)}_onStart(){const t=this.animation;t.changed||(t.changed=!0,Lr(this,"onStart",nt(this,jr(this,t.to)),this))}_onChange(t,e){e||(this._onStart(),Yt(this.animation.onChange,t,this)),Yt(this.defaultProps.onChange,t,this),super._onChange(t,e)}_start(){const t=this.animation;ut(this).reset(Ve(t.to)),t.immediate||(t.fromValues=t.values.map(e=>e.lastPosition)),Pt(this)||(Rs(this,!0),zr(this)||this._resume())}_resume(){lt.skipAnimation?this.finish():zi.start(this)}_stop(t,e){if(Pt(this)){Rs(this,!1);const r=this.animation;de(r.values,i=>{i.done=!0}),r.toValues&&(r.onChange=r.onPause=r.onResume=void 0),en(this,{type:"idle",parent:this});const n=e?fr(this.get()):nt(this.get(),jr(this,t??r.to));Fr(this._pendingCalls,n),r.changed&&(r.changed=!1,Lr(this,"onRest",n,this))}}};function jr(t,e){const r=nn(e),n=nn(t.get());return xt(n,r)}function Ac(t,e=t.loop,r=t.to){const n=Yt(e);if(n){const i=n!==!0&&Oc(n),o=(i||t).reverse,a=!i||i.reset;return an({...t,loop:e,default:!1,pause:void 0,to:!o||zo(r)?r:void 0,from:a?t.from:void 0,reset:a,...i})}}function an(t){const{to:e,from:r}=t=Oc(t),n=new Set;return P.obj(e)&&$s(e,n),P.obj(r)&&$s(r,n),t.keys=n.size?Array.from(n):null,t}function lm(t){const e=an(t);return P.und(e.default)&&(e.default=xa(e)),e}function $s(t,e){mt(t,(r,n)=>r!=null&&e.add(n))}var cm=["onStart","onRest","onChange","onPause","onResume"];function zs(t,e,r){t.animation[r]=e[r]!==Ec(e,r)?Cc(e[r],t.key):void 0}function Lr(t,e,...r){var n,i,o,a;(i=(n=t.animation)[e])==null||i.call(n,...r),(a=(o=t.defaultProps)[e])==null||a.call(o,...r)}var dm=["onStart","onChange","onRest"],um=1,pm=class{constructor(t,e){this.id=um++,this.springs={},this.queue=[],this._lastAsyncId=0,this._active=new Set,this._changed=new Set,this._started=!1,this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._events={onStart:new Map,onChange:new Map,onRest:new Map},this._onFrame=this._onFrame.bind(this),e&&(this._flush=e),t&&this.start({default:!0,...t})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(t=>t.idle&&!t.isDelayed&&!t.isPaused)}get item(){return this._item}set item(t){this._item=t}get(){const t={};return this.each((e,r)=>t[r]=e.get()),t}set(t){for(const e in t){const r=t[e];P.und(r)||this.springs[e].set(r)}}update(t){return t&&this.queue.push(an(t)),this}start(t){let{queue:e}=this;return t?e=Qe(t).map(an):this.queue=[],this._flush?this._flush(this,e):(jc(this,e),Ho(this,e))}stop(t,e){if(t!==!!t&&(e=t),e){const r=this.springs;de(Qe(e),n=>r[n].stop(!!t))}else on(this._state,this._lastAsyncId),this.each(r=>r.stop(!!t));return this}pause(t){if(P.und(t))this.start({pause:!0});else{const e=this.springs;de(Qe(t),r=>e[r].pause())}return this}resume(t){if(P.und(t))this.start({pause:!1});else{const e=this.springs;de(Qe(t),r=>e[r].resume())}return this}each(t){mt(this.springs,t)}_onFrame(){const{onStart:t,onChange:e,onRest:r}=this._events,n=this._active.size>0,i=this._changed.size>0;(n&&!this._started||i&&!this._started)&&(this._started=!0,qr(t,([s,l])=>{l.value=this.get(),s(l,this,this._item)}));const o=!n&&this._started,a=i||o&&r.size?this.get():null;i&&e.size&&qr(e,([s,l])=>{l.value=a,s(l,this,this._item)}),o&&(this._started=!1,qr(r,([s,l])=>{l.value=a,s(l,this,this._item)}))}eventObserved(t){if(t.type=="change")this._changed.add(t.parent),t.idle||this._active.add(t.parent);else if(t.type=="idle")this._active.delete(t.parent);else return;J.onFrame(this._onFrame)}};function Ho(t,e){return Promise.all(e.map(r=>Rc(t,r))).then(r=>ka(t,r))}async function Rc(t,e,r){const{keys:n,to:i,from:o,loop:a,onRest:s,onResolve:l}=e,c=P.obj(e.default)&&e.default;a&&(e.loop=!1),i===!1&&(e.to=null),o===!1&&(e.from=null);const u=P.arr(i)||P.fun(i)?i:void 0;u?(e.to=void 0,e.onRest=void 0,c&&(c.onRest=void 0)):de(dm,f=>{const w=e[f];if(P.fun(w)){const y=t._events[f];e[f]=({finished:v,cancelled:_})=>{const S=y.get(w);S?(v||(S.finished=!1),_&&(S.cancelled=!0)):y.set(w,{value:null,finished:v||!1,cancelled:_||!1})},c&&(c[f]=e[f])}});const d=t._state;e.pause===!d.paused?(d.paused=e.pause,Fr(e.pause?d.pauseQueue:d.resumeQueue)):d.paused&&(e.pause=!0);const p=(n||Object.keys(t.springs)).map(f=>t.springs[f].start(e)),h=e.cancel===!0||Ec(e,"cancel")===!0;(u||h&&d.asyncId)&&p.push(Nc(++t._lastAsyncId,{props:e,state:d,actions:{pause:Ro,resume:Ro,start(f,w){h?(on(d,t._lastAsyncId),w(fr(t))):(f.onRest=s,w(Tc(u,f,d,t)))}}})),d.paused&&await new Promise(f=>{d.resumeQueue.add(f)});const b=ka(t,await Promise.all(p));if(a&&b.finished&&!(r&&b.noop)){const f=Ac(e,a,i);if(f)return jc(t,[f]),Rc(t,f,!0)}return l&&J.batchedUpdates(()=>l(b,t,t.item)),b}function js(t,e){const r={...t.springs};return e&&de(Qe(e),n=>{P.und(n.keys)&&(n=an(n)),P.obj(n.to)||(n={...n,to:void 0}),zc(r,n,i=>$c(i))}),Ic(t,r),r}function Ic(t,e){mt(e,(r,n)=>{t.springs[n]||(t.springs[n]=r,Tr(r,t))})}function $c(t,e){const r=new sm;return r.key=t,e&&Tr(r,e),r}function zc(t,e,r){e.keys&&de(e.keys,n=>{(t[n]||(t[n]=r(n)))._prepareNode(e)})}function jc(t,e){de(e,r=>{zc(t.springs,r,n=>$c(n,t))})}var hm=q.createContext({pause:!1,immediate:!1}),fm=()=>{const t=[],e=function(n){Df();const i=[];return de(t,(o,a)=>{if(P.und(n))i.push(o.start());else{const s=r(n,o,a);s&&i.push(o.start(s))}}),i};e.current=t,e.add=function(n){t.includes(n)||t.push(n)},e.delete=function(n){const i=t.indexOf(n);~i&&t.splice(i,1)},e.pause=function(){return de(t,n=>n.pause(...arguments)),this},e.resume=function(){return de(t,n=>n.resume(...arguments)),this},e.set=function(n){de(t,(i,o)=>{const a=P.fun(n)?n(o,i):n;a&&i.set(a)})},e.start=function(n){const i=[];return de(t,(o,a)=>{if(P.und(n))i.push(o.start());else{const s=this._getProps(n,o,a);s&&i.push(o.start(s))}}),i},e.stop=function(){return de(t,n=>n.stop(...arguments)),this},e.update=function(n){return de(t,(i,o)=>i.update(this._getProps(n,i,o))),this};const r=function(n,i,o){return P.fun(n)?n(o,i):n};return e._getProps=r,e};function mm(t,e,r){const n=P.fun(e)&&e;n&&!r&&(r=[]);const i=q.useMemo(()=>n||arguments.length==3?fm():void 0,[]),o=q.useRef(0),a=wc(),s=q.useMemo(()=>({ctrls:[],queue:[],flush(y,v){const _=js(y,v);return o.current>0&&!s.queue.length&&!Object.keys(_).some(E=>!y.springs[E])?Ho(y,v):new Promise(E=>{Ic(y,_),s.queue.push(()=>{E(Ho(y,v))}),a()})}}),[]),l=q.useRef([...s.ctrls]),c=q.useRef([]),u=Cs(t)||0;q.useMemo(()=>{de(l.current.slice(t,u),y=>{em(y,i),y.stop(!0)}),l.current.length=t,d(u,t)},[t]),q.useMemo(()=>{d(0,Math.min(u,t))},r);function d(y,v){for(let _=y;_<v;_++){const S=l.current[_]||(l.current[_]=new pm(null,s.flush)),E=n?n(_,S):e[_];E&&(c.current[_]=lm(E))}}const p=l.current.map((y,v)=>js(y,c.current[v])),h=q.useContext(hm),b=Cs(h),f=h!==b&&Jf(h);ya(()=>{o.current++,s.ctrls=l.current;const{queue:y}=s;y.length&&(s.queue=[],de(y,v=>v())),de(l.current,(v,_)=>{i==null||i.add(v),f&&v.start({default:h});const S=c.current[_];S&&(tm(v,S.ref),v.ref?v.queue.push(S):v.start(S))})}),yc(()=>()=>{de(s.ctrls,y=>y.stop(!0))});const w=p.map(y=>({...y}));return i?[w,i]:w}function gm(t,e){const r=P.fun(t),[[n],i]=mm(1,r?t:[t],r?e||[]:e);return r||arguments.length==2?[n,i]:n}var vm=class extends Sa{constructor(t,e){super(),this.source=t,this.idle=!0,this._active=new Set,this.calc=Xr(...e);const r=this._get(),n=$o(r);_a(this,n.create(r))}advance(t){const e=this._get(),r=this.get();xt(e,r)||(ut(this).setValue(e),this._onChange(e,this.idle)),!this.idle&&Ls(this._active)&&lo(this)}_get(){const t=P.arr(this.source)?this.source.map(Ve):Qe(Ve(this.source));return this.calc(...t)}_start(){this.idle&&!Ls(this._active)&&(this.idle=!1,de(Fi(this),t=>{t.done=!1}),lt.skipAnimation?(J.batchedUpdates(()=>this.advance()),lo(this)):zi.start(this))}_attach(){let t=1;de(Qe(this.source),e=>{et(e)&&Tr(e,this),Lo(e)&&(e.idle||this._active.add(e),t=Math.max(t,e.priority+1))}),this.priority=t,this._start()}_detach(){de(Qe(this.source),t=>{et(t)&&tn(t,this)}),this._active.clear(),lo(this)}eventObserved(t){t.type=="change"?t.idle?this.advance():(this._active.add(t.parent),this._start()):t.type=="idle"?this._active.delete(t.parent):t.type=="priority"&&(this.priority=Qe(this.source).reduce((e,r)=>Math.max(e,(Lo(r)?r.priority:0)+1),0))}};function bm(t){return t.idle!==!1}function Ls(t){return!t.size||Array.from(t).every(bm)}function lo(t){t.idle||(t.idle=!0,de(Fi(t),e=>{e.done=!0}),en(t,{type:"idle",parent:t}))}lt.assign({createStringInterpolator:vc,to:(t,e)=>new vm(t,e)});var Lc=/^--/;function wm(t,e){return e==null||typeof e=="boolean"||e===""?"":typeof e=="number"&&e!==0&&!Lc.test(t)&&!(Zr.hasOwnProperty(t)&&Zr[t])?e+"px":(""+e).trim()}var Ds={};function ym(t,e){if(!t.nodeType||!t.setAttribute)return!1;const r=t.nodeName==="filter"||t.parentNode&&t.parentNode.nodeName==="filter",{className:n,style:i,children:o,scrollTop:a,scrollLeft:s,viewBox:l,...c}=e,u=Object.values(c),d=Object.keys(c).map(p=>r||t.hasAttribute(p)?p:Ds[p]||(Ds[p]=p.replace(/([A-Z])/g,h=>"-"+h.toLowerCase())));o!==void 0&&(t.textContent=o);for(const p in i)if(i.hasOwnProperty(p)){const h=wm(p,i[p]);Lc.test(p)?t.style.setProperty(p,h):t.style[p]=h}d.forEach((p,h)=>{t.setAttribute(p,u[h])}),n!==void 0&&(t.className=n),a!==void 0&&(t.scrollTop=a),s!==void 0&&(t.scrollLeft=s),l!==void 0&&t.setAttribute("viewBox",l)}var Zr={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},_m=(t,e)=>t+e.charAt(0).toUpperCase()+e.substring(1),xm=["Webkit","Ms","Moz","O"];Zr=Object.keys(Zr).reduce((t,e)=>(xm.forEach(r=>t[_m(r,e)]=t[e]),t),Zr);var km=/^(matrix|translate|scale|rotate|skew)/,Sm=/^(translate)/,Cm=/^(rotate|skew)/,co=(t,e)=>P.num(t)&&t!==0?t+e:t,Qn=(t,e)=>P.arr(t)?t.every(r=>Qn(r,e)):P.num(t)?t===e:parseFloat(t)===e,Em=class extends Bi{constructor({x:t,y:e,z:r,...n}){const i=[],o=[];(t||e||r)&&(i.push([t||0,e||0,r||0]),o.push(a=>[`translate3d(${a.map(s=>co(s,"px")).join(",")})`,Qn(a,0)])),mt(n,(a,s)=>{if(s==="transform")i.push([a||""]),o.push(l=>[l,l===""]);else if(km.test(s)){if(delete n[s],P.und(a))return;const l=Sm.test(s)?"px":Cm.test(s)?"deg":"";i.push(Qe(a)),o.push(s==="rotate3d"?([c,u,d,p])=>[`rotate3d(${c},${u},${d},${co(p,l)})`,Qn(p,0)]:c=>[`${s}(${c.map(u=>co(u,l)).join(",")})`,Qn(c,s.startsWith("scale")?1:0)])}}),i.length&&(n.transform=new Om(i,o)),super(n)}},Om=class extends fc{constructor(t,e){super(),this.inputs=t,this.transforms=e,this._value=null}get(){return this._value||(this._value=this._get())}_get(){let t="",e=!0;return de(this.inputs,(r,n)=>{const i=Ve(r[0]),[o,a]=this.transforms[n](P.arr(i)?i:r.map(Ve));t+=" "+o,e=e&&a}),e?"none":t}observerAdded(t){t==1&&de(this.inputs,e=>de(e,r=>et(r)&&Tr(r,this)))}observerRemoved(t){t==0&&de(this.inputs,e=>de(e,r=>et(r)&&tn(r,this)))}eventObserved(t){t.type=="change"&&(this._value=null),en(this,t)}},Nm=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"];lt.assign({batchedUpdates:yl.unstable_batchedUpdates,createStringInterpolator:vc,colors:gf});var Pm=Zf(Nm,{applyAnimatedValues:ym,createAnimatedStyle:t=>new Em(t),getComponentProps:({scrollTop:t,scrollLeft:e,...r})=>r}),Tm=Pm.animated;const Dc=t=>{const r={"Cloud & DevOps":Eu,Backend:Ou,Frontend:qa,"AI & ML":Nu,Architecture:Pu,Tools:Tu,Analytics:Mu,"System Design":Au}[t]||qa;return g.jsx(r,{})},Mm=({skill:t,category:e})=>{const[r,n]=gm(()=>({xys:[0,0,1],config:{mass:5,tension:350,friction:40}})),i=(a,s)=>[-(s-window.innerHeight/2)/20,(a-window.innerWidth/2)/20,1.1],o=(a,s,l)=>`perspective(600px) rotateX(${a}deg) rotateY(${s}deg) scale(${l})`;return g.jsxs(Tm.div,{className:"skill-card",onMouseMove:({clientX:a,clientY:s})=>n.start({xys:i(a,s)}),onMouseLeave:()=>n.start({xys:[0,0,1]}),style:{transform:r.xys.to(o)},children:[g.jsx("div",{className:"skill-icon",children:Dc(e)}),g.jsx("h3",{children:t})]})},Am=()=>{const t=qe.getFormattedSkills(),[e,r]=q.useState(Object.keys(t)[0]),[n,i]=q.useState(0),o=q.useRef(null),a=6,s=t[e]||[],l=Math.ceil(s.length/a),c=()=>{i(p=>(p+1)%l)},u=()=>{i(p=>(p-1+l)%l)},d=s.slice(n*a,(n+1)*a).map(p=>({skill:p}));return g.jsx("section",{id:"skills",className:"skills-section",children:g.jsxs("div",{className:"skills-container",ref:o,children:[g.jsxs("div",{className:"skills-header",children:[g.jsx("h2",{children:"Technical Skills"}),g.jsx("p",{children:"Explore my technical expertise across different domains"})]}),g.jsx("div",{className:"category-tabs",children:Object.keys(t).map(p=>g.jsxs("button",{className:`category-tab ${e===p?"active":""}`,onClick:()=>{r(p),i(0)},children:[Dc(p),g.jsx("span",{children:p})]},p))}),g.jsx("div",{className:"skills-grid",children:d.map(({skill:p,proficiency:h},b)=>g.jsx(Mm,{skill:p,category:e},`${p}-${b}`))}),l>1&&g.jsxs("div",{className:"pagination-controls",children:[g.jsx("button",{className:"nav-button prev",onClick:u,disabled:n===0,children:g.jsx(Su,{})}),g.jsxs("span",{className:"page-indicator",children:[n+1," / ",l]}),g.jsx("button",{className:"nav-button next",onClick:c,disabled:n===l-1,children:g.jsx(Cu,{})})]})]})})};const Rm=()=>{qe.getEducation();const t=qe.getFormattedCertificates(),e=qe.getAwards();return g.jsx("section",{id:"education",className:"education",children:g.jsxs("div",{className:"container",children:[g.jsx("h2",{className:"section-title",children:"Certifications & Awards"}),g.jsxs("div",{className:"education-grid",children:[g.jsx(it,{delay:.2,children:g.jsxs("div",{className:"education-card",children:[g.jsx("h3",{className:"card-title",children:"Certifications"}),g.jsx("ul",{className:"certification-list",children:t.map((r,n)=>g.jsxs("li",{children:[g.jsx("strong",{children:r.name}),r.issuer&&g.jsxs("span",{children:[" - ",r.issuer]}),r.date&&g.jsxs("span",{className:"cert-date",children:[" (",r.date,")"]})]},n))})]})}),g.jsx(it,{delay:.4,children:g.jsxs("div",{className:"education-card",children:[g.jsx("h3",{className:"card-title",children:"Awards"}),e.map((r,n)=>g.jsxs("div",{className:"award-item",children:[g.jsx("h4",{children:r.title}),g.jsx("p",{children:r.summary}),r.date&&g.jsx("span",{className:"award-date",children:r.date})]},n))]})})]})]})})};const Im=()=>{const t=qe.getFormattedPublications();return g.jsx("section",{id:"publications",className:"publications",children:g.jsxs("div",{className:"container",children:[g.jsx("h2",{className:"section-title",children:"Publications & Thought Leadership"}),g.jsx("div",{className:"publications-grid",children:t.map((e,r)=>g.jsx(it,{delay:r*.1,children:g.jsxs("div",{className:"publication-item",children:[g.jsx("h3",{children:e.name}),e.publisher&&g.jsx("p",{className:"publisher",children:e.publisher}),e.url&&g.jsx("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"publication-link",children:"Read More"})]})},r))})]})})};const $m=()=>{var u,d,p,h,b,f,w;const t=qe.getBasics(),{isMobile:e}=kl(),[r,n]=q.useState({name:"",email:"",subject:"",message:""}),[i,o]=q.useState(!1),[a,s]=q.useState(null),l=y=>{const{name:v,value:_}=y.target;n(S=>({...S,[v]:_}))},c=async y=>{y.preventDefault(),o(!0),s(null);try{await new Promise(v=>setTimeout(v,1e3)),console.log("Form submitted:",r),s("success"),n({name:"",email:"",subject:"",message:""}),e&&navigator.vibrate&&navigator.vibrate(100)}catch(v){s("error"),console.error("Form submission error:",v)}finally{o(!1)}};return g.jsx("section",{id:"contact",className:"contact",children:g.jsxs("div",{className:"container",children:[g.jsx("h2",{className:"section-title",children:"Get In Touch"}),g.jsxs("div",{className:"contact-content",children:[g.jsxs("div",{className:"contact-info-section",children:[g.jsx("h3",{children:"Contact Information"}),g.jsxs("div",{className:"contact-details",children:[g.jsxs("div",{className:"contact-detail",children:[g.jsx("strong",{children:"Location:"})," ",(u=t.location)==null?void 0:u.city,", ",(d=t.location)==null?void 0:d.region,", ",(p=t.location)==null?void 0:p.countryCode]}),g.jsxs("div",{className:"contact-detail",children:[g.jsx("strong",{children:"LinkedIn:"})," ",g.jsx("a",{href:(b=(h=t.profiles)==null?void 0:h.find(y=>y.network==="LinkedIn"))==null?void 0:b.url,target:"_blank",rel:"noopener noreferrer",children:(w=(f=t.profiles)==null?void 0:f.find(y=>y.network==="LinkedIn"))==null?void 0:w.url})]})]})]}),g.jsxs("div",{className:"contact-form-section",children:[g.jsx("h3",{children:"Send a Message"}),g.jsxs("form",{className:"contact-form",id:"contact-form",onSubmit:c,children:[g.jsxs("div",{className:"form-group",children:[g.jsx("label",{htmlFor:"name",className:"form-label",children:"Name"}),g.jsx("input",{type:"text",id:"name",name:"name",className:"form-control",required:!0,value:r.name,onChange:l})]}),g.jsxs("div",{className:"form-group",children:[g.jsx("label",{htmlFor:"email",className:"form-label",children:"Email"}),g.jsx("input",{type:"email",id:"email",name:"email",className:"form-control",required:!0,value:r.email,onChange:l})]}),g.jsxs("div",{className:"form-group",children:[g.jsx("label",{htmlFor:"subject",className:"form-label",children:"Subject"}),g.jsx("input",{type:"text",id:"subject",name:"subject",className:"form-control",required:!0,value:r.subject,onChange:l})]}),g.jsxs("div",{className:"form-group",children:[g.jsx("label",{htmlFor:"message",className:"form-label",children:"Message"}),g.jsx("textarea",{id:"message",name:"message",rows:"5",className:"form-control",required:!0,value:r.message,onChange:l})]}),g.jsx("button",{type:"submit",className:`btn btn--primary btn--full-width ${i?"btn--loading":""}`,disabled:i,children:i?g.jsxs(g.Fragment,{children:[g.jsx("span",{className:"btn-spinner"}),"Sending..."]}):"Send Message"}),a==="success"&&g.jsx("div",{className:"form-message form-message--success",children:"✅ Thank you! Your message has been sent successfully."}),a==="error"&&g.jsx("div",{className:"form-message form-message--error",children:"❌ Sorry, there was an error sending your message. Please try again."})]})]})]})]})})},zm=()=>g.jsx("footer",{className:"footer",children:g.jsx("div",{className:"container",children:g.jsx("p",{children:"© 2025 Nicholas Gerasimatos. All rights reserved."})})});var En,re,Fc,Kt,Fs,Hc,Bc,Wc,Ca,Bo,Wo,Vc,sn={},qc=[],jm=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,On=Array.isArray;function pt(t,e){for(var r in e)t[r]=e[r];return t}function Ea(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function Ge(t,e,r){var n,i,o,a={};for(o in e)o=="key"?n=e[o]:o=="ref"?i=e[o]:a[o]=e[o];if(arguments.length>2&&(a.children=arguments.length>3?En.call(arguments,2):r),typeof t=="function"&&t.defaultProps!=null)for(o in t.defaultProps)a[o]==null&&(a[o]=t.defaultProps[o]);return Yr(t,a,n,i,null)}function Yr(t,e,r,n,i){var o={type:t,props:e,key:r,ref:n,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:i??++Fc,__i:-1,__u:0};return i==null&&re.vnode!=null&&re.vnode(o),o}function Vo(){return{current:null}}function Ue(t){return t.children}function ht(t,e){this.props=t,this.context=e}function kr(t,e){if(e==null)return t.__?kr(t.__,t.__i+1):null;for(var r;e<t.__k.length;e++)if((r=t.__k[e])!=null&&r.__e!=null)return r.__e;return typeof t.type=="function"?kr(t):null}function Uc(t){var e,r;if((t=t.__)!=null&&t.__c!=null){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if((r=t.__k[e])!=null&&r.__e!=null){t.__e=t.__c.base=r.__e;break}return Uc(t)}}function qo(t){(!t.__d&&(t.__d=!0)&&Kt.push(t)&&!gi.__r++||Fs!=re.debounceRendering)&&((Fs=re.debounceRendering)||Hc)(gi)}function gi(){for(var t,e,r,n,i,o,a,s=1;Kt.length;)Kt.length>s&&Kt.sort(Bc),t=Kt.shift(),s=Kt.length,t.__d&&(r=void 0,i=(n=(e=t).__v).__e,o=[],a=[],e.__P&&((r=pt({},n)).__v=n.__v+1,re.vnode&&re.vnode(r),Oa(e.__P,r,n,e.__n,e.__P.namespaceURI,32&n.__u?[i]:null,o,i??kr(n),!!(32&n.__u),a),r.__v=n.__v,r.__.__k[r.__i]=r,Yc(o,r,a),r.__e!=i&&Uc(r)));gi.__r=0}function Gc(t,e,r,n,i,o,a,s,l,c,u){var d,p,h,b,f,w,y=n&&n.__k||qc,v=e.length;for(l=Lm(r,e,y,l,v),d=0;d<v;d++)(h=r.__k[d])!=null&&(p=h.__i==-1?sn:y[h.__i]||sn,h.__i=d,w=Oa(t,h,p,i,o,a,s,l,c,u),b=h.__e,h.ref&&p.ref!=h.ref&&(p.ref&&Na(p.ref,null,h),u.push(h.ref,h.__c||b,h)),f==null&&b!=null&&(f=b),4&h.__u||p.__k===h.__k?l=Zc(h,l,t):typeof h.type=="function"&&w!==void 0?l=w:b&&(l=b.nextSibling),h.__u&=-7);return r.__e=f,l}function Lm(t,e,r,n,i){var o,a,s,l,c,u=r.length,d=u,p=0;for(t.__k=new Array(i),o=0;o<i;o++)(a=e[o])!=null&&typeof a!="boolean"&&typeof a!="function"?(l=o+p,(a=t.__k[o]=typeof a=="string"||typeof a=="number"||typeof a=="bigint"||a.constructor==String?Yr(null,a,null,null,null):On(a)?Yr(Ue,{children:a},null,null,null):a.constructor==null&&a.__b>0?Yr(a.type,a.props,a.key,a.ref?a.ref:null,a.__v):a).__=t,a.__b=t.__b+1,s=null,(c=a.__i=Dm(a,r,l,d))!=-1&&(d--,(s=r[c])&&(s.__u|=2)),s==null||s.__v==null?(c==-1&&(i>u?p--:i<u&&p++),typeof a.type!="function"&&(a.__u|=4)):c!=l&&(c==l-1?p--:c==l+1?p++:(c>l?p--:p++,a.__u|=4))):t.__k[o]=null;if(d)for(o=0;o<u;o++)(s=r[o])!=null&&!(2&s.__u)&&(s.__e==n&&(n=kr(s)),Qc(s,s));return n}function Zc(t,e,r){var n,i;if(typeof t.type=="function"){for(n=t.__k,i=0;n&&i<n.length;i++)n[i]&&(n[i].__=t,e=Zc(n[i],e,r));return e}t.__e!=e&&(e&&t.type&&!r.contains(e)&&(e=kr(t)),r.insertBefore(t.__e,e||null),e=t.__e);do e=e&&e.nextSibling;while(e!=null&&e.nodeType==8);return e}function St(t,e){return e=e||[],t==null||typeof t=="boolean"||(On(t)?t.some(function(r){St(r,e)}):e.push(t)),e}function Dm(t,e,r,n){var i,o,a=t.key,s=t.type,l=e[r];if(l===null&&t.key==null||l&&a==l.key&&s==l.type&&!(2&l.__u))return r;if(n>(l!=null&&!(2&l.__u)?1:0))for(i=r-1,o=r+1;i>=0||o<e.length;){if(i>=0){if((l=e[i])&&!(2&l.__u)&&a==l.key&&s==l.type)return i;i--}if(o<e.length){if((l=e[o])&&!(2&l.__u)&&a==l.key&&s==l.type)return o;o++}}return-1}function Hs(t,e,r){e[0]=="-"?t.setProperty(e,r??""):t[e]=r==null?"":typeof r!="number"||jm.test(e)?r:r+"px"}function Bn(t,e,r,n,i){var o;e:if(e=="style")if(typeof r=="string")t.style.cssText=r;else{if(typeof n=="string"&&(t.style.cssText=n=""),n)for(e in n)r&&e in r||Hs(t.style,e,"");if(r)for(e in r)n&&r[e]==n[e]||Hs(t.style,e,r[e])}else if(e[0]=="o"&&e[1]=="n")o=e!=(e=e.replace(Wc,"$1")),e=e.toLowerCase()in t||e=="onFocusOut"||e=="onFocusIn"?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+o]=r,r?n?r.u=n.u:(r.u=Ca,t.addEventListener(e,o?Wo:Bo,o)):t.removeEventListener(e,o?Wo:Bo,o);else{if(i=="http://www.w3.org/2000/svg")e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if(e!="width"&&e!="height"&&e!="href"&&e!="list"&&e!="form"&&e!="tabIndex"&&e!="download"&&e!="rowSpan"&&e!="colSpan"&&e!="role"&&e!="popover"&&e in t)try{t[e]=r??"";break e}catch{}typeof r=="function"||(r==null||r===!1&&e[4]!="-"?t.removeAttribute(e):t.setAttribute(e,e=="popover"&&r==1?"":r))}}function Bs(t){return function(e){if(this.l){var r=this.l[e.type+t];if(e.t==null)e.t=Ca++;else if(e.t<r.u)return;return r(re.event?re.event(e):e)}}}function Oa(t,e,r,n,i,o,a,s,l,c){var u,d,p,h,b,f,w,y,v,_,S,E,I,H,R,k,O,$=e.type;if(e.constructor!=null)return null;128&r.__u&&(l=!!(32&r.__u),o=[s=e.__e=r.__e]),(u=re.__b)&&u(e);e:if(typeof $=="function")try{if(y=e.props,v="prototype"in $&&$.prototype.render,_=(u=$.contextType)&&n[u.__c],S=u?_?_.props.value:u.__:n,r.__c?w=(d=e.__c=r.__c).__=d.__E:(v?e.__c=d=new $(y,S):(e.__c=d=new ht(y,S),d.constructor=$,d.render=Hm),_&&_.sub(d),d.props=y,d.state||(d.state={}),d.context=S,d.__n=n,p=d.__d=!0,d.__h=[],d._sb=[]),v&&d.__s==null&&(d.__s=d.state),v&&$.getDerivedStateFromProps!=null&&(d.__s==d.state&&(d.__s=pt({},d.__s)),pt(d.__s,$.getDerivedStateFromProps(y,d.__s))),h=d.props,b=d.state,d.__v=e,p)v&&$.getDerivedStateFromProps==null&&d.componentWillMount!=null&&d.componentWillMount(),v&&d.componentDidMount!=null&&d.__h.push(d.componentDidMount);else{if(v&&$.getDerivedStateFromProps==null&&y!==h&&d.componentWillReceiveProps!=null&&d.componentWillReceiveProps(y,S),!d.__e&&d.shouldComponentUpdate!=null&&d.shouldComponentUpdate(y,d.__s,S)===!1||e.__v==r.__v){for(e.__v!=r.__v&&(d.props=y,d.state=d.__s,d.__d=!1),e.__e=r.__e,e.__k=r.__k,e.__k.some(function(j){j&&(j.__=e)}),E=0;E<d._sb.length;E++)d.__h.push(d._sb[E]);d._sb=[],d.__h.length&&a.push(d);break e}d.componentWillUpdate!=null&&d.componentWillUpdate(y,d.__s,S),v&&d.componentDidUpdate!=null&&d.__h.push(function(){d.componentDidUpdate(h,b,f)})}if(d.context=S,d.props=y,d.__P=t,d.__e=!1,I=re.__r,H=0,v){for(d.state=d.__s,d.__d=!1,I&&I(e),u=d.render(d.props,d.state,d.context),R=0;R<d._sb.length;R++)d.__h.push(d._sb[R]);d._sb=[]}else do d.__d=!1,I&&I(e),u=d.render(d.props,d.state,d.context),d.state=d.__s;while(d.__d&&++H<25);d.state=d.__s,d.getChildContext!=null&&(n=pt(pt({},n),d.getChildContext())),v&&!p&&d.getSnapshotBeforeUpdate!=null&&(f=d.getSnapshotBeforeUpdate(h,b)),k=u,u!=null&&u.type===Ue&&u.key==null&&(k=Kc(u.props.children)),s=Gc(t,On(k)?k:[k],e,r,n,i,o,a,s,l,c),d.base=e.__e,e.__u&=-161,d.__h.length&&a.push(d),w&&(d.__E=d.__=null)}catch(j){if(e.__v=null,l||o!=null)if(j.then){for(e.__u|=l?160:128;s&&s.nodeType==8&&s.nextSibling;)s=s.nextSibling;o[o.indexOf(s)]=null,e.__e=s}else for(O=o.length;O--;)Ea(o[O]);else e.__e=r.__e,e.__k=r.__k;re.__e(j,e,r)}else o==null&&e.__v==r.__v?(e.__k=r.__k,e.__e=r.__e):s=e.__e=Fm(r.__e,e,r,n,i,o,a,l,c);return(u=re.diffed)&&u(e),128&e.__u?void 0:s}function Yc(t,e,r){for(var n=0;n<r.length;n++)Na(r[n],r[++n],r[++n]);re.__c&&re.__c(e,t),t.some(function(i){try{t=i.__h,i.__h=[],t.some(function(o){o.call(i)})}catch(o){re.__e(o,i.__v)}})}function Kc(t){return typeof t!="object"||t==null||t.__b&&t.__b>0?t:On(t)?t.map(Kc):pt({},t)}function Fm(t,e,r,n,i,o,a,s,l){var c,u,d,p,h,b,f,w=r.props,y=e.props,v=e.type;if(v=="svg"?i="http://www.w3.org/2000/svg":v=="math"?i="http://www.w3.org/1998/Math/MathML":i||(i="http://www.w3.org/1999/xhtml"),o!=null){for(c=0;c<o.length;c++)if((h=o[c])&&"setAttribute"in h==!!v&&(v?h.localName==v:h.nodeType==3)){t=h,o[c]=null;break}}if(t==null){if(v==null)return document.createTextNode(y);t=document.createElementNS(i,v,y.is&&y),s&&(re.__m&&re.__m(e,o),s=!1),o=null}if(v==null)w===y||s&&t.data==y||(t.data=y);else{if(o=o&&En.call(t.childNodes),w=r.props||sn,!s&&o!=null)for(w={},c=0;c<t.attributes.length;c++)w[(h=t.attributes[c]).name]=h.value;for(c in w)if(h=w[c],c!="children"){if(c=="dangerouslySetInnerHTML")d=h;else if(!(c in y)){if(c=="value"&&"defaultValue"in y||c=="checked"&&"defaultChecked"in y)continue;Bn(t,c,null,h,i)}}for(c in y)h=y[c],c=="children"?p=h:c=="dangerouslySetInnerHTML"?u=h:c=="value"?b=h:c=="checked"?f=h:s&&typeof h!="function"||w[c]===h||Bn(t,c,h,w[c],i);if(u)s||d&&(u.__html==d.__html||u.__html==t.innerHTML)||(t.innerHTML=u.__html),e.__k=[];else if(d&&(t.innerHTML=""),Gc(e.type=="template"?t.content:t,On(p)?p:[p],e,r,n,v=="foreignObject"?"http://www.w3.org/1999/xhtml":i,o,a,o?o[0]:r.__k&&kr(r,0),s,l),o!=null)for(c=o.length;c--;)Ea(o[c]);s||(c="value",v=="progress"&&b==null?t.removeAttribute("value"):b!=null&&(b!==t[c]||v=="progress"&&!b||v=="option"&&b!=w[c])&&Bn(t,c,b,w[c],i),c="checked",f!=null&&f!=t[c]&&Bn(t,c,f,w[c],i))}return t}function Na(t,e,r){try{if(typeof t=="function"){var n=typeof t.__u=="function";n&&t.__u(),n&&e==null||(t.__u=t(e))}else t.current=e}catch(i){re.__e(i,r)}}function Qc(t,e,r){var n,i;if(re.unmount&&re.unmount(t),(n=t.ref)&&(n.current&&n.current!=t.__e||Na(n,null,e)),(n=t.__c)!=null){if(n.componentWillUnmount)try{n.componentWillUnmount()}catch(o){re.__e(o,e)}n.base=n.__P=null}if(n=t.__k)for(i=0;i<n.length;i++)n[i]&&Qc(n[i],e,r||typeof t.type!="function");r||Ea(t.__e),t.__c=t.__=t.__e=void 0}function Hm(t,e,r){return this.constructor(t,r)}function Sr(t,e,r){var n,i,o,a;e==document&&(e=document.documentElement),re.__&&re.__(t,e),i=(n=typeof r=="function")?null:r&&r.__k||e.__k,o=[],a=[],Oa(e,t=(!n&&r||e).__k=Ge(Ue,null,[t]),i||sn,sn,e.namespaceURI,!n&&r?[r]:i?null:e.firstChild?En.call(e.childNodes):null,o,!n&&r?r:i?i.__e:e.firstChild,n,a),Yc(o,t,a)}function Xc(t,e){Sr(t,e,Xc)}function Bm(t,e,r){var n,i,o,a,s=pt({},t.props);for(o in t.type&&t.type.defaultProps&&(a=t.type.defaultProps),e)o=="key"?n=e[o]:o=="ref"?i=e[o]:s[o]=e[o]==null&&a!=null?a[o]:e[o];return arguments.length>2&&(s.children=arguments.length>3?En.call(arguments,2):r),Yr(t.type,s,n||t.key,i||t.ref,null)}function rt(t){function e(r){var n,i;return this.getChildContext||(n=new Set,(i={})[e.__c]=this,this.getChildContext=function(){return i},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(o){this.props.value!=o.value&&n.forEach(function(a){a.__e=!0,qo(a)})},this.sub=function(o){n.add(o);var a=o.componentWillUnmount;o.componentWillUnmount=function(){n&&n.delete(o),a&&a.call(o)}}),r.children}return e.__c="__cC"+Vc++,e.__=t,e.Provider=e.__l=(e.Consumer=function(r,n){return r.children(n)}).contextType=e,e}En=qc.slice,re={__e:function(t,e,r,n){for(var i,o,a;e=e.__;)if((i=e.__c)&&!i.__)try{if((o=i.constructor)&&o.getDerivedStateFromError!=null&&(i.setState(o.getDerivedStateFromError(t)),a=i.__d),i.componentDidCatch!=null&&(i.componentDidCatch(t,n||{}),a=i.__d),a)return i.__E=i}catch(s){t=s}throw t}},Fc=0,ht.prototype.setState=function(t,e){var r;r=this.__s!=null&&this.__s!=this.state?this.__s:this.__s=pt({},this.state),typeof t=="function"&&(t=t(pt({},r),this.props)),t&&pt(r,t),t!=null&&this.__v&&(e&&this._sb.push(e),qo(this))},ht.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),qo(this))},ht.prototype.render=Ue,Kt=[],Hc=typeof Promise=="function"?Promise.prototype.then.bind(Promise.resolve()):setTimeout,Bc=function(t,e){return t.__v.__b-e.__v.__b},gi.__r=0,Wc=/(PointerCapture)$|Capture$/i,Ca=0,Bo=Bs(!1),Wo=Bs(!0),Vc=0;var Wm=0;function m(t,e,r,n,i,o){e||(e={});var a,s,l=e;if("ref"in l)for(s in l={},e)s=="ref"?a=e[s]:l[s]=e[s];var c={type:t,props:l,key:r,ref:a,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:--Wm,__i:-1,__u:0,__source:i,__self:o};if(typeof t=="function"&&(a=t.defaultProps))for(s in a)l[s]===void 0&&(l[s]=a[s]);return re.vnode&&re.vnode(c),c}var jt,_e,uo,Ws,Cr=0,Jc=[],Ee=re,Vs=Ee.__b,qs=Ee.__r,Us=Ee.diffed,Gs=Ee.__c,Zs=Ee.unmount,Ys=Ee.__;function Mr(t,e){Ee.__h&&Ee.__h(_e,t,Cr||e),Cr=0;var r=_e.__H||(_e.__H={__:[],__h:[]});return t>=r.__.length&&r.__.push({}),r.__[t]}function me(t){return Cr=1,Pa(rd,t)}function Pa(t,e,r){var n=Mr(jt++,2);if(n.t=t,!n.__c&&(n.__=[r?r(e):rd(void 0,e),function(s){var l=n.__N?n.__N[0]:n.__[0],c=n.t(l,s);l!==c&&(n.__N=[c,n.__[1]],n.__c.setState({}))}],n.__c=_e,!_e.__f)){var i=function(s,l,c){if(!n.__c.__H)return!0;var u=n.__c.__H.__.filter(function(p){return!!p.__c});if(u.every(function(p){return!p.__N}))return!o||o.call(this,s,l,c);var d=n.__c.props!==s;return u.forEach(function(p){if(p.__N){var h=p.__[0];p.__=p.__N,p.__N=void 0,h!==p.__[0]&&(d=!0)}}),o&&o.call(this,s,l,c)||d};_e.__f=!0;var o=_e.shouldComponentUpdate,a=_e.componentWillUpdate;_e.componentWillUpdate=function(s,l,c){if(this.__e){var u=o;o=void 0,i(s,l,c),o=u}a&&a.call(this,s,l,c)},_e.shouldComponentUpdate=i}return n.__N||n.__}function xe(t,e){var r=Mr(jt++,3);!Ee.__s&&Ta(r.__H,e)&&(r.__=t,r.u=e,_e.__H.__h.push(r))}function Ar(t,e){var r=Mr(jt++,4);!Ee.__s&&Ta(r.__H,e)&&(r.__=t,r.u=e,_e.__h.push(r))}function ce(t){return Cr=5,Ne(function(){return{current:t}},[])}function ed(t,e,r){Cr=6,Ar(function(){if(typeof t=="function"){var n=t(e());return function(){t(null),n&&typeof n=="function"&&n()}}if(t)return t.current=e(),function(){return t.current=null}},r==null?r:r.concat(t))}function Ne(t,e){var r=Mr(jt++,7);return Ta(r.__H,e)&&(r.__=t(),r.__H=e,r.__h=t),r.__}function V(t,e){return Cr=8,Ne(function(){return t},e)}function Le(t){var e=_e.context[t.__c],r=Mr(jt++,9);return r.c=t,e?(r.__==null&&(r.__=!0,e.sub(_e)),e.props.value):t.__}function td(t,e){Ee.useDebugValue&&Ee.useDebugValue(e?e(t):t)}function Nn(){var t=Mr(jt++,11);if(!t.__){for(var e=_e.__v;e!==null&&!e.__m&&e.__!==null;)e=e.__;var r=e.__m||(e.__m=[0,0]);t.__="P"+r[0]+"-"+r[1]++}return t.__}function Vm(){for(var t;t=Jc.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(Xn),t.__H.__h.forEach(Uo),t.__H.__h=[]}catch(e){t.__H.__h=[],Ee.__e(e,t.__v)}}Ee.__b=function(t){_e=null,Vs&&Vs(t)},Ee.__=function(t,e){t&&e.__k&&e.__k.__m&&(t.__m=e.__k.__m),Ys&&Ys(t,e)},Ee.__r=function(t){qs&&qs(t),jt=0;var e=(_e=t.__c).__H;e&&(uo===_e?(e.__h=[],_e.__h=[],e.__.forEach(function(r){r.__N&&(r.__=r.__N),r.u=r.__N=void 0})):(e.__h.forEach(Xn),e.__h.forEach(Uo),e.__h=[],jt=0)),uo=_e},Ee.diffed=function(t){Us&&Us(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(Jc.push(e)!==1&&Ws===Ee.requestAnimationFrame||((Ws=Ee.requestAnimationFrame)||qm)(Vm)),e.__H.__.forEach(function(r){r.u&&(r.__H=r.u),r.u=void 0})),uo=_e=null},Ee.__c=function(t,e){e.some(function(r){try{r.__h.forEach(Xn),r.__h=r.__h.filter(function(n){return!n.__||Uo(n)})}catch(n){e.some(function(i){i.__h&&(i.__h=[])}),e=[],Ee.__e(n,r.__v)}}),Gs&&Gs(t,e)},Ee.unmount=function(t){Zs&&Zs(t);var e,r=t.__c;r&&r.__H&&(r.__H.__.forEach(function(n){try{Xn(n)}catch(i){e=i}}),r.__H=void 0,e&&Ee.__e(e,r.__v))};var Ks=typeof requestAnimationFrame=="function";function qm(t){var e,r=function(){clearTimeout(n),Ks&&cancelAnimationFrame(e),setTimeout(t)},n=setTimeout(r,100);Ks&&(e=requestAnimationFrame(r))}function Xn(t){var e=_e,r=t.__c;typeof r=="function"&&(t.__c=void 0,r()),_e=e}function Uo(t){var e=_e;t.__c=t.__(),_e=e}function Ta(t,e){return!t||t.length!==e.length||e.some(function(r,n){return r!==t[n]})}function rd(t,e){return typeof e=="function"?e(t):e}function nd(t,e){for(var r in e)t[r]=e[r];return t}function Go(t,e){for(var r in t)if(r!=="__source"&&!(r in e))return!0;for(var n in e)if(n!=="__source"&&t[n]!==e[n])return!0;return!1}function id(t,e){var r=e(),n=me({t:{__:r,u:e}}),i=n[0].t,o=n[1];return Ar(function(){i.__=r,i.u=e,po(i)&&o({t:i})},[t,r,e]),xe(function(){return po(i)&&o({t:i}),t(function(){po(i)&&o({t:i})})},[t]),r}function po(t){var e,r,n=t.u,i=t.__;try{var o=n();return!((e=i)===(r=o)&&(e!==0||1/e==1/r)||e!=e&&r!=r)}catch{return!0}}function od(t){t()}function ad(t){return t}function sd(){return[!1,od]}var ld=Ar;function Zo(t,e){this.props=t,this.context=e}function Um(t,e){function r(i){var o=this.props.ref,a=o==i.ref;return!a&&o&&(o.call?o(null):o.current=null),e?!e(this.props,i)||!a:Go(this.props,i)}function n(i){return this.shouldComponentUpdate=r,Ge(t,i)}return n.displayName="Memo("+(t.displayName||t.name)+")",n.prototype.isReactComponent=!0,n.__f=!0,n}(Zo.prototype=new ht).isPureReactComponent=!0,Zo.prototype.shouldComponentUpdate=function(t,e){return Go(this.props,t)||Go(this.state,e)};var Qs=re.__b;re.__b=function(t){t.type&&t.type.__f&&t.ref&&(t.props.ref=t.ref,t.ref=null),Qs&&Qs(t)};var Gm=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function Pn(t){function e(r){var n=nd({},r);return delete n.ref,t(n,r.ref||null)}return e.$$typeof=Gm,e.render=e,e.prototype.isReactComponent=e.__f=!0,e.displayName="ForwardRef("+(t.displayName||t.name)+")",e}var Xs=function(t,e){return t==null?null:St(St(t).map(e))},Zm={map:Xs,forEach:Xs,count:function(t){return t?St(t).length:0},only:function(t){var e=St(t);if(e.length!==1)throw"Children.only";return e[0]},toArray:St},Ym=re.__e;re.__e=function(t,e,r,n){if(t.then){for(var i,o=e;o=o.__;)if((i=o.__c)&&i.__c)return e.__e==null&&(e.__e=r.__e,e.__k=r.__k),i.__c(t,e)}Ym(t,e,r,n)};var Js=re.unmount;function cd(t,e,r){return t&&(t.__c&&t.__c.__H&&(t.__c.__H.__.forEach(function(n){typeof n.__c=="function"&&n.__c()}),t.__c.__H=null),(t=nd({},t)).__c!=null&&(t.__c.__P===r&&(t.__c.__P=e),t.__c.__e=!0,t.__c=null),t.__k=t.__k&&t.__k.map(function(n){return cd(n,e,r)})),t}function dd(t,e,r){return t&&r&&(t.__v=null,t.__k=t.__k&&t.__k.map(function(n){return dd(n,e,r)}),t.__c&&t.__c.__P===e&&(t.__e&&r.appendChild(t.__e),t.__c.__e=!0,t.__c.__P=r)),t}function Jn(){this.__u=0,this.o=null,this.__b=null}function ud(t){var e=t.__.__c;return e&&e.__a&&e.__a(t)}function Km(t){var e,r,n;function i(o){if(e||(e=t()).then(function(a){r=a.default||a},function(a){n=a}),n)throw n;if(!r)throw e;return Ge(r,o)}return i.displayName="Lazy",i.__f=!0,i}function Hr(){this.i=null,this.l=null}re.unmount=function(t){var e=t.__c;e&&e.__R&&e.__R(),e&&32&t.__u&&(t.type=null),Js&&Js(t)},(Jn.prototype=new ht).__c=function(t,e){var r=e.__c,n=this;n.o==null&&(n.o=[]),n.o.push(r);var i=ud(n.__v),o=!1,a=function(){o||(o=!0,r.__R=null,i?i(s):s())};r.__R=a;var s=function(){if(!--n.__u){if(n.state.__a){var l=n.state.__a;n.__v.__k[0]=dd(l,l.__c.__P,l.__c.__O)}var c;for(n.setState({__a:n.__b=null});c=n.o.pop();)c.forceUpdate()}};n.__u++||32&e.__u||n.setState({__a:n.__b=n.__v.__k[0]}),t.then(a,a)},Jn.prototype.componentWillUnmount=function(){this.o=[]},Jn.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var r=document.createElement("div"),n=this.__v.__k[0].__c;this.__v.__k[0]=cd(this.__b,r,n.__O=n.__P)}this.__b=null}var i=e.__a&&Ge(Ue,null,t.fallback);return i&&(i.__u&=-33),[Ge(Ue,null,e.__a?null:t.children),i]};var el=function(t,e,r){if(++r[1]===r[0]&&t.l.delete(e),t.props.revealOrder&&(t.props.revealOrder[0]!=="t"||!t.l.size))for(r=t.i;r;){for(;r.length>3;)r.pop()();if(r[1]<r[0])break;t.i=r=r[2]}};function Qm(t){return this.getChildContext=function(){return t.context},t.children}function Xm(t){var e=this,r=t.h;if(e.componentWillUnmount=function(){Sr(null,e.v),e.v=null,e.h=null},e.h&&e.h!==r&&e.componentWillUnmount(),!e.v){for(var n=e.__v;n!==null&&!n.__m&&n.__!==null;)n=n.__;e.h=r,e.v={nodeType:1,parentNode:r,childNodes:[],__k:{__m:n.__m},contains:function(){return!0},appendChild:function(i){this.childNodes.push(i),e.h.appendChild(i)},insertBefore:function(i,o){this.childNodes.push(i),e.h.insertBefore(i,o)},removeChild:function(i){this.childNodes.splice(this.childNodes.indexOf(i)>>>1,1),e.h.removeChild(i)}}}Sr(Ge(Qm,{context:e.context},t.__v),e.v)}function Jm(t,e){var r=Ge(Xm,{__v:t,h:e});return r.containerInfo=e,r}(Hr.prototype=new ht).__a=function(t){var e=this,r=ud(e.__v),n=e.l.get(t);return n[0]++,function(i){var o=function(){e.props.revealOrder?(n.push(i),el(e,t,n)):i()};r?r(o):o()}},Hr.prototype.render=function(t){this.i=null,this.l=new Map;var e=St(t.children);t.revealOrder&&t.revealOrder[0]==="b"&&e.reverse();for(var r=e.length;r--;)this.l.set(e[r],this.i=[1,0,this.i]);return t.children},Hr.prototype.componentDidUpdate=Hr.prototype.componentDidMount=function(){var t=this;this.l.forEach(function(e,r){el(t,r,e)})};var pd=typeof Symbol<"u"&&Symbol.for&&Symbol.for("react.element")||60103,eg=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,tg=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,rg=/[A-Z0-9]/g,ng=typeof document<"u",ig=function(t){return(typeof Symbol<"u"&&typeof Symbol()=="symbol"?/fil|che|rad/:/fil|che|ra/).test(t)};function og(t,e,r){return e.__k==null&&(e.textContent=""),Sr(t,e),typeof r=="function"&&r(),t?t.__c:null}function ag(t,e,r){return Xc(t,e),typeof r=="function"&&r(),t?t.__c:null}ht.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(ht.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(e){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:e})}})});var tl=re.event;function sg(){}function lg(){return this.cancelBubble}function cg(){return this.defaultPrevented}re.event=function(t){return tl&&(t=tl(t)),t.persist=sg,t.isPropagationStopped=lg,t.isDefaultPrevented=cg,t.nativeEvent=t};var Ma,dg={enumerable:!1,configurable:!0,get:function(){return this.class}},rl=re.vnode;re.vnode=function(t){typeof t.type=="string"&&function(e){var r=e.props,n=e.type,i={},o=n.indexOf("-")===-1;for(var a in r){var s=r[a];if(!(a==="value"&&"defaultValue"in r&&s==null||ng&&a==="children"&&n==="noscript"||a==="class"||a==="className")){var l=a.toLowerCase();a==="defaultValue"&&"value"in r&&r.value==null?a="value":a==="download"&&s===!0?s="":l==="translate"&&s==="no"?s=!1:l[0]==="o"&&l[1]==="n"?l==="ondoubleclick"?a="ondblclick":l!=="onchange"||n!=="input"&&n!=="textarea"||ig(r.type)?l==="onfocus"?a="onfocusin":l==="onblur"?a="onfocusout":tg.test(a)&&(a=l):l=a="oninput":o&&eg.test(a)?a=a.replace(rg,"-$&").toLowerCase():s===null&&(s=void 0),l==="oninput"&&i[a=l]&&(a="oninputCapture"),i[a]=s}}n=="select"&&i.multiple&&Array.isArray(i.value)&&(i.value=St(r.children).forEach(function(c){c.props.selected=i.value.indexOf(c.props.value)!=-1})),n=="select"&&i.defaultValue!=null&&(i.value=St(r.children).forEach(function(c){c.props.selected=i.multiple?i.defaultValue.indexOf(c.props.value)!=-1:i.defaultValue==c.props.value})),r.class&&!r.className?(i.class=r.class,Object.defineProperty(i,"className",dg)):(r.className&&!r.class||r.class&&r.className)&&(i.class=i.className=r.className),e.props=i}(t),t.$$typeof=pd,rl&&rl(t)};var nl=re.__r;re.__r=function(t){nl&&nl(t),Ma=t.__c};var il=re.diffed;re.diffed=function(t){il&&il(t);var e=t.props,r=t.__e;r!=null&&t.type==="textarea"&&"value"in e&&e.value!==r.value&&(r.value=e.value==null?"":e.value),Ma=null};var ug={ReactCurrentDispatcher:{current:{readContext:function(t){return Ma.__n[t.__c].props.value},useCallback:V,useContext:Le,useDebugValue:td,useDeferredValue:ad,useEffect:xe,useId:Nn,useImperativeHandle:ed,useInsertionEffect:ld,useLayoutEffect:Ar,useMemo:Ne,useReducer:Pa,useRef:ce,useState:me,useSyncExternalStore:id,useTransition:sd}}};function pg(t){return Ge.bind(null,t)}function Tn(t){return!!t&&t.$$typeof===pd}function hg(t){return Tn(t)&&t.type===Ue}function fg(t){return!!t&&!!t.displayName&&(typeof t.displayName=="string"||t.displayName instanceof String)&&t.displayName.startsWith("Memo(")}function hd(t){return Tn(t)?Bm.apply(null,arguments):t}function mg(t){return!!t.__k&&(Sr(null,t),!0)}function gg(t){return t&&(t.base||t.nodeType===1&&t)||null}var vg=function(t,e){return t(e)},bg=function(t,e){return t(e)},wg=Ue,yg=Tn,Aa={useState:me,useId:Nn,useReducer:Pa,useEffect:xe,useLayoutEffect:Ar,useInsertionEffect:ld,useTransition:sd,useDeferredValue:ad,useSyncExternalStore:id,startTransition:od,useRef:ce,useImperativeHandle:ed,useMemo:Ne,useCallback:V,useContext:Le,useDebugValue:td,version:"18.3.1",Children:Zm,render:og,hydrate:ag,unmountComponentAtNode:mg,createPortal:Jm,createElement:Ge,createContext:rt,createFactory:pg,cloneElement:hd,createRef:Vo,Fragment:Ue,isValidElement:Tn,isElement:yg,isFragment:hg,isMemo:fg,findDOMNode:gg,Component:ht,PureComponent:Zo,memo:Um,forwardRef:Pn,flushSync:bg,unstable_batchedUpdates:vg,StrictMode:wg,Suspense:Jn,SuspenseList:Hr,lazy:Km,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:ug},_g=(t=16)=>Math.random().toString(36).substring(2,t+2),xg={maxReconnectAttempts:5,reconnectDelay:1e3,requestTimeout:3e4},kg=class{constructor(t={}){this.ws=null,this.pendingRequests=new Map,this.reconnectAttempts=0,this.methods={},this.isIntentionalClose=!1,this.options={...xg,...t}}register(t){Object.entries(t).forEach(([e,r])=>{this.methods[e]={handler:r}})}callMethod(t,e,r){if(!this.ws)throw new Error("WebSocket is not connected");const n=_g(),i={id:n,messageType:"request",method:t,payload:e};return new Promise((o,a)=>{var s;const l=setTimeout(()=>{this.pendingRequests.delete(n),a(new Error(`Request timed out: ${t}`))},this.options.requestTimeout);this.pendingRequests.set(n,{resolve:o,reject:a,timeout:l,onUpdate:r}),(s=this.ws)==null||s.send(JSON.stringify(i))})}setupWebSocketHandlers(t){t.onmessage=e=>{try{const r=JSON.parse(e.data);this.handleMessage(r)}catch(r){console.error("Error handling WebSocket message:",r)}},t.onclose=()=>{this.handleDisconnect()},t.onerror=e=>{console.error("WebSocket error:",e)}}handleMessage(t){const{messageType:e,id:r}=t;switch(e){case"request":this.handleRequest(t);break;case"response":this.handleResponse(r,t.payload);break;case"update":this.handleUpdate(r,t.payload);break;case"error":this.handleError(r,t.error.message);break;default:console.warn(`Unknown message type: ${e}`)}}async handleRequest(t){const{id:e,method:r,payload:n}=t;if(!r){this.sendError(e,"Method name is required");return}const i=this.methods[r];if(!i){this.sendError(e,`Method not found: ${r}`);return}try{const o=s=>{this.sendUpdate(e,r,s)},a=await i.handler(n,o);this.sendResponse(e,r,a)}catch(o){this.sendError(e,o instanceof Error?o.message:String(o))}}handleResponse(t,e){const r=this.pendingRequests.get(t);if(!r){console.warn(`Received response for unknown request ID: ${t}`);return}clearTimeout(r.timeout),this.pendingRequests.delete(t),r.resolve(e)}handleUpdate(t,e){const r=this.pendingRequests.get(t);if(!r||!r.onUpdate){console.warn(`Received update for unknown request ID: ${t}`);return}r.onUpdate(e)}handleError(t,e){const r=this.pendingRequests.get(t);if(!r){console.warn(`Received error for unknown request ID: ${t}`);return}clearTimeout(r.timeout),this.pendingRequests.delete(t),r.reject(new Error(e))}sendResponse(t,e,r){if(!this.ws)throw new Error("WebSocket is not connected");const n={id:t,messageType:"response",method:e,payload:r};this.ws.send(JSON.stringify(n))}sendUpdate(t,e,r){if(!this.ws)throw new Error("WebSocket is not connected");const n={id:t,messageType:"update",method:e,payload:r};this.ws.send(JSON.stringify(n))}sendError(t,e){if(!this.ws)throw new Error("WebSocket is not connected");const r={id:t,messageType:"error",error:{message:e}};this.ws.send(JSON.stringify(r))}handleDisconnect(){if(this.isIntentionalClose){console.log("WebSocket closed intentionally, not attempting to reconnect"),this.clearPendingRequests(new Error("Connection closed by user"));return}this.reconnectAttempts<this.options.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.options.maxReconnectAttempts})...`),setTimeout(()=>this.reconnect(),this.options.reconnectDelay*this.reconnectAttempts)):(console.error("Max reconnection attempts reached"),this.clearPendingRequests(new Error("Connection closed")))}clearPendingRequests(t){this.pendingRequests.forEach(({reject:e})=>{e(t)}),this.pendingRequests.clear()}async close(){this.isIntentionalClose=!0,this.ws&&(this.ws.close(),this.ws=null),this.clearPendingRequests(new Error("Connection closed by user"))}};function sr(t,e,r,n=!1){const i=t.safeParse(e);if(!i.success){const o=new Error(`Validation failed for ${r}: ${i.error.message}`);if(n)return console.error(o),e;throw o}return i.data}var Sg=class{constructor(t,e){this.bridge=t,this.contract=e,this.call=new Proxy({},{get:(r,n)=>(i,o)=>this.callMethod(n,i,o)})}async callMethod(t,e,r){const n=this.contract.consumes[t];if(!n)throw new Error(`Method ${String(t)} not found in contract`);const i=sr(n.request,e,`request for method ${String(t)}`),o=r!=null&&r.onUpdate&&n.update?s=>{var l;if(n.update)try{const c=sr(n.update,s,`update for method ${String(t)}`,!0);(l=r.onUpdate)==null||l.call(r,c)}catch(c){console.error("Update validation failed:",c)}}:void 0,a=await this.bridge.callMethod(t,i,o);return sr(n.response,a,`response for method ${String(t)}`)}register(t){const e={};for(const[r,n]of Object.entries(t)){const i=this.contract.serves[r];if(!i)throw new Error(`Method ${r} not found in contract`);e[r]=async(o,a)=>{const s=sr(i.request,o,`request for method ${r}`),l=i.update&&a?u=>{if(i.update)try{const d=sr(i.update,u,`update for method ${r}`,!0);a(d)}catch(d){console.error("Update validation failed:",d)}}:void 0,c=await n(s,{sendUpdate:l});return sr(i.response,c,`response for method ${r}`)}}this.bridge.register(e)}async close(){await this.bridge.close()}},Cg=class extends kg{constructor(t,e){super(e),this.reconnectTimer=null,this.url=t}call(t,e,r){return this.callMethod(t,e,r)}reconnect(){this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectTimer=setTimeout(async()=>{try{await this.connect()}catch{this.reconnect()}},this.options.reconnectDelay)}connect(){return new Promise((t,e)=>{try{const r=new window.WebSocket(this.url);r.onopen=()=>{this.ws=r,this.setupWebSocketHandlers(r),t()},r.onerror=()=>{e(new Error("Failed to connect to WebSocket server"))}}catch(r){e(r)}})}},Eg=class extends Sg{constructor(t,e,r){super(new Cg(t,r),{serves:e.client||{},consumes:e.server||{}})}connect(){return this.bridge.connect()}};function fd(t,e,r){return new Eg(t,e,r)}var ue;(function(t){t.assertEqual=i=>i;function e(i){}t.assertIs=e;function r(i){throw new Error}t.assertNever=r,t.arrayToEnum=i=>{const o={};for(const a of i)o[a]=a;return o},t.getValidEnumValues=i=>{const o=t.objectKeys(i).filter(s=>typeof i[i[s]]!="number"),a={};for(const s of o)a[s]=i[s];return t.objectValues(a)},t.objectValues=i=>t.objectKeys(i).map(function(o){return i[o]}),t.objectKeys=typeof Object.keys=="function"?i=>Object.keys(i):i=>{const o=[];for(const a in i)Object.prototype.hasOwnProperty.call(i,a)&&o.push(a);return o},t.find=(i,o)=>{for(const a of i)if(o(a))return a},t.isInteger=typeof Number.isInteger=="function"?i=>Number.isInteger(i):i=>typeof i=="number"&&isFinite(i)&&Math.floor(i)===i;function n(i,o=" | "){return i.map(a=>typeof a=="string"?`'${a}'`:a).join(o)}t.joinValues=n,t.jsonStringifyReplacer=(i,o)=>typeof o=="bigint"?o.toString():o})(ue||(ue={}));var Yo;(function(t){t.mergeShapes=(e,r)=>({...e,...r})})(Yo||(Yo={}));const z=ue.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),kt=t=>{switch(typeof t){case"undefined":return z.undefined;case"string":return z.string;case"number":return isNaN(t)?z.nan:z.number;case"boolean":return z.boolean;case"function":return z.function;case"bigint":return z.bigint;case"symbol":return z.symbol;case"object":return Array.isArray(t)?z.array:t===null?z.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?z.promise:typeof Map<"u"&&t instanceof Map?z.map:typeof Set<"u"&&t instanceof Set?z.set:typeof Date<"u"&&t instanceof Date?z.date:z.object;default:return z.unknown}},C=ue.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),Og=t=>JSON.stringify(t,null,2).replace(/"([^"]+)":/g,"$1:");class Xe extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const r=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,r):this.__proto__=r,this.name="ZodError",this.issues=e}format(e){const r=e||function(o){return o.message},n={_errors:[]},i=o=>{for(const a of o.issues)if(a.code==="invalid_union")a.unionErrors.map(i);else if(a.code==="invalid_return_type")i(a.returnTypeError);else if(a.code==="invalid_arguments")i(a.argumentsError);else if(a.path.length===0)n._errors.push(r(a));else{let s=n,l=0;for(;l<a.path.length;){const c=a.path[l];l===a.path.length-1?(s[c]=s[c]||{_errors:[]},s[c]._errors.push(r(a))):s[c]=s[c]||{_errors:[]},s=s[c],l++}}};return i(this),n}static assert(e){if(!(e instanceof Xe))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ue.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=r=>r.message){const r={},n=[];for(const i of this.issues)i.path.length>0?(r[i.path[0]]=r[i.path[0]]||[],r[i.path[0]].push(e(i))):n.push(e(i));return{formErrors:n,fieldErrors:r}}get formErrors(){return this.flatten()}}Xe.create=t=>new Xe(t);const Er=(t,e)=>{let r;switch(t.code){case C.invalid_type:t.received===z.undefined?r="Required":r=`Expected ${t.expected}, received ${t.received}`;break;case C.invalid_literal:r=`Invalid literal value, expected ${JSON.stringify(t.expected,ue.jsonStringifyReplacer)}`;break;case C.unrecognized_keys:r=`Unrecognized key(s) in object: ${ue.joinValues(t.keys,", ")}`;break;case C.invalid_union:r="Invalid input";break;case C.invalid_union_discriminator:r=`Invalid discriminator value. Expected ${ue.joinValues(t.options)}`;break;case C.invalid_enum_value:r=`Invalid enum value. Expected ${ue.joinValues(t.options)}, received '${t.received}'`;break;case C.invalid_arguments:r="Invalid function arguments";break;case C.invalid_return_type:r="Invalid function return type";break;case C.invalid_date:r="Invalid date";break;case C.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(r=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(r=`${r} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?r=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?r=`Invalid input: must end with "${t.validation.endsWith}"`:ue.assertNever(t.validation):t.validation!=="regex"?r=`Invalid ${t.validation}`:r="Invalid";break;case C.too_small:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:r="Invalid input";break;case C.too_big:t.type==="array"?r=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?r=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?r=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?r=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?r=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:r="Invalid input";break;case C.custom:r="Invalid input";break;case C.invalid_intersection_types:r="Intersection results could not be merged";break;case C.not_multiple_of:r=`Number must be a multiple of ${t.multipleOf}`;break;case C.not_finite:r="Number must be finite";break;default:r=e.defaultError,ue.assertNever(t)}return{message:r}};let md=Er;function Ng(t){md=t}function vi(){return md}const bi=t=>{const{data:e,path:r,errorMaps:n,issueData:i}=t,o=[...r,...i.path||[]],a={...i,path:o};if(i.message!==void 0)return{...i,path:o,message:i.message};let s="";const l=n.filter(c=>!!c).slice().reverse();for(const c of l)s=c(a,{data:e,defaultError:s}).message;return{...i,path:o,message:s}},Pg=[];function A(t,e){const r=vi(),n=bi({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,r,r===Er?void 0:Er].filter(i=>!!i)});t.common.issues.push(n)}class je{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,r){const n=[];for(const i of r){if(i.status==="aborted")return Q;i.status==="dirty"&&e.dirty(),n.push(i.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,r){const n=[];for(const i of r){const o=await i.key,a=await i.value;n.push({key:o,value:a})}return je.mergeObjectSync(e,n)}static mergeObjectSync(e,r){const n={};for(const i of r){const{key:o,value:a}=i;if(o.status==="aborted"||a.status==="aborted")return Q;o.status==="dirty"&&e.dirty(),a.status==="dirty"&&e.dirty(),o.value!=="__proto__"&&(typeof a.value<"u"||i.alwaysSet)&&(n[o.value]=a.value)}return{status:e.value,value:n}}}const Q=Object.freeze({status:"aborted"}),wi=t=>({status:"dirty",value:t}),He=t=>({status:"valid",value:t}),Ko=t=>t.status==="aborted",Qo=t=>t.status==="dirty",rr=t=>t.status==="valid",ln=t=>typeof Promise<"u"&&t instanceof Promise;function yi(t,e,r,n){if(typeof e=="function"?t!==e||!0:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(t)}function gd(t,e,r,n,i){if(typeof e=="function"?t!==e||!0:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(t,r),r}var W;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e==null?void 0:e.message})(W||(W={}));var Br,Wr;class gt{constructor(e,r,n,i){this._cachedPath=[],this.parent=e,this.data=r,this._path=n,this._key=i}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const ol=(t,e)=>{if(rr(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const r=new Xe(t.common.issues);return this._error=r,this._error}}};function ee(t){if(!t)return{};const{errorMap:e,invalid_type_error:r,required_error:n,description:i}=t;if(e&&(r||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:i}:{errorMap:(o,a)=>{var s,l;const{message:c}=t;return o.code==="invalid_enum_value"?{message:c??a.defaultError}:typeof a.data>"u"?{message:(s=c??n)!==null&&s!==void 0?s:a.defaultError}:o.code!=="invalid_type"?{message:a.defaultError}:{message:(l=c??r)!==null&&l!==void 0?l:a.defaultError}},description:i}}class ne{get description(){return this._def.description}_getType(e){return kt(e.data)}_getOrReturnCtx(e,r){return r||{common:e.parent.common,data:e.data,parsedType:kt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new je,ctx:{common:e.parent.common,data:e.data,parsedType:kt(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const r=this._parse(e);if(ln(r))throw new Error("Synchronous parse encountered promise.");return r}_parseAsync(e){const r=this._parse(e);return Promise.resolve(r)}parse(e,r){const n=this.safeParse(e,r);if(n.success)return n.data;throw n.error}safeParse(e,r){var n;const i={common:{issues:[],async:(n=r==null?void 0:r.async)!==null&&n!==void 0?n:!1,contextualErrorMap:r==null?void 0:r.errorMap},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:kt(e)},o=this._parseSync({data:e,path:i.path,parent:i});return ol(i,o)}"~validate"(e){var r,n;const i={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:kt(e)};if(!this["~standard"].async)try{const o=this._parseSync({data:e,path:[],parent:i});return rr(o)?{value:o.value}:{issues:i.common.issues}}catch(o){!((n=(r=o==null?void 0:o.message)===null||r===void 0?void 0:r.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),i.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:i}).then(o=>rr(o)?{value:o.value}:{issues:i.common.issues})}async parseAsync(e,r){const n=await this.safeParseAsync(e,r);if(n.success)return n.data;throw n.error}async safeParseAsync(e,r){const n={common:{issues:[],contextualErrorMap:r==null?void 0:r.errorMap,async:!0},path:(r==null?void 0:r.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:kt(e)},i=this._parse({data:e,path:n.path,parent:n}),o=await(ln(i)?i:Promise.resolve(i));return ol(n,o)}refine(e,r){const n=i=>typeof r=="string"||typeof r>"u"?{message:r}:typeof r=="function"?r(i):r;return this._refinement((i,o)=>{const a=e(i),s=()=>o.addIssue({code:C.custom,...n(i)});return typeof Promise<"u"&&a instanceof Promise?a.then(l=>l?!0:(s(),!1)):a?!0:(s(),!1)})}refinement(e,r){return this._refinement((n,i)=>e(n)?!0:(i.addIssue(typeof r=="function"?r(n,i):r),!1))}_refinement(e){return new ct({schema:this,typeName:K.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:r=>this["~validate"](r)}}optional(){return ft.create(this,this._def)}nullable(){return Ht.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return st.create(this)}promise(){return Nr.create(this,this._def)}or(e){return pn.create([this,e],this._def)}and(e){return hn.create(this,e,this._def)}transform(e){return new ct({...ee(this._def),schema:this,typeName:K.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const r=typeof e=="function"?e:()=>e;return new bn({...ee(this._def),innerType:this,defaultValue:r,typeName:K.ZodDefault})}brand(){return new Ra({typeName:K.ZodBranded,type:this,...ee(this._def)})}catch(e){const r=typeof e=="function"?e:()=>e;return new wn({...ee(this._def),innerType:this,catchValue:r,typeName:K.ZodCatch})}describe(e){const r=this.constructor;return new r({...this._def,description:e})}pipe(e){return Mn.create(this,e)}readonly(){return yn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Tg=/^c[^\s-]{8,}$/i,Mg=/^[0-9a-z]+$/,Ag=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Rg=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Ig=/^[a-z0-9_-]{21}$/i,$g=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,zg=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,jg=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Lg="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let ho;const Dg=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Fg=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Hg=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Bg=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Wg=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Vg=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,vd="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",qg=new RegExp(`^${vd}$`);function bd(t){let e="[0-5]\\d";t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`);const r=t.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${r}`}function Ug(t){return new RegExp(`^${bd(t)}$`)}function wd(t){let e=`${vd}T${bd(t)}`;const r=[];return r.push(t.local?"Z?":"Z"),t.offset&&r.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${r.join("|")})`,new RegExp(`^${e}$`)}function Gg(t,e){return!!((e==="v4"||!e)&&Dg.test(t)||(e==="v6"||!e)&&Hg.test(t))}function Zg(t,e){if(!$g.test(t))return!1;try{const[r]=t.split("."),n=r.replace(/-/g,"+").replace(/_/g,"/").padEnd(r.length+(4-r.length%4)%4,"="),i=JSON.parse(atob(n));return!(typeof i!="object"||i===null||!i.typ||!i.alg||e&&i.alg!==e)}catch{return!1}}function Yg(t,e){return!!((e==="v4"||!e)&&Fg.test(t)||(e==="v6"||!e)&&Bg.test(t))}class at extends ne{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==z.string){const i=this._getOrReturnCtx(e);return A(i,{code:C.invalid_type,expected:z.string,received:i.parsedType}),Q}const r=new je;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),A(n,{code:C.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),r.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),A(n,{code:C.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),r.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,a=e.data.length<i.value;(o||a)&&(n=this._getOrReturnCtx(e,n),o?A(n,{code:C.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):a&&A(n,{code:C.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),r.dirty())}else if(i.kind==="email")jg.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"email",code:C.invalid_string,message:i.message}),r.dirty());else if(i.kind==="emoji")ho||(ho=new RegExp(Lg,"u")),ho.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"emoji",code:C.invalid_string,message:i.message}),r.dirty());else if(i.kind==="uuid")Rg.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"uuid",code:C.invalid_string,message:i.message}),r.dirty());else if(i.kind==="nanoid")Ig.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"nanoid",code:C.invalid_string,message:i.message}),r.dirty());else if(i.kind==="cuid")Tg.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"cuid",code:C.invalid_string,message:i.message}),r.dirty());else if(i.kind==="cuid2")Mg.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"cuid2",code:C.invalid_string,message:i.message}),r.dirty());else if(i.kind==="ulid")Ag.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"ulid",code:C.invalid_string,message:i.message}),r.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),A(n,{validation:"url",code:C.invalid_string,message:i.message}),r.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"regex",code:C.invalid_string,message:i.message}),r.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),A(n,{code:C.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),r.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),A(n,{code:C.invalid_string,validation:{startsWith:i.value},message:i.message}),r.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),A(n,{code:C.invalid_string,validation:{endsWith:i.value},message:i.message}),r.dirty()):i.kind==="datetime"?wd(i).test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{code:C.invalid_string,validation:"datetime",message:i.message}),r.dirty()):i.kind==="date"?qg.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{code:C.invalid_string,validation:"date",message:i.message}),r.dirty()):i.kind==="time"?Ug(i).test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{code:C.invalid_string,validation:"time",message:i.message}),r.dirty()):i.kind==="duration"?zg.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"duration",code:C.invalid_string,message:i.message}),r.dirty()):i.kind==="ip"?Gg(e.data,i.version)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"ip",code:C.invalid_string,message:i.message}),r.dirty()):i.kind==="jwt"?Zg(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"jwt",code:C.invalid_string,message:i.message}),r.dirty()):i.kind==="cidr"?Yg(e.data,i.version)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"cidr",code:C.invalid_string,message:i.message}),r.dirty()):i.kind==="base64"?Wg.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"base64",code:C.invalid_string,message:i.message}),r.dirty()):i.kind==="base64url"?Vg.test(e.data)||(n=this._getOrReturnCtx(e,n),A(n,{validation:"base64url",code:C.invalid_string,message:i.message}),r.dirty()):ue.assertNever(i);return{status:r.value,value:e.data}}_regex(e,r,n){return this.refinement(i=>e.test(i),{validation:r,code:C.invalid_string,...W.errToObj(n)})}_addCheck(e){return new at({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...W.errToObj(e)})}url(e){return this._addCheck({kind:"url",...W.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...W.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...W.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...W.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...W.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...W.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...W.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...W.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...W.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...W.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...W.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...W.errToObj(e)})}datetime(e){var r,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,offset:(r=e==null?void 0:e.offset)!==null&&r!==void 0?r:!1,local:(n=e==null?void 0:e.local)!==null&&n!==void 0?n:!1,...W.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof(e==null?void 0:e.precision)>"u"?null:e==null?void 0:e.precision,...W.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...W.errToObj(e)})}regex(e,r){return this._addCheck({kind:"regex",regex:e,...W.errToObj(r)})}includes(e,r){return this._addCheck({kind:"includes",value:e,position:r==null?void 0:r.position,...W.errToObj(r==null?void 0:r.message)})}startsWith(e,r){return this._addCheck({kind:"startsWith",value:e,...W.errToObj(r)})}endsWith(e,r){return this._addCheck({kind:"endsWith",value:e,...W.errToObj(r)})}min(e,r){return this._addCheck({kind:"min",value:e,...W.errToObj(r)})}max(e,r){return this._addCheck({kind:"max",value:e,...W.errToObj(r)})}length(e,r){return this._addCheck({kind:"length",value:e,...W.errToObj(r)})}nonempty(e){return this.min(1,W.errToObj(e))}trim(){return new at({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new at({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new at({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxLength(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}at.create=t=>{var e;return new at({checks:[],typeName:K.ZodString,coerce:(e=t==null?void 0:t.coerce)!==null&&e!==void 0?e:!1,...ee(t)})};function Kg(t,e){const r=(t.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,i=r>n?r:n,o=parseInt(t.toFixed(i).replace(".","")),a=parseInt(e.toFixed(i).replace(".",""));return o%a/Math.pow(10,i)}class Lt extends ne{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==z.number){const i=this._getOrReturnCtx(e);return A(i,{code:C.invalid_type,expected:z.number,received:i.parsedType}),Q}let r;const n=new je;for(const i of this._def.checks)i.kind==="int"?ue.isInteger(e.data)||(r=this._getOrReturnCtx(e,r),A(r,{code:C.invalid_type,expected:"integer",received:"float",message:i.message}),n.dirty()):i.kind==="min"?(i.inclusive?e.data<i.value:e.data<=i.value)&&(r=this._getOrReturnCtx(e,r),A(r,{code:C.too_small,minimum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),n.dirty()):i.kind==="max"?(i.inclusive?e.data>i.value:e.data>=i.value)&&(r=this._getOrReturnCtx(e,r),A(r,{code:C.too_big,maximum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),n.dirty()):i.kind==="multipleOf"?Kg(e.data,i.value)!==0&&(r=this._getOrReturnCtx(e,r),A(r,{code:C.not_multiple_of,multipleOf:i.value,message:i.message}),n.dirty()):i.kind==="finite"?Number.isFinite(e.data)||(r=this._getOrReturnCtx(e,r),A(r,{code:C.not_finite,message:i.message}),n.dirty()):ue.assertNever(i);return{status:n.value,value:e.data}}gte(e,r){return this.setLimit("min",e,!0,W.toString(r))}gt(e,r){return this.setLimit("min",e,!1,W.toString(r))}lte(e,r){return this.setLimit("max",e,!0,W.toString(r))}lt(e,r){return this.setLimit("max",e,!1,W.toString(r))}setLimit(e,r,n,i){return new Lt({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:W.toString(i)}]})}_addCheck(e){return new Lt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:W.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:W.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:W.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:W.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:W.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:W.toString(r)})}finite(e){return this._addCheck({kind:"finite",message:W.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:W.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:W.toString(e)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&ue.isInteger(e.value))}get isFinite(){let e=null,r=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(r===null||n.value>r)&&(r=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(r)&&Number.isFinite(e)}}Lt.create=t=>new Lt({checks:[],typeName:K.ZodNumber,coerce:(t==null?void 0:t.coerce)||!1,...ee(t)});class Dt extends ne{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==z.bigint)return this._getInvalidInput(e);let r;const n=new je;for(const i of this._def.checks)i.kind==="min"?(i.inclusive?e.data<i.value:e.data<=i.value)&&(r=this._getOrReturnCtx(e,r),A(r,{code:C.too_small,type:"bigint",minimum:i.value,inclusive:i.inclusive,message:i.message}),n.dirty()):i.kind==="max"?(i.inclusive?e.data>i.value:e.data>=i.value)&&(r=this._getOrReturnCtx(e,r),A(r,{code:C.too_big,type:"bigint",maximum:i.value,inclusive:i.inclusive,message:i.message}),n.dirty()):i.kind==="multipleOf"?e.data%i.value!==BigInt(0)&&(r=this._getOrReturnCtx(e,r),A(r,{code:C.not_multiple_of,multipleOf:i.value,message:i.message}),n.dirty()):ue.assertNever(i);return{status:n.value,value:e.data}}_getInvalidInput(e){const r=this._getOrReturnCtx(e);return A(r,{code:C.invalid_type,expected:z.bigint,received:r.parsedType}),Q}gte(e,r){return this.setLimit("min",e,!0,W.toString(r))}gt(e,r){return this.setLimit("min",e,!1,W.toString(r))}lte(e,r){return this.setLimit("max",e,!0,W.toString(r))}lt(e,r){return this.setLimit("max",e,!1,W.toString(r))}setLimit(e,r,n,i){return new Dt({...this._def,checks:[...this._def.checks,{kind:e,value:r,inclusive:n,message:W.toString(i)}]})}_addCheck(e){return new Dt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:W.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:W.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:W.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:W.toString(e)})}multipleOf(e,r){return this._addCheck({kind:"multipleOf",value:e,message:W.toString(r)})}get minValue(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e}get maxValue(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e}}Dt.create=t=>{var e;return new Dt({checks:[],typeName:K.ZodBigInt,coerce:(e=t==null?void 0:t.coerce)!==null&&e!==void 0?e:!1,...ee(t)})};class cn extends ne{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==z.boolean){const r=this._getOrReturnCtx(e);return A(r,{code:C.invalid_type,expected:z.boolean,received:r.parsedType}),Q}return He(e.data)}}cn.create=t=>new cn({typeName:K.ZodBoolean,coerce:(t==null?void 0:t.coerce)||!1,...ee(t)});class nr extends ne{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==z.date){const i=this._getOrReturnCtx(e);return A(i,{code:C.invalid_type,expected:z.date,received:i.parsedType}),Q}if(isNaN(e.data.getTime())){const i=this._getOrReturnCtx(e);return A(i,{code:C.invalid_date}),Q}const r=new je;let n;for(const i of this._def.checks)i.kind==="min"?e.data.getTime()<i.value&&(n=this._getOrReturnCtx(e,n),A(n,{code:C.too_small,message:i.message,inclusive:!0,exact:!1,minimum:i.value,type:"date"}),r.dirty()):i.kind==="max"?e.data.getTime()>i.value&&(n=this._getOrReturnCtx(e,n),A(n,{code:C.too_big,message:i.message,inclusive:!0,exact:!1,maximum:i.value,type:"date"}),r.dirty()):ue.assertNever(i);return{status:r.value,value:new Date(e.data.getTime())}}_addCheck(e){return new nr({...this._def,checks:[...this._def.checks,e]})}min(e,r){return this._addCheck({kind:"min",value:e.getTime(),message:W.toString(r)})}max(e,r){return this._addCheck({kind:"max",value:e.getTime(),message:W.toString(r)})}get minDate(){let e=null;for(const r of this._def.checks)r.kind==="min"&&(e===null||r.value>e)&&(e=r.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const r of this._def.checks)r.kind==="max"&&(e===null||r.value<e)&&(e=r.value);return e!=null?new Date(e):null}}nr.create=t=>new nr({checks:[],coerce:(t==null?void 0:t.coerce)||!1,typeName:K.ZodDate,...ee(t)});class _i extends ne{_parse(e){if(this._getType(e)!==z.symbol){const r=this._getOrReturnCtx(e);return A(r,{code:C.invalid_type,expected:z.symbol,received:r.parsedType}),Q}return He(e.data)}}_i.create=t=>new _i({typeName:K.ZodSymbol,...ee(t)});class dn extends ne{_parse(e){if(this._getType(e)!==z.undefined){const r=this._getOrReturnCtx(e);return A(r,{code:C.invalid_type,expected:z.undefined,received:r.parsedType}),Q}return He(e.data)}}dn.create=t=>new dn({typeName:K.ZodUndefined,...ee(t)});class un extends ne{_parse(e){if(this._getType(e)!==z.null){const r=this._getOrReturnCtx(e);return A(r,{code:C.invalid_type,expected:z.null,received:r.parsedType}),Q}return He(e.data)}}un.create=t=>new un({typeName:K.ZodNull,...ee(t)});class Or extends ne{constructor(){super(...arguments),this._any=!0}_parse(e){return He(e.data)}}Or.create=t=>new Or({typeName:K.ZodAny,...ee(t)});class Jt extends ne{constructor(){super(...arguments),this._unknown=!0}_parse(e){return He(e.data)}}Jt.create=t=>new Jt({typeName:K.ZodUnknown,...ee(t)});class Ct extends ne{_parse(e){const r=this._getOrReturnCtx(e);return A(r,{code:C.invalid_type,expected:z.never,received:r.parsedType}),Q}}Ct.create=t=>new Ct({typeName:K.ZodNever,...ee(t)});class xi extends ne{_parse(e){if(this._getType(e)!==z.undefined){const r=this._getOrReturnCtx(e);return A(r,{code:C.invalid_type,expected:z.void,received:r.parsedType}),Q}return He(e.data)}}xi.create=t=>new xi({typeName:K.ZodVoid,...ee(t)});class st extends ne{_parse(e){const{ctx:r,status:n}=this._processInputParams(e),i=this._def;if(r.parsedType!==z.array)return A(r,{code:C.invalid_type,expected:z.array,received:r.parsedType}),Q;if(i.exactLength!==null){const a=r.data.length>i.exactLength.value,s=r.data.length<i.exactLength.value;(a||s)&&(A(r,{code:a?C.too_big:C.too_small,minimum:s?i.exactLength.value:void 0,maximum:a?i.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:i.exactLength.message}),n.dirty())}if(i.minLength!==null&&r.data.length<i.minLength.value&&(A(r,{code:C.too_small,minimum:i.minLength.value,type:"array",inclusive:!0,exact:!1,message:i.minLength.message}),n.dirty()),i.maxLength!==null&&r.data.length>i.maxLength.value&&(A(r,{code:C.too_big,maximum:i.maxLength.value,type:"array",inclusive:!0,exact:!1,message:i.maxLength.message}),n.dirty()),r.common.async)return Promise.all([...r.data].map((a,s)=>i.type._parseAsync(new gt(r,a,r.path,s)))).then(a=>je.mergeArray(n,a));const o=[...r.data].map((a,s)=>i.type._parseSync(new gt(r,a,r.path,s)));return je.mergeArray(n,o)}get element(){return this._def.type}min(e,r){return new st({...this._def,minLength:{value:e,message:W.toString(r)}})}max(e,r){return new st({...this._def,maxLength:{value:e,message:W.toString(r)}})}length(e,r){return new st({...this._def,exactLength:{value:e,message:W.toString(r)}})}nonempty(e){return this.min(1,e)}}st.create=(t,e)=>new st({type:t,minLength:null,maxLength:null,exactLength:null,typeName:K.ZodArray,...ee(e)});function dr(t){if(t instanceof Oe){const e={};for(const r in t.shape){const n=t.shape[r];e[r]=ft.create(dr(n))}return new Oe({...t._def,shape:()=>e})}else return t instanceof st?new st({...t._def,type:dr(t.element)}):t instanceof ft?ft.create(dr(t.unwrap())):t instanceof Ht?Ht.create(dr(t.unwrap())):t instanceof vt?vt.create(t.items.map(e=>dr(e))):t}class Oe extends ne{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),r=ue.objectKeys(e);return this._cached={shape:e,keys:r}}_parse(e){if(this._getType(e)!==z.object){const l=this._getOrReturnCtx(e);return A(l,{code:C.invalid_type,expected:z.object,received:l.parsedType}),Q}const{status:r,ctx:n}=this._processInputParams(e),{shape:i,keys:o}=this._getCached(),a=[];if(!(this._def.catchall instanceof Ct&&this._def.unknownKeys==="strip"))for(const l in n.data)o.includes(l)||a.push(l);const s=[];for(const l of o){const c=i[l],u=n.data[l];s.push({key:{status:"valid",value:l},value:c._parse(new gt(n,u,n.path,l)),alwaysSet:l in n.data})}if(this._def.catchall instanceof Ct){const l=this._def.unknownKeys;if(l==="passthrough")for(const c of a)s.push({key:{status:"valid",value:c},value:{status:"valid",value:n.data[c]}});else if(l==="strict")a.length>0&&(A(n,{code:C.unrecognized_keys,keys:a}),r.dirty());else if(l!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const l=this._def.catchall;for(const c of a){const u=n.data[c];s.push({key:{status:"valid",value:c},value:l._parse(new gt(n,u,n.path,c)),alwaysSet:c in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const l=[];for(const c of s){const u=await c.key,d=await c.value;l.push({key:u,value:d,alwaysSet:c.alwaysSet})}return l}).then(l=>je.mergeObjectSync(r,l)):je.mergeObjectSync(r,s)}get shape(){return this._def.shape()}strict(e){return W.errToObj,new Oe({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(r,n)=>{var i,o,a,s;const l=(a=(o=(i=this._def).errorMap)===null||o===void 0?void 0:o.call(i,r,n).message)!==null&&a!==void 0?a:n.defaultError;return r.code==="unrecognized_keys"?{message:(s=W.errToObj(e).message)!==null&&s!==void 0?s:l}:{message:l}}}:{}})}strip(){return new Oe({...this._def,unknownKeys:"strip"})}passthrough(){return new Oe({...this._def,unknownKeys:"passthrough"})}extend(e){return new Oe({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Oe({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:K.ZodObject})}setKey(e,r){return this.augment({[e]:r})}catchall(e){return new Oe({...this._def,catchall:e})}pick(e){const r={};return ue.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(r[n]=this.shape[n])}),new Oe({...this._def,shape:()=>r})}omit(e){const r={};return ue.objectKeys(this.shape).forEach(n=>{e[n]||(r[n]=this.shape[n])}),new Oe({...this._def,shape:()=>r})}deepPartial(){return dr(this)}partial(e){const r={};return ue.objectKeys(this.shape).forEach(n=>{const i=this.shape[n];e&&!e[n]?r[n]=i:r[n]=i.optional()}),new Oe({...this._def,shape:()=>r})}required(e){const r={};return ue.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])r[n]=this.shape[n];else{let i=this.shape[n];for(;i instanceof ft;)i=i._def.innerType;r[n]=i}}),new Oe({...this._def,shape:()=>r})}keyof(){return yd(ue.objectKeys(this.shape))}}Oe.create=(t,e)=>new Oe({shape:()=>t,unknownKeys:"strip",catchall:Ct.create(),typeName:K.ZodObject,...ee(e)});Oe.strictCreate=(t,e)=>new Oe({shape:()=>t,unknownKeys:"strict",catchall:Ct.create(),typeName:K.ZodObject,...ee(e)});Oe.lazycreate=(t,e)=>new Oe({shape:t,unknownKeys:"strip",catchall:Ct.create(),typeName:K.ZodObject,...ee(e)});class pn extends ne{_parse(e){const{ctx:r}=this._processInputParams(e),n=this._def.options;function i(o){for(const s of o)if(s.result.status==="valid")return s.result;for(const s of o)if(s.result.status==="dirty")return r.common.issues.push(...s.ctx.common.issues),s.result;const a=o.map(s=>new Xe(s.ctx.common.issues));return A(r,{code:C.invalid_union,unionErrors:a}),Q}if(r.common.async)return Promise.all(n.map(async o=>{const a={...r,common:{...r.common,issues:[]},parent:null};return{result:await o._parseAsync({data:r.data,path:r.path,parent:a}),ctx:a}})).then(i);{let o;const a=[];for(const l of n){const c={...r,common:{...r.common,issues:[]},parent:null},u=l._parseSync({data:r.data,path:r.path,parent:c});if(u.status==="valid")return u;u.status==="dirty"&&!o&&(o={result:u,ctx:c}),c.common.issues.length&&a.push(c.common.issues)}if(o)return r.common.issues.push(...o.ctx.common.issues),o.result;const s=a.map(l=>new Xe(l));return A(r,{code:C.invalid_union,unionErrors:s}),Q}}get options(){return this._def.options}}pn.create=(t,e)=>new pn({options:t,typeName:K.ZodUnion,...ee(e)});const Mt=t=>t instanceof mn?Mt(t.schema):t instanceof ct?Mt(t.innerType()):t instanceof gn?[t.value]:t instanceof Ft?t.options:t instanceof vn?ue.objectValues(t.enum):t instanceof bn?Mt(t._def.innerType):t instanceof dn?[void 0]:t instanceof un?[null]:t instanceof ft?[void 0,...Mt(t.unwrap())]:t instanceof Ht?[null,...Mt(t.unwrap())]:t instanceof Ra||t instanceof yn?Mt(t.unwrap()):t instanceof wn?Mt(t._def.innerType):[];class Wi extends ne{_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==z.object)return A(r,{code:C.invalid_type,expected:z.object,received:r.parsedType}),Q;const n=this.discriminator,i=r.data[n],o=this.optionsMap.get(i);return o?r.common.async?o._parseAsync({data:r.data,path:r.path,parent:r}):o._parseSync({data:r.data,path:r.path,parent:r}):(A(r,{code:C.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),Q)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,r,n){const i=new Map;for(const o of r){const a=Mt(o.shape[e]);if(!a.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const s of a){if(i.has(s))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(s)}`);i.set(s,o)}}return new Wi({typeName:K.ZodDiscriminatedUnion,discriminator:e,options:r,optionsMap:i,...ee(n)})}}function Xo(t,e){const r=kt(t),n=kt(e);if(t===e)return{valid:!0,data:t};if(r===z.object&&n===z.object){const i=ue.objectKeys(e),o=ue.objectKeys(t).filter(s=>i.indexOf(s)!==-1),a={...t,...e};for(const s of o){const l=Xo(t[s],e[s]);if(!l.valid)return{valid:!1};a[s]=l.data}return{valid:!0,data:a}}else if(r===z.array&&n===z.array){if(t.length!==e.length)return{valid:!1};const i=[];for(let o=0;o<t.length;o++){const a=t[o],s=e[o],l=Xo(a,s);if(!l.valid)return{valid:!1};i.push(l.data)}return{valid:!0,data:i}}else return r===z.date&&n===z.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class hn extends ne{_parse(e){const{status:r,ctx:n}=this._processInputParams(e),i=(o,a)=>{if(Ko(o)||Ko(a))return Q;const s=Xo(o.value,a.value);return s.valid?((Qo(o)||Qo(a))&&r.dirty(),{status:r.value,value:s.data}):(A(n,{code:C.invalid_intersection_types}),Q)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([o,a])=>i(o,a)):i(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}hn.create=(t,e,r)=>new hn({left:t,right:e,typeName:K.ZodIntersection,...ee(r)});class vt extends ne{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.array)return A(n,{code:C.invalid_type,expected:z.array,received:n.parsedType}),Q;if(n.data.length<this._def.items.length)return A(n,{code:C.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Q;!this._def.rest&&n.data.length>this._def.items.length&&(A(n,{code:C.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),r.dirty());const i=[...n.data].map((o,a)=>{const s=this._def.items[a]||this._def.rest;return s?s._parse(new gt(n,o,n.path,a)):null}).filter(o=>!!o);return n.common.async?Promise.all(i).then(o=>je.mergeArray(r,o)):je.mergeArray(r,i)}get items(){return this._def.items}rest(e){return new vt({...this._def,rest:e})}}vt.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new vt({items:t,typeName:K.ZodTuple,rest:null,...ee(e)})};class fn extends ne{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.object)return A(n,{code:C.invalid_type,expected:z.object,received:n.parsedType}),Q;const i=[],o=this._def.keyType,a=this._def.valueType;for(const s in n.data)i.push({key:o._parse(new gt(n,s,n.path,s)),value:a._parse(new gt(n,n.data[s],n.path,s)),alwaysSet:s in n.data});return n.common.async?je.mergeObjectAsync(r,i):je.mergeObjectSync(r,i)}get element(){return this._def.valueType}static create(e,r,n){return r instanceof ne?new fn({keyType:e,valueType:r,typeName:K.ZodRecord,...ee(n)}):new fn({keyType:at.create(),valueType:e,typeName:K.ZodRecord,...ee(r)})}}class ki extends ne{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.map)return A(n,{code:C.invalid_type,expected:z.map,received:n.parsedType}),Q;const i=this._def.keyType,o=this._def.valueType,a=[...n.data.entries()].map(([s,l],c)=>({key:i._parse(new gt(n,s,n.path,[c,"key"])),value:o._parse(new gt(n,l,n.path,[c,"value"]))}));if(n.common.async){const s=new Map;return Promise.resolve().then(async()=>{for(const l of a){const c=await l.key,u=await l.value;if(c.status==="aborted"||u.status==="aborted")return Q;(c.status==="dirty"||u.status==="dirty")&&r.dirty(),s.set(c.value,u.value)}return{status:r.value,value:s}})}else{const s=new Map;for(const l of a){const c=l.key,u=l.value;if(c.status==="aborted"||u.status==="aborted")return Q;(c.status==="dirty"||u.status==="dirty")&&r.dirty(),s.set(c.value,u.value)}return{status:r.value,value:s}}}}ki.create=(t,e,r)=>new ki({valueType:e,keyType:t,typeName:K.ZodMap,...ee(r)});class ir extends ne{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.set)return A(n,{code:C.invalid_type,expected:z.set,received:n.parsedType}),Q;const i=this._def;i.minSize!==null&&n.data.size<i.minSize.value&&(A(n,{code:C.too_small,minimum:i.minSize.value,type:"set",inclusive:!0,exact:!1,message:i.minSize.message}),r.dirty()),i.maxSize!==null&&n.data.size>i.maxSize.value&&(A(n,{code:C.too_big,maximum:i.maxSize.value,type:"set",inclusive:!0,exact:!1,message:i.maxSize.message}),r.dirty());const o=this._def.valueType;function a(l){const c=new Set;for(const u of l){if(u.status==="aborted")return Q;u.status==="dirty"&&r.dirty(),c.add(u.value)}return{status:r.value,value:c}}const s=[...n.data.values()].map((l,c)=>o._parse(new gt(n,l,n.path,c)));return n.common.async?Promise.all(s).then(l=>a(l)):a(s)}min(e,r){return new ir({...this._def,minSize:{value:e,message:W.toString(r)}})}max(e,r){return new ir({...this._def,maxSize:{value:e,message:W.toString(r)}})}size(e,r){return this.min(e,r).max(e,r)}nonempty(e){return this.min(1,e)}}ir.create=(t,e)=>new ir({valueType:t,minSize:null,maxSize:null,typeName:K.ZodSet,...ee(e)});class mr extends ne{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==z.function)return A(r,{code:C.invalid_type,expected:z.function,received:r.parsedType}),Q;function n(s,l){return bi({data:s,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,vi(),Er].filter(c=>!!c),issueData:{code:C.invalid_arguments,argumentsError:l}})}function i(s,l){return bi({data:s,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,vi(),Er].filter(c=>!!c),issueData:{code:C.invalid_return_type,returnTypeError:l}})}const o={errorMap:r.common.contextualErrorMap},a=r.data;if(this._def.returns instanceof Nr){const s=this;return He(async function(...l){const c=new Xe([]),u=await s._def.args.parseAsync(l,o).catch(p=>{throw c.addIssue(n(l,p)),c}),d=await Reflect.apply(a,this,u);return await s._def.returns._def.type.parseAsync(d,o).catch(p=>{throw c.addIssue(i(d,p)),c})})}else{const s=this;return He(function(...l){const c=s._def.args.safeParse(l,o);if(!c.success)throw new Xe([n(l,c.error)]);const u=Reflect.apply(a,this,c.data),d=s._def.returns.safeParse(u,o);if(!d.success)throw new Xe([i(u,d.error)]);return d.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new mr({...this._def,args:vt.create(e).rest(Jt.create())})}returns(e){return new mr({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,r,n){return new mr({args:e||vt.create([]).rest(Jt.create()),returns:r||Jt.create(),typeName:K.ZodFunction,...ee(n)})}}class mn extends ne{get schema(){return this._def.getter()}_parse(e){const{ctx:r}=this._processInputParams(e);return this._def.getter()._parse({data:r.data,path:r.path,parent:r})}}mn.create=(t,e)=>new mn({getter:t,typeName:K.ZodLazy,...ee(e)});class gn extends ne{_parse(e){if(e.data!==this._def.value){const r=this._getOrReturnCtx(e);return A(r,{received:r.data,code:C.invalid_literal,expected:this._def.value}),Q}return{status:"valid",value:e.data}}get value(){return this._def.value}}gn.create=(t,e)=>new gn({value:t,typeName:K.ZodLiteral,...ee(e)});function yd(t,e){return new Ft({values:t,typeName:K.ZodEnum,...ee(e)})}class Ft extends ne{constructor(){super(...arguments),Br.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const r=this._getOrReturnCtx(e),n=this._def.values;return A(r,{expected:ue.joinValues(n),received:r.parsedType,code:C.invalid_type}),Q}if(yi(this,Br)||gd(this,Br,new Set(this._def.values)),!yi(this,Br).has(e.data)){const r=this._getOrReturnCtx(e),n=this._def.values;return A(r,{received:r.data,code:C.invalid_enum_value,options:n}),Q}return He(e.data)}get options(){return this._def.values}get enum(){const e={};for(const r of this._def.values)e[r]=r;return e}get Values(){const e={};for(const r of this._def.values)e[r]=r;return e}get Enum(){const e={};for(const r of this._def.values)e[r]=r;return e}extract(e,r=this._def){return Ft.create(e,{...this._def,...r})}exclude(e,r=this._def){return Ft.create(this.options.filter(n=>!e.includes(n)),{...this._def,...r})}}Br=new WeakMap;Ft.create=yd;class vn extends ne{constructor(){super(...arguments),Wr.set(this,void 0)}_parse(e){const r=ue.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==z.string&&n.parsedType!==z.number){const i=ue.objectValues(r);return A(n,{expected:ue.joinValues(i),received:n.parsedType,code:C.invalid_type}),Q}if(yi(this,Wr)||gd(this,Wr,new Set(ue.getValidEnumValues(this._def.values))),!yi(this,Wr).has(e.data)){const i=ue.objectValues(r);return A(n,{received:n.data,code:C.invalid_enum_value,options:i}),Q}return He(e.data)}get enum(){return this._def.values}}Wr=new WeakMap;vn.create=(t,e)=>new vn({values:t,typeName:K.ZodNativeEnum,...ee(e)});class Nr extends ne{unwrap(){return this._def.type}_parse(e){const{ctx:r}=this._processInputParams(e);if(r.parsedType!==z.promise&&r.common.async===!1)return A(r,{code:C.invalid_type,expected:z.promise,received:r.parsedType}),Q;const n=r.parsedType===z.promise?r.data:Promise.resolve(r.data);return He(n.then(i=>this._def.type.parseAsync(i,{path:r.path,errorMap:r.common.contextualErrorMap})))}}Nr.create=(t,e)=>new Nr({type:t,typeName:K.ZodPromise,...ee(e)});class ct extends ne{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===K.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:r,ctx:n}=this._processInputParams(e),i=this._def.effect||null,o={addIssue:a=>{A(n,a),a.fatal?r.abort():r.dirty()},get path(){return n.path}};if(o.addIssue=o.addIssue.bind(o),i.type==="preprocess"){const a=i.transform(n.data,o);if(n.common.async)return Promise.resolve(a).then(async s=>{if(r.value==="aborted")return Q;const l=await this._def.schema._parseAsync({data:s,path:n.path,parent:n});return l.status==="aborted"?Q:l.status==="dirty"||r.value==="dirty"?wi(l.value):l});{if(r.value==="aborted")return Q;const s=this._def.schema._parseSync({data:a,path:n.path,parent:n});return s.status==="aborted"?Q:s.status==="dirty"||r.value==="dirty"?wi(s.value):s}}if(i.type==="refinement"){const a=s=>{const l=i.refinement(s,o);if(n.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return s};if(n.common.async===!1){const s=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?Q:(s.status==="dirty"&&r.dirty(),a(s.value),{status:r.value,value:s.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(s=>s.status==="aborted"?Q:(s.status==="dirty"&&r.dirty(),a(s.value).then(()=>({status:r.value,value:s.value}))))}if(i.type==="transform")if(n.common.async===!1){const a=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!rr(a))return a;const s=i.transform(a.value,o);if(s instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:r.value,value:s}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(a=>rr(a)?Promise.resolve(i.transform(a.value,o)).then(s=>({status:r.value,value:s})):a);ue.assertNever(i)}}ct.create=(t,e,r)=>new ct({schema:t,typeName:K.ZodEffects,effect:e,...ee(r)});ct.createWithPreprocess=(t,e,r)=>new ct({schema:e,effect:{type:"preprocess",transform:t},typeName:K.ZodEffects,...ee(r)});class ft extends ne{_parse(e){return this._getType(e)===z.undefined?He(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ft.create=(t,e)=>new ft({innerType:t,typeName:K.ZodOptional,...ee(e)});class Ht extends ne{_parse(e){return this._getType(e)===z.null?He(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ht.create=(t,e)=>new Ht({innerType:t,typeName:K.ZodNullable,...ee(e)});class bn extends ne{_parse(e){const{ctx:r}=this._processInputParams(e);let n=r.data;return r.parsedType===z.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:r.path,parent:r})}removeDefault(){return this._def.innerType}}bn.create=(t,e)=>new bn({innerType:t,typeName:K.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ee(e)});class wn extends ne{_parse(e){const{ctx:r}=this._processInputParams(e),n={...r,common:{...r.common,issues:[]}},i=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return ln(i)?i.then(o=>({status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new Xe(n.common.issues)},input:n.data})})):{status:"valid",value:i.status==="valid"?i.value:this._def.catchValue({get error(){return new Xe(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}wn.create=(t,e)=>new wn({innerType:t,typeName:K.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ee(e)});class Si extends ne{_parse(e){if(this._getType(e)!==z.nan){const r=this._getOrReturnCtx(e);return A(r,{code:C.invalid_type,expected:z.nan,received:r.parsedType}),Q}return{status:"valid",value:e.data}}}Si.create=t=>new Si({typeName:K.ZodNaN,...ee(t)});const Qg=Symbol("zod_brand");class Ra extends ne{_parse(e){const{ctx:r}=this._processInputParams(e),n=r.data;return this._def.type._parse({data:n,path:r.path,parent:r})}unwrap(){return this._def.type}}class Mn extends ne{_parse(e){const{status:r,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const i=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return i.status==="aborted"?Q:i.status==="dirty"?(r.dirty(),wi(i.value)):this._def.out._parseAsync({data:i.value,path:n.path,parent:n})})();{const i=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return i.status==="aborted"?Q:i.status==="dirty"?(r.dirty(),{status:"dirty",value:i.value}):this._def.out._parseSync({data:i.value,path:n.path,parent:n})}}static create(e,r){return new Mn({in:e,out:r,typeName:K.ZodPipeline})}}class yn extends ne{_parse(e){const r=this._def.innerType._parse(e),n=i=>(rr(i)&&(i.value=Object.freeze(i.value)),i);return ln(r)?r.then(i=>n(i)):n(r)}unwrap(){return this._def.innerType}}yn.create=(t,e)=>new yn({innerType:t,typeName:K.ZodReadonly,...ee(e)});function al(t,e){const r=typeof t=="function"?t(e):typeof t=="string"?{message:t}:t;return typeof r=="string"?{message:r}:r}function _d(t,e={},r){return t?Or.create().superRefine((n,i)=>{var o,a;const s=t(n);if(s instanceof Promise)return s.then(l=>{var c,u;if(!l){const d=al(e,n),p=(u=(c=d.fatal)!==null&&c!==void 0?c:r)!==null&&u!==void 0?u:!0;i.addIssue({code:"custom",...d,fatal:p})}});if(!s){const l=al(e,n),c=(a=(o=l.fatal)!==null&&o!==void 0?o:r)!==null&&a!==void 0?a:!0;i.addIssue({code:"custom",...l,fatal:c})}}):Or.create()}const Xg={object:Oe.lazycreate};var K;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(K||(K={}));const Jg=(t,e={message:`Input not instance of ${t.name}`})=>_d(r=>r instanceof t,e),xd=at.create,kd=Lt.create,ev=Si.create,tv=Dt.create,Sd=cn.create,rv=nr.create,nv=_i.create,iv=dn.create,ov=un.create,av=Or.create,sv=Jt.create,lv=Ct.create,cv=xi.create,dv=st.create,uv=Oe.create,pv=Oe.strictCreate,hv=pn.create,fv=Wi.create,mv=hn.create,gv=vt.create,vv=fn.create,bv=ki.create,wv=ir.create,yv=mr.create,_v=mn.create,xv=gn.create,kv=Ft.create,Sv=vn.create,Cv=Nr.create,sl=ct.create,Ev=ft.create,Ov=Ht.create,Nv=ct.createWithPreprocess,Pv=Mn.create,Tv=()=>xd().optional(),Mv=()=>kd().optional(),Av=()=>Sd().optional(),Rv={string:t=>at.create({...t,coerce:!0}),number:t=>Lt.create({...t,coerce:!0}),boolean:t=>cn.create({...t,coerce:!0}),bigint:t=>Dt.create({...t,coerce:!0}),date:t=>nr.create({...t,coerce:!0})},Iv=Q;var we=Object.freeze({__proto__:null,defaultErrorMap:Er,setErrorMap:Ng,getErrorMap:vi,makeIssue:bi,EMPTY_PATH:Pg,addIssueToContext:A,ParseStatus:je,INVALID:Q,DIRTY:wi,OK:He,isAborted:Ko,isDirty:Qo,isValid:rr,isAsync:ln,get util(){return ue},get objectUtil(){return Yo},ZodParsedType:z,getParsedType:kt,ZodType:ne,datetimeRegex:wd,ZodString:at,ZodNumber:Lt,ZodBigInt:Dt,ZodBoolean:cn,ZodDate:nr,ZodSymbol:_i,ZodUndefined:dn,ZodNull:un,ZodAny:Or,ZodUnknown:Jt,ZodNever:Ct,ZodVoid:xi,ZodArray:st,ZodObject:Oe,ZodUnion:pn,ZodDiscriminatedUnion:Wi,ZodIntersection:hn,ZodTuple:vt,ZodRecord:fn,ZodMap:ki,ZodSet:ir,ZodFunction:mr,ZodLazy:mn,ZodLiteral:gn,ZodEnum:Ft,ZodNativeEnum:vn,ZodPromise:Nr,ZodEffects:ct,ZodTransformer:ct,ZodOptional:ft,ZodNullable:Ht,ZodDefault:bn,ZodCatch:wn,ZodNaN:Si,BRAND:Qg,ZodBranded:Ra,ZodPipeline:Mn,ZodReadonly:yn,custom:_d,Schema:ne,ZodSchema:ne,late:Xg,get ZodFirstPartyTypeKind(){return K},coerce:Rv,any:av,array:dv,bigint:tv,boolean:Sd,date:rv,discriminatedUnion:fv,effect:sl,enum:kv,function:yv,instanceof:Jg,intersection:mv,lazy:_v,literal:xv,map:bv,nan:ev,nativeEnum:Sv,never:lv,null:ov,nullable:Ov,number:kd,object:uv,oboolean:Av,onumber:Mv,optional:Ev,ostring:Tv,pipeline:Pv,preprocess:Nv,promise:Cv,record:vv,set:wv,strictObject:pv,string:xd,symbol:nv,transformer:sl,tuple:gv,undefined:iv,union:hv,unknown:sv,void:cv,NEVER:Iv,ZodIssueCode:C,quotelessJson:Og,ZodError:Xe}),$v=5746,zv="/ping/stagewise",jv="stagewise",Cd={server:{getSessionInfo:{request:we.object({}),response:we.object({sessionId:we.string().optional(),appName:we.string().describe('The name of the application, e.g. "VS Code" or "Cursor"'),displayName:we.string().describe("Human-readable window identifier for UI display"),port:we.number().describe("Port number this VS Code instance is running on")}),update:we.object({})},triggerAgentPrompt:{request:we.object({sessionId:we.string().optional(),prompt:we.string(),model:we.string().optional().describe("The model to use for the agent prompt"),files:we.array(we.string()).optional().describe("Link project files to the agent prompt"),mode:we.enum(["agent","ask","manual"]).optional().describe("The mode to use for the agent prompt"),images:we.array(we.string()).optional().describe("Upload files like images, videos, etc.")}),response:we.object({sessionId:we.string().optional(),result:we.object({success:we.boolean(),error:we.string().optional(),errorCode:we.enum(["session_mismatch"]).optional(),output:we.string().optional()})}),update:we.object({sessionId:we.string().optional(),updateText:we.string()})}}};const Lv=2;async function Dv(t=10,e=300){const r=[];let n=0;for(let i=0;i<t;i++){const o=$v+i;try{const a=new AbortController,s=setTimeout(()=>a.abort(),e),l=await fetch(`http://localhost:${o}${zv}`,{signal:a.signal});if(clearTimeout(s),n=0,l.ok&&await l.text()===jv)try{const c=fd(`ws://localhost:${o}`,Cd);await c.connect();const u=await c.call.getSessionInfo({},{onUpdate:()=>{}});r.push(u),await c.close()}catch(c){console.warn(`Failed to get session info from port ${o}:`,c)}else continue}catch{if(n++,n>=Lv){console.warn("⬆️⬆️⬆️ Those two errors are expected! (Everything is fine, they are part of stagewise's discovery mechanism!) ✅");break}continue}}return r.length===0&&console.warn("No IDE windows found, please start an IDE with the stagewise extension installed! ❌"),r}const Fv=()=>typeof window<"u"&&window.location&&window.location.port||"80",Jo=()=>`ide-selected-session-id-on-browser-port-${Fv()}`,ll=()=>{try{return localStorage.getItem(Jo())||void 0}catch{return}},Wn=t=>{try{t?localStorage.setItem(Jo(),t):localStorage.removeItem(Jo())}catch{}},Ed=rt({windows:[],isDiscovering:!1,discoveryError:null,selectedSession:void 0,shouldPromptWindowSelection:!1,setShouldPromptWindowSelection:()=>{},discover:async()=>{},selectSession:()=>{},refreshSession:async()=>{},appName:void 0});function Hv({children:t}){const[e,r]=me([]),[n,i]=me(!1),[o,a]=me(null),[s,l]=me(ll()),[c,u]=me(!1),d=async()=>{i(!0),a(null);try{const w=await Dv();r(w);const y=ll();if(w.length===1){const v=w[0];(!y||y!==v.sessionId)&&(l(v.sessionId),Wn(v.sessionId)),u(!1)}else{const v=w.length>1&&!y||y&&!w.some(_=>_.sessionId===y);u(v),v&&(l(void 0),Wn(void 0))}}catch(w){a(w instanceof Error?w.message:"Failed to discover windows")}finally{i(!1)}},p=w=>{if(!w||w===""){Wn(void 0),l(void 0);return}l(w),Wn(w),w&&u(!1)},h=async()=>{s&&await d()};xe(()=>{d()},[]);const b=e.find(w=>w.sessionId===s),f={windows:e,isDiscovering:n,discoveryError:o,selectedSession:b,shouldPromptWindowSelection:c,setShouldPromptWindowSelection:u,discover:d,selectSession:p,refreshSession:h,appName:b==null?void 0:b.appName};return m(Ed.Provider,{value:f,children:t})}function Bt(){return Le(Ed)}const Od=rt({bridge:null,isConnecting:!1,error:null});function Bv({children:t}){const[e,r]=me({bridge:null,isConnecting:!0,error:null}),{selectedSession:n}=Bt(),i=ce(null),o=V(async a=>{i.current&&await i.current.close();try{const s=fd(`ws://localhost:${a}`,Cd);await s.connect(),i.current=s,r({bridge:s,isConnecting:!1,error:null})}catch(s){i.current=null,r({bridge:null,isConnecting:!1,error:s instanceof Error?s:new Error(String(s))})}},[]);return xe(()=>{n&&o(n.port)},[n,o]),m(Od.Provider,{value:e,children:t})}function Nd(){const t=Le(Od);if(!t)throw new Error("useSRPCBridge must be used within an SRPCBridgeProvider");return t}const Pd=rt({config:void 0});function Wv({children:t,config:e}){const r=Ne(()=>({config:e}),[e]);return m(Pd.Provider,{value:r,children:t})}function Vv(){return Le(Pd)}const Td=rt({plugins:[],toolbarContext:{sendPrompt:()=>{}}});function qv({children:t}){const{bridge:e}=Nd(),{selectedSession:r}=Bt(),{config:n}=Vv(),i=(n==null?void 0:n.plugins)||[],o=Ne(()=>({sendPrompt:async l=>{if(!e)throw new Error("No connection to the agent");return await e.call.triggerAgentPrompt(typeof l=="string"?{prompt:l,...r&&{sessionId:r.sessionId}}:{prompt:l.prompt,model:l.model,files:l.files,images:l.images,mode:l.mode,...r&&{sessionId:r.sessionId}},{onUpdate:c=>{}})}}),[e,r]),a=ce(!1);xe(()=>{a.current||(a.current=!0,i.forEach(l=>{var c;(c=l.onLoad)==null||c.call(l,o)}))},[i,o]);const s=Ne(()=>({plugins:i,toolbarContext:o}),[i,o]);return m(Td.Provider,{value:s,children:t})}function Vi(){return Le(Td)}function Md(t){var e,r,n="";if(typeof t=="string"||typeof t=="number")n+=t;else if(typeof t=="object")if(Array.isArray(t)){var i=t.length;for(e=0;e<i;e++)t[e]&&(r=Md(t[e]))&&(n&&(n+=" "),n+=r)}else for(r in t)t[r]&&(n&&(n+=" "),n+=r);return n}function Uv(){for(var t,e,r=0,n="",i=arguments.length;r<i;r++)(t=arguments[r])&&(e=Md(t))&&(n&&(n+=" "),n+=e);return n}const Ia="-",Gv=t=>{const e=Yv(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:i=>{const o=i.split(Ia);return o[0]===""&&o.length!==1&&o.shift(),Ad(o,e)||Zv(i)},getConflictingClassGroupIds:(i,o)=>{const a=r[i]||[];return o&&n[i]?[...a,...n[i]]:a}}},Ad=(t,e)=>{var r;if(t.length===0)return e.classGroupId;const n=t[0],i=e.nextPart.get(n),o=i?Ad(t.slice(1),i):void 0;if(o)return o;if(e.validators.length===0)return;const a=t.join(Ia);return(r=e.validators.find(({validator:s})=>s(a)))==null?void 0:r.classGroupId},cl=/^\[(.+)\]$/,Zv=t=>{if(cl.test(t)){const e=cl.exec(t)[1],r=e==null?void 0:e.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},Yv=t=>{const{theme:e,classGroups:r}=t,n={nextPart:new Map,validators:[]};for(const i in r)ea(r[i],n,i,e);return n},ea=(t,e,r,n)=>{t.forEach(i=>{if(typeof i=="string"){const o=i===""?e:dl(e,i);o.classGroupId=r;return}if(typeof i=="function"){if(Kv(i)){ea(i(n),e,r,n);return}e.validators.push({validator:i,classGroupId:r});return}Object.entries(i).forEach(([o,a])=>{ea(a,dl(e,o),r,n)})})},dl=(t,e)=>{let r=t;return e.split(Ia).forEach(n=>{r.nextPart.has(n)||r.nextPart.set(n,{nextPart:new Map,validators:[]}),r=r.nextPart.get(n)}),r},Kv=t=>t.isThemeGetter,Qv=t=>{if(t<1)return{get:()=>{},set:()=>{}};let e=0,r=new Map,n=new Map;const i=(o,a)=>{r.set(o,a),e++,e>t&&(e=0,n=r,r=new Map)};return{get(o){let a=r.get(o);if(a!==void 0)return a;if((a=n.get(o))!==void 0)return i(o,a),a},set(o,a){r.has(o)?r.set(o,a):i(o,a)}}},ta="!",ra=":",Xv=ra.length,Jv=t=>{const{prefix:e,experimentalParseClassName:r}=t;let n=i=>{const o=[];let a=0,s=0,l=0,c;for(let b=0;b<i.length;b++){let f=i[b];if(a===0&&s===0){if(f===ra){o.push(i.slice(l,b)),l=b+Xv;continue}if(f==="/"){c=b;continue}}f==="["?a++:f==="]"?a--:f==="("?s++:f===")"&&s--}const u=o.length===0?i:i.substring(l),d=e0(u),p=d!==u,h=c&&c>l?c-l:void 0;return{modifiers:o,hasImportantModifier:p,baseClassName:d,maybePostfixModifierPosition:h}};if(e){const i=e+ra,o=n;n=a=>a.startsWith(i)?o(a.substring(i.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:a,maybePostfixModifierPosition:void 0}}if(r){const i=n;n=o=>r({className:o,parseClassName:i})}return n},e0=t=>t.endsWith(ta)?t.substring(0,t.length-1):t.startsWith(ta)?t.substring(1):t,t0=t=>{const e=Object.fromEntries(t.orderSensitiveModifiers.map(r=>[r,!0]));return r=>{if(r.length<=1)return r;const n=[];let i=[];return r.forEach(o=>{o[0]==="["||e[o]?(n.push(...i.sort(),o),i=[]):i.push(o)}),n.push(...i.sort()),n}},r0=t=>({cache:Qv(t.cacheSize),parseClassName:Jv(t),sortModifiers:t0(t),...Gv(t)}),n0=/\s+/,i0=(t,e)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i,sortModifiers:o}=e,a=[],s=t.trim().split(n0);let l="";for(let c=s.length-1;c>=0;c-=1){const u=s[c],{isExternal:d,modifiers:p,hasImportantModifier:h,baseClassName:b,maybePostfixModifierPosition:f}=r(u);if(d){l=u+(l.length>0?" "+l:l);continue}let w=!!f,y=n(w?b.substring(0,f):b);if(!y){if(!w){l=u+(l.length>0?" "+l:l);continue}if(y=n(b),!y){l=u+(l.length>0?" "+l:l);continue}w=!1}const v=o(p).join(":"),_=h?v+ta:v,S=_+y;if(a.includes(S))continue;a.push(S);const E=i(y,w);for(let I=0;I<E.length;++I){const H=E[I];a.push(_+H)}l=u+(l.length>0?" "+l:l)}return l};function o0(){let t=0,e,r,n="";for(;t<arguments.length;)(e=arguments[t++])&&(r=Rd(e))&&(n&&(n+=" "),n+=r);return n}const Rd=t=>{if(typeof t=="string")return t;let e,r="";for(let n=0;n<t.length;n++)t[n]&&(e=Rd(t[n]))&&(r&&(r+=" "),r+=e);return r};function ul(t,...e){let r,n,i,o=a;function a(l){const c=e.reduce((u,d)=>d(u),t());return r=r0(c),n=r.cache.get,i=r.cache.set,o=s,s(l)}function s(l){const c=n(l);if(c)return c;const u=i0(l,r);return i(l,u),u}return function(){return o(o0.apply(null,arguments))}}const Re=t=>{const e=r=>r[t]||[];return e.isThemeGetter=!0,e},Id=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,$d=/^\((?:(\w[\w-]*):)?(.+)\)$/i,a0=/^\d+\/\d+$/,s0=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,l0=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,c0=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,d0=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,u0=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,lr=t=>a0.test(t),ie=t=>!!t&&!Number.isNaN(Number(t)),Tt=t=>!!t&&Number.isInteger(Number(t)),fo=t=>t.endsWith("%")&&ie(t.slice(0,-1)),_t=t=>s0.test(t),p0=()=>!0,h0=t=>l0.test(t)&&!c0.test(t),zd=()=>!1,f0=t=>d0.test(t),m0=t=>u0.test(t),g0=t=>!D(t)&&!F(t),v0=t=>Rr(t,Dd,zd),D=t=>Id.test(t),Zt=t=>Rr(t,Fd,h0),mo=t=>Rr(t,x0,ie),pl=t=>Rr(t,jd,zd),b0=t=>Rr(t,Ld,m0),Vn=t=>Rr(t,Hd,f0),F=t=>$d.test(t),Dr=t=>Ir(t,Fd),w0=t=>Ir(t,k0),hl=t=>Ir(t,jd),y0=t=>Ir(t,Dd),_0=t=>Ir(t,Ld),qn=t=>Ir(t,Hd,!0),Rr=(t,e,r)=>{const n=Id.exec(t);return n?n[1]?e(n[1]):r(n[2]):!1},Ir=(t,e,r=!1)=>{const n=$d.exec(t);return n?n[1]?e(n[1]):r:!1},jd=t=>t==="position"||t==="percentage",Ld=t=>t==="image"||t==="url",Dd=t=>t==="length"||t==="size"||t==="bg-size",Fd=t=>t==="length",x0=t=>t==="number",k0=t=>t==="family-name",Hd=t=>t==="shadow",fl=()=>{const t=Re("color"),e=Re("font"),r=Re("text"),n=Re("font-weight"),i=Re("tracking"),o=Re("leading"),a=Re("breakpoint"),s=Re("container"),l=Re("spacing"),c=Re("radius"),u=Re("shadow"),d=Re("inset-shadow"),p=Re("text-shadow"),h=Re("drop-shadow"),b=Re("blur"),f=Re("perspective"),w=Re("aspect"),y=Re("ease"),v=Re("animate"),_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],S=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],E=()=>[...S(),F,D],I=()=>["auto","hidden","clip","visible","scroll"],H=()=>["auto","contain","none"],R=()=>[F,D,l],k=()=>[lr,"full","auto",...R()],O=()=>[Tt,"none","subgrid",F,D],$=()=>["auto",{span:["full",Tt,F,D]},Tt,F,D],j=()=>[Tt,"auto",F,D],N=()=>["auto","min","max","fr",F,D],T=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],pe=()=>["start","end","center","stretch","center-safe","end-safe"],Z=()=>["auto",...R()],ge=()=>[lr,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...R()],B=()=>[t,F,D],G=()=>[...S(),hl,pl,{position:[F,D]}],fe=()=>["no-repeat",{repeat:["","x","y","space","round"]}],le=()=>["auto","cover","contain",y0,v0,{size:[F,D]}],U=()=>[fo,Dr,Zt],L=()=>["","none","full",c,F,D],te=()=>["",ie,Dr,Zt],se=()=>["solid","dashed","dotted","double"],Pe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],X=()=>[ie,fo,hl,pl],he=()=>["","none",b,F,D],ke=()=>["none",ie,F,D],ye=()=>["none",ie,F,D],Ie=()=>[ie,F,D],Ze=()=>[lr,"full",...R()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[_t],breakpoint:[_t],color:[p0],container:[_t],"drop-shadow":[_t],ease:["in","out","in-out"],font:[g0],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[_t],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[_t],shadow:[_t],spacing:["px",ie],text:[_t],"text-shadow":[_t],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",lr,D,F,w]}],container:["container"],columns:[{columns:[ie,D,F,s]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:E()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:H()}],"overscroll-x":[{"overscroll-x":H()}],"overscroll-y":[{"overscroll-y":H()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:k()}],"inset-x":[{"inset-x":k()}],"inset-y":[{"inset-y":k()}],start:[{start:k()}],end:[{end:k()}],top:[{top:k()}],right:[{right:k()}],bottom:[{bottom:k()}],left:[{left:k()}],visibility:["visible","invisible","collapse"],z:[{z:[Tt,"auto",F,D]}],basis:[{basis:[lr,"full","auto",s,...R()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[ie,lr,"auto","initial","none",D]}],grow:[{grow:["",ie,F,D]}],shrink:[{shrink:["",ie,F,D]}],order:[{order:[Tt,"first","last","none",F,D]}],"grid-cols":[{"grid-cols":O()}],"col-start-end":[{col:$()}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":O()}],"row-start-end":[{row:$()}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":N()}],"auto-rows":[{"auto-rows":N()}],gap:[{gap:R()}],"gap-x":[{"gap-x":R()}],"gap-y":[{"gap-y":R()}],"justify-content":[{justify:[...T(),"normal"]}],"justify-items":[{"justify-items":[...pe(),"normal"]}],"justify-self":[{"justify-self":["auto",...pe()]}],"align-content":[{content:["normal",...T()]}],"align-items":[{items:[...pe(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...pe(),{baseline:["","last"]}]}],"place-content":[{"place-content":T()}],"place-items":[{"place-items":[...pe(),"baseline"]}],"place-self":[{"place-self":["auto",...pe()]}],p:[{p:R()}],px:[{px:R()}],py:[{py:R()}],ps:[{ps:R()}],pe:[{pe:R()}],pt:[{pt:R()}],pr:[{pr:R()}],pb:[{pb:R()}],pl:[{pl:R()}],m:[{m:Z()}],mx:[{mx:Z()}],my:[{my:Z()}],ms:[{ms:Z()}],me:[{me:Z()}],mt:[{mt:Z()}],mr:[{mr:Z()}],mb:[{mb:Z()}],ml:[{ml:Z()}],"space-x":[{"space-x":R()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":R()}],"space-y-reverse":["space-y-reverse"],size:[{size:ge()}],w:[{w:[s,"screen",...ge()]}],"min-w":[{"min-w":[s,"screen","none",...ge()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...ge()]}],h:[{h:["screen",...ge()]}],"min-h":[{"min-h":["screen","none",...ge()]}],"max-h":[{"max-h":["screen",...ge()]}],"font-size":[{text:["base",r,Dr,Zt]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,F,mo]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",fo,D]}],"font-family":[{font:[w0,D,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[i,F,D]}],"line-clamp":[{"line-clamp":[ie,"none",F,mo]}],leading:[{leading:[o,...R()]}],"list-image":[{"list-image":["none",F,D]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",F,D]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:B()}],"text-color":[{text:B()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...se(),"wavy"]}],"text-decoration-thickness":[{decoration:[ie,"from-font","auto",F,Zt]}],"text-decoration-color":[{decoration:B()}],"underline-offset":[{"underline-offset":[ie,"auto",F,D]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F,D]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F,D]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:G()}],"bg-repeat":[{bg:fe()}],"bg-size":[{bg:le()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Tt,F,D],radial:["",F,D],conic:[Tt,F,D]},_0,b0]}],"bg-color":[{bg:B()}],"gradient-from-pos":[{from:U()}],"gradient-via-pos":[{via:U()}],"gradient-to-pos":[{to:U()}],"gradient-from":[{from:B()}],"gradient-via":[{via:B()}],"gradient-to":[{to:B()}],rounded:[{rounded:L()}],"rounded-s":[{"rounded-s":L()}],"rounded-e":[{"rounded-e":L()}],"rounded-t":[{"rounded-t":L()}],"rounded-r":[{"rounded-r":L()}],"rounded-b":[{"rounded-b":L()}],"rounded-l":[{"rounded-l":L()}],"rounded-ss":[{"rounded-ss":L()}],"rounded-se":[{"rounded-se":L()}],"rounded-ee":[{"rounded-ee":L()}],"rounded-es":[{"rounded-es":L()}],"rounded-tl":[{"rounded-tl":L()}],"rounded-tr":[{"rounded-tr":L()}],"rounded-br":[{"rounded-br":L()}],"rounded-bl":[{"rounded-bl":L()}],"border-w":[{border:te()}],"border-w-x":[{"border-x":te()}],"border-w-y":[{"border-y":te()}],"border-w-s":[{"border-s":te()}],"border-w-e":[{"border-e":te()}],"border-w-t":[{"border-t":te()}],"border-w-r":[{"border-r":te()}],"border-w-b":[{"border-b":te()}],"border-w-l":[{"border-l":te()}],"divide-x":[{"divide-x":te()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":te()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...se(),"hidden","none"]}],"divide-style":[{divide:[...se(),"hidden","none"]}],"border-color":[{border:B()}],"border-color-x":[{"border-x":B()}],"border-color-y":[{"border-y":B()}],"border-color-s":[{"border-s":B()}],"border-color-e":[{"border-e":B()}],"border-color-t":[{"border-t":B()}],"border-color-r":[{"border-r":B()}],"border-color-b":[{"border-b":B()}],"border-color-l":[{"border-l":B()}],"divide-color":[{divide:B()}],"outline-style":[{outline:[...se(),"none","hidden"]}],"outline-offset":[{"outline-offset":[ie,F,D]}],"outline-w":[{outline:["",ie,Dr,Zt]}],"outline-color":[{outline:B()}],shadow:[{shadow:["","none",u,qn,Vn]}],"shadow-color":[{shadow:B()}],"inset-shadow":[{"inset-shadow":["none",d,qn,Vn]}],"inset-shadow-color":[{"inset-shadow":B()}],"ring-w":[{ring:te()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:B()}],"ring-offset-w":[{"ring-offset":[ie,Zt]}],"ring-offset-color":[{"ring-offset":B()}],"inset-ring-w":[{"inset-ring":te()}],"inset-ring-color":[{"inset-ring":B()}],"text-shadow":[{"text-shadow":["none",p,qn,Vn]}],"text-shadow-color":[{"text-shadow":B()}],opacity:[{opacity:[ie,F,D]}],"mix-blend":[{"mix-blend":[...Pe(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Pe()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[ie]}],"mask-image-linear-from-pos":[{"mask-linear-from":X()}],"mask-image-linear-to-pos":[{"mask-linear-to":X()}],"mask-image-linear-from-color":[{"mask-linear-from":B()}],"mask-image-linear-to-color":[{"mask-linear-to":B()}],"mask-image-t-from-pos":[{"mask-t-from":X()}],"mask-image-t-to-pos":[{"mask-t-to":X()}],"mask-image-t-from-color":[{"mask-t-from":B()}],"mask-image-t-to-color":[{"mask-t-to":B()}],"mask-image-r-from-pos":[{"mask-r-from":X()}],"mask-image-r-to-pos":[{"mask-r-to":X()}],"mask-image-r-from-color":[{"mask-r-from":B()}],"mask-image-r-to-color":[{"mask-r-to":B()}],"mask-image-b-from-pos":[{"mask-b-from":X()}],"mask-image-b-to-pos":[{"mask-b-to":X()}],"mask-image-b-from-color":[{"mask-b-from":B()}],"mask-image-b-to-color":[{"mask-b-to":B()}],"mask-image-l-from-pos":[{"mask-l-from":X()}],"mask-image-l-to-pos":[{"mask-l-to":X()}],"mask-image-l-from-color":[{"mask-l-from":B()}],"mask-image-l-to-color":[{"mask-l-to":B()}],"mask-image-x-from-pos":[{"mask-x-from":X()}],"mask-image-x-to-pos":[{"mask-x-to":X()}],"mask-image-x-from-color":[{"mask-x-from":B()}],"mask-image-x-to-color":[{"mask-x-to":B()}],"mask-image-y-from-pos":[{"mask-y-from":X()}],"mask-image-y-to-pos":[{"mask-y-to":X()}],"mask-image-y-from-color":[{"mask-y-from":B()}],"mask-image-y-to-color":[{"mask-y-to":B()}],"mask-image-radial":[{"mask-radial":[F,D]}],"mask-image-radial-from-pos":[{"mask-radial-from":X()}],"mask-image-radial-to-pos":[{"mask-radial-to":X()}],"mask-image-radial-from-color":[{"mask-radial-from":B()}],"mask-image-radial-to-color":[{"mask-radial-to":B()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":S()}],"mask-image-conic-pos":[{"mask-conic":[ie]}],"mask-image-conic-from-pos":[{"mask-conic-from":X()}],"mask-image-conic-to-pos":[{"mask-conic-to":X()}],"mask-image-conic-from-color":[{"mask-conic-from":B()}],"mask-image-conic-to-color":[{"mask-conic-to":B()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:G()}],"mask-repeat":[{mask:fe()}],"mask-size":[{mask:le()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",F,D]}],filter:[{filter:["","none",F,D]}],blur:[{blur:he()}],brightness:[{brightness:[ie,F,D]}],contrast:[{contrast:[ie,F,D]}],"drop-shadow":[{"drop-shadow":["","none",h,qn,Vn]}],"drop-shadow-color":[{"drop-shadow":B()}],grayscale:[{grayscale:["",ie,F,D]}],"hue-rotate":[{"hue-rotate":[ie,F,D]}],invert:[{invert:["",ie,F,D]}],saturate:[{saturate:[ie,F,D]}],sepia:[{sepia:["",ie,F,D]}],"backdrop-filter":[{"backdrop-filter":["","none",F,D]}],"backdrop-blur":[{"backdrop-blur":he()}],"backdrop-brightness":[{"backdrop-brightness":[ie,F,D]}],"backdrop-contrast":[{"backdrop-contrast":[ie,F,D]}],"backdrop-grayscale":[{"backdrop-grayscale":["",ie,F,D]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[ie,F,D]}],"backdrop-invert":[{"backdrop-invert":["",ie,F,D]}],"backdrop-opacity":[{"backdrop-opacity":[ie,F,D]}],"backdrop-saturate":[{"backdrop-saturate":[ie,F,D]}],"backdrop-sepia":[{"backdrop-sepia":["",ie,F,D]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":R()}],"border-spacing-x":[{"border-spacing-x":R()}],"border-spacing-y":[{"border-spacing-y":R()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",F,D]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[ie,"initial",F,D]}],ease:[{ease:["linear","initial",y,F,D]}],delay:[{delay:[ie,F,D]}],animate:[{animate:["none",v,F,D]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,F,D]}],"perspective-origin":[{"perspective-origin":E()}],rotate:[{rotate:ke()}],"rotate-x":[{"rotate-x":ke()}],"rotate-y":[{"rotate-y":ke()}],"rotate-z":[{"rotate-z":ke()}],scale:[{scale:ye()}],"scale-x":[{"scale-x":ye()}],"scale-y":[{"scale-y":ye()}],"scale-z":[{"scale-z":ye()}],"scale-3d":["scale-3d"],skew:[{skew:Ie()}],"skew-x":[{"skew-x":Ie()}],"skew-y":[{"skew-y":Ie()}],transform:[{transform:[F,D,"","none","gpu","cpu"]}],"transform-origin":[{origin:E()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Ze()}],"translate-x":[{"translate-x":Ze()}],"translate-y":[{"translate-y":Ze()}],"translate-z":[{"translate-z":Ze()}],"translate-none":["translate-none"],accent:[{accent:B()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:B()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F,D]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F,D]}],fill:[{fill:["none",...B()]}],"stroke-w":[{stroke:[ie,Dr,Zt,mo]}],stroke:[{stroke:["none",...B()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},S0=(t,{cacheSize:e,prefix:r,experimentalParseClassName:n,extend:i={},override:o={}})=>(Vr(t,"cacheSize",e),Vr(t,"prefix",r),Vr(t,"experimentalParseClassName",n),Un(t.theme,o.theme),Un(t.classGroups,o.classGroups),Un(t.conflictingClassGroups,o.conflictingClassGroups),Un(t.conflictingClassGroupModifiers,o.conflictingClassGroupModifiers),Vr(t,"orderSensitiveModifiers",o.orderSensitiveModifiers),Gn(t.theme,i.theme),Gn(t.classGroups,i.classGroups),Gn(t.conflictingClassGroups,i.conflictingClassGroups),Gn(t.conflictingClassGroupModifiers,i.conflictingClassGroupModifiers),Bd(t,i,"orderSensitiveModifiers"),t),Vr=(t,e,r)=>{r!==void 0&&(t[e]=r)},Un=(t,e)=>{if(e)for(const r in e)Vr(t,r,e[r])},Gn=(t,e)=>{if(e)for(const r in e)Bd(t,e,r)},Bd=(t,e,r)=>{const n=e[r];n!==void 0&&(t[r]=t[r]?t[r].concat(n):n)},C0=(t,...e)=>typeof t=="function"?ul(fl,t,...e):ul(()=>S0(fl(),t),...e),_n="stagewise-companion-anchor";function E0(t,e){return document.elementsFromPoint(t,e).find(r=>r.nodeName!=="STAGEWISE-COMPANION-ANCHOR"&&!r.closest(_n)&&!r.closest("svg")&&O0(r,t,e))||document.body}const O0=(t,e,r)=>{const n=t.getBoundingClientRect(),i=e>n.left&&e<n.left+n.width,o=r>n.top&&r<n.top+n.height;return i&&o};var Ci=(t=>(t[t.ESC=0]="ESC",t[t.CTRL_ALT_C=1]="CTRL_ALT_C",t))(Ci||{});const na={0:{keyComboDefault:"Esc",keyComboMac:"esc",isEventMatching:t=>t.code==="Escape"},1:{keyComboDefault:"Ctrl+Alt+C",keyComboMac:"⌘+⌥+C",isEventMatching:t=>t.code==="KeyC"&&(t.ctrlKey||t.metaKey)&&t.altKey}},N0=C0({extend:{classGroups:{"bg-image":["bg-gradient","bg-gradient-light-1","bg-gradient-light-2","bg-gradient-light-3"]}}});function Ae(...t){return N0(Uv(t))}const go=(t=16)=>Math.random().toString(36).substring(2,t+2);function Qt({children:t,alwaysFullHeight:e=!1}){return m("section",{className:Ae("flex max-h-full min-h-48 flex-col items-stretch justify-start rounded-2xl border border-border/30 bg-zinc-50/80 p-4 shadow-md backdrop-blur-md",e&&"h-full"),children:t})}Qt.Header=function({title:t,description:e}){return m("header",{className:"mb-3 flex flex-col gap-1 text-zinc-950",children:[t&&m("h3",{className:"font-semibold text-lg ",children:t}),e&&m("p",{className:"font-medium text-zinc-600",children:e})]})};Qt.Content=function({children:t}){return m("div",{className:"-mx-4 flex flex-col gap-2 overflow-y-auto border-border/30 border-t px-4 pt-4 text-zinc-950",children:t})};Qt.Footer=function({children:t}){return m("footer",{className:"flex flex-row items-end justify-end gap-2 text-sm text-zinc-600",children:t})};const P0='/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){stagewise-companion-anchor *,stagewise-companion-anchor :before,stagewise-companion-anchor :after,stagewise-companion-anchor ::backdrop{--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-gradient-position:initial;--tw-gradient-from:#0000;--tw-gradient-via:#0000;--tw-gradient-to:#0000;--tw-gradient-stops:initial;--tw-gradient-via-stops:initial;--tw-gradient-from-position:0%;--tw-gradient-via-position:50%;--tw-gradient-to-position:100%;--tw-font-weight:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial}}}@layer theme{stagewise-companion-anchor{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-red-100:oklch(93.6% .032 17.717);--color-red-200:oklch(88.5% .062 18.334);--color-red-500:oklch(63.7% .237 25.331);--color-red-600:oklch(57.7% .245 27.325);--color-red-700:oklch(50.5% .213 27.518);--color-orange-50:oklch(98% .016 73.684);--color-orange-100:oklch(95.4% .038 75.164);--color-orange-200:oklch(90.1% .076 70.697);--color-orange-300:oklch(83.7% .128 66.29);--color-orange-500:oklch(70.5% .213 47.604);--color-orange-600:oklch(64.6% .222 41.116);--color-orange-700:oklch(55.3% .195 38.402);--color-orange-800:oklch(47% .157 37.304);--color-amber-50:oklch(98.7% .022 95.277);--color-amber-800:oklch(47.3% .137 46.201);--color-yellow-500:oklch(79.5% .184 86.047);--color-green-500:oklch(72.3% .219 149.579);--color-green-600:oklch(62.7% .194 149.214);--color-teal-500:oklch(70.4% .14 182.503);--color-sky-600:oklch(58.8% .158 241.966);--color-sky-700:oklch(50% .134 242.749);--color-blue-50:oklch(97% .014 254.604);--color-blue-100:oklch(93.2% .032 255.585);--color-blue-200:oklch(88.2% .059 254.128);--color-blue-300:oklch(80.9% .105 251.813);--color-blue-500:oklch(62.3% .214 259.815);--color-blue-600:oklch(54.6% .245 262.881);--color-blue-700:oklch(48.8% .243 264.376);--color-blue-800:oklch(42.4% .199 265.638);--color-indigo-700:oklch(45.7% .24 277.023);--color-indigo-950:oklch(25.7% .09 281.288);--color-violet-700:oklch(49.1% .27 292.581);--color-purple-500:oklch(62.7% .265 303.9);--color-fuchsia-700:oklch(51.8% .253 323.949);--color-pink-500:oklch(65.6% .241 354.308);--color-rose-600:oklch(58.6% .253 17.585);--color-zinc-50:oklch(98.5% 0 0);--color-zinc-100:oklch(96.7% .001 286.375);--color-zinc-300:oklch(87.1% .006 286.286);--color-zinc-400:oklch(70.5% .015 286.067);--color-zinc-500:oklch(55.2% .016 285.938);--color-zinc-600:oklch(44.2% .017 285.786);--color-zinc-700:oklch(37% .013 285.805);--color-zinc-900:oklch(21% .006 285.885);--color-zinc-950:oklch(14.1% .005 285.823);--color-black:#000;--color-white:#fff;--spacing:.25rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height: 1.5 ;--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--radius-md:.375rem;--radius-lg:.5rem;--radius-2xl:1rem;--radius-3xl:1.5rem;--drop-shadow-xs:0 1px 1px #0000000d;--drop-shadow-md:0 3px 3px #0000001f;--drop-shadow-xl:0 9px 7px #0000001a;--ease-out:cubic-bezier(0,0,.2,1);--animate-spin:spin 1s linear infinite;--animate-pulse:pulse 2s cubic-bezier(.4,0,.6,1)infinite;--blur-md:12px;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-background:var(--color-white);--color-foreground:var(--color-zinc-950);--color-border:var(--color-zinc-500)}}@layer base{stagewise-companion-anchor *,stagewise-companion-anchor :after,stagewise-companion-anchor :before,stagewise-companion-anchor ::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}stagewise-companion-anchor ::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}:where(stagewise-companion-anchor),stagewise-companion-anchor{-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}stagewise-companion-anchor hr{height:0;color:inherit;border-top-width:1px}stagewise-companion-anchor abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}stagewise-companion-anchor h1,stagewise-companion-anchor h2,stagewise-companion-anchor h3,stagewise-companion-anchor h4,stagewise-companion-anchor h5,stagewise-companion-anchor h6{font-size:inherit;font-weight:inherit}stagewise-companion-anchor a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}stagewise-companion-anchor b,stagewise-companion-anchor strong{font-weight:bolder}stagewise-companion-anchor code,stagewise-companion-anchor kbd,stagewise-companion-anchor samp,stagewise-companion-anchor pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}stagewise-companion-anchor small{font-size:80%}stagewise-companion-anchor sub,stagewise-companion-anchor sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}stagewise-companion-anchor sub{bottom:-.25em}stagewise-companion-anchor sup{top:-.5em}stagewise-companion-anchor table{text-indent:0;border-color:inherit;border-collapse:collapse}stagewise-companion-anchor :-moz-focusring{outline:auto}stagewise-companion-anchor progress{vertical-align:baseline}stagewise-companion-anchor summary{display:list-item}stagewise-companion-anchor ol,stagewise-companion-anchor ul,stagewise-companion-anchor menu{list-style:none}stagewise-companion-anchor img,stagewise-companion-anchor svg,stagewise-companion-anchor video,stagewise-companion-anchor canvas,stagewise-companion-anchor audio,stagewise-companion-anchor iframe,stagewise-companion-anchor embed,stagewise-companion-anchor object{vertical-align:middle;display:block}stagewise-companion-anchor img,stagewise-companion-anchor video{max-width:100%;height:auto}stagewise-companion-anchor button,stagewise-companion-anchor input,stagewise-companion-anchor select,stagewise-companion-anchor optgroup,stagewise-companion-anchor textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor ::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup{font-weight:bolder}stagewise-companion-anchor :where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}stagewise-companion-anchor ::file-selector-button{margin-inline-end:4px}stagewise-companion-anchor ::-moz-placeholder{opacity:1}stagewise-companion-anchor ::placeholder{opacity:1}@supports (not (-webkit-appearance:-apple-pay-button)) or (contain-intrinsic-size:1px){stagewise-companion-anchor ::-moz-placeholder{color:currentColor}stagewise-companion-anchor ::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor ::-moz-placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}stagewise-companion-anchor ::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}stagewise-companion-anchor textarea{resize:vertical}stagewise-companion-anchor ::-webkit-search-decoration{-webkit-appearance:none}stagewise-companion-anchor ::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}stagewise-companion-anchor ::-webkit-datetime-edit{display:inline-flex}stagewise-companion-anchor ::-webkit-datetime-edit-fields-wrapper{padding:0}stagewise-companion-anchor ::-webkit-datetime-edit{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-year-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-month-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-day-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-hour-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-minute-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-second-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-millisecond-field{padding-block:0}stagewise-companion-anchor ::-webkit-datetime-edit-meridiem-field{padding-block:0}stagewise-companion-anchor :-moz-ui-invalid{box-shadow:none}stagewise-companion-anchor button,stagewise-companion-anchor input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}stagewise-companion-anchor ::-webkit-inner-spin-button{height:auto}stagewise-companion-anchor ::-webkit-outer-spin-button{height:auto}stagewise-companion-anchor [hidden]:where(:not([hidden=until-found])){display:none!important}stagewise-companion-anchor stagewise-companion-anchor *{min-width:0;min-height:0;position:relative}}@layer components{stagewise-companion-anchor .chat-loading-gradient{background:linear-gradient(#f8fafccc,#f8fafccc) padding-box padding-box,linear-gradient(45deg,#8b5cf6,#06b6d4,#8b5cf6) 0 0/400% 400% border-box;border:2px solid #0000;animation:2s infinite gradient-animation}stagewise-companion-anchor .chat-success-border{animation:2s ease-out blink-green-fade}stagewise-companion-anchor .chat-error-border{animation:1s ease-out blink-red-fade}@keyframes blink-green-fade{0%,50%{box-shadow:0 0 0 2px #22c55eb3}to{box-shadow:0 0 0 2px #22c55e00}}@keyframes blink-red-fade{0%,50%{box-shadow:0 0 0 2px #ef4444}to{box-shadow:0 0 0 2px #ef444400}}}@layer utilities{stagewise-companion-anchor .pointer-events-auto{pointer-events:auto!important}stagewise-companion-anchor .pointer-events-none{pointer-events:none!important}stagewise-companion-anchor .visible{visibility:visible!important}stagewise-companion-anchor .absolute{position:absolute!important}stagewise-companion-anchor .fixed{position:fixed!important}stagewise-companion-anchor .relative{position:relative!important}stagewise-companion-anchor .inset-0{inset:calc(var(--spacing)*0)!important}stagewise-companion-anchor .inset-4{inset:calc(var(--spacing)*4)!important}stagewise-companion-anchor .top-0{top:calc(var(--spacing)*0)!important}stagewise-companion-anchor .top-0\\.5{top:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .top-1\\/2{top:50%!important}stagewise-companion-anchor .top-\\[-20\\%\\]{top:-20%!important}stagewise-companion-anchor .top-\\[25\\%\\]{top:25%!important}stagewise-companion-anchor .right-0{right:calc(var(--spacing)*0)!important}stagewise-companion-anchor .right-1\\/2{right:50%!important}stagewise-companion-anchor .right-\\[100\\%\\]{right:100%!important}stagewise-companion-anchor .bottom-0{bottom:calc(var(--spacing)*0)!important}stagewise-companion-anchor .bottom-1\\/2{bottom:50%!important}stagewise-companion-anchor .bottom-3{bottom:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-0{left:calc(var(--spacing)*0)!important}stagewise-companion-anchor .left-0\\.5{left:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .left-1\\/2{left:50%!important}stagewise-companion-anchor .left-3{left:calc(var(--spacing)*3)!important}stagewise-companion-anchor .left-\\[-10\\%\\]{left:-10%!important}stagewise-companion-anchor .left-\\[25\\%\\]{left:25%!important}stagewise-companion-anchor .left-\\[100\\%\\]{left:100%!important}stagewise-companion-anchor .z-20{z-index:20!important}stagewise-companion-anchor .z-50{z-index:50!important}stagewise-companion-anchor .container{width:100%!important}@media (min-width:40rem){stagewise-companion-anchor .container{max-width:40rem!important}}@media (min-width:48rem){stagewise-companion-anchor .container{max-width:48rem!important}}@media (min-width:64rem){stagewise-companion-anchor .container{max-width:64rem!important}}@media (min-width:80rem){stagewise-companion-anchor .container{max-width:80rem!important}}@media (min-width:96rem){stagewise-companion-anchor .container{max-width:96rem!important}}stagewise-companion-anchor .-mx-4{margin-inline:calc(var(--spacing)*-4)!important}stagewise-companion-anchor .my-2{margin-block:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mt-1{margin-top:calc(var(--spacing)*1)!important}stagewise-companion-anchor .mt-2{margin-top:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mb-2{margin-bottom:calc(var(--spacing)*2)!important}stagewise-companion-anchor .mb-3{margin-bottom:calc(var(--spacing)*3)!important}stagewise-companion-anchor .block{display:block!important}stagewise-companion-anchor .contents{display:contents!important}stagewise-companion-anchor .flex{display:flex!important}stagewise-companion-anchor .hidden{display:none!important}stagewise-companion-anchor .inline{display:inline!important}stagewise-companion-anchor .aspect-square{aspect-ratio:1!important}stagewise-companion-anchor .size-0{width:calc(var(--spacing)*0)!important;height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .size-1\\.5{width:calc(var(--spacing)*1.5)!important;height:calc(var(--spacing)*1.5)!important}stagewise-companion-anchor .size-2\\/3{width:66.6667%!important;height:66.6667%!important}stagewise-companion-anchor .size-3{width:calc(var(--spacing)*3)!important;height:calc(var(--spacing)*3)!important}stagewise-companion-anchor .size-4{width:calc(var(--spacing)*4)!important;height:calc(var(--spacing)*4)!important}stagewise-companion-anchor .size-4\\.5{width:calc(var(--spacing)*4.5)!important;height:calc(var(--spacing)*4.5)!important}stagewise-companion-anchor .size-5{width:calc(var(--spacing)*5)!important;height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .size-6{width:calc(var(--spacing)*6)!important;height:calc(var(--spacing)*6)!important}stagewise-companion-anchor .size-8{width:calc(var(--spacing)*8)!important;height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .size-9{width:calc(var(--spacing)*9)!important;height:calc(var(--spacing)*9)!important}stagewise-companion-anchor .size-9\\/12{width:75%!important;height:75%!important}stagewise-companion-anchor .size-12{width:calc(var(--spacing)*12)!important;height:calc(var(--spacing)*12)!important}stagewise-companion-anchor .size-\\[120\\%\\]{width:120%!important;height:120%!important}stagewise-companion-anchor .size-full{width:100%!important;height:100%!important}stagewise-companion-anchor .h-0{height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .h-3{height:calc(var(--spacing)*3)!important}stagewise-companion-anchor .h-5{height:calc(var(--spacing)*5)!important}stagewise-companion-anchor .h-8{height:calc(var(--spacing)*8)!important}stagewise-companion-anchor .h-9\\.5{height:calc(var(--spacing)*9.5)!important}stagewise-companion-anchor .h-12{height:calc(var(--spacing)*12)!important}stagewise-companion-anchor .h-16{height:calc(var(--spacing)*16)!important}stagewise-companion-anchor .h-24{height:calc(var(--spacing)*24)!important}stagewise-companion-anchor .h-\\[50\\%\\]{height:50%!important}stagewise-companion-anchor .h-\\[120\\%\\]{height:120%!important}stagewise-companion-anchor .h-\\[calc\\(100vh-32px\\)\\]{height:calc(100vh - 32px)!important}stagewise-companion-anchor .h-\\[calc-size\\(auto\\)\\]{height:calc-size(auto)!important}stagewise-companion-anchor .h-\\[calc-size\\(auto\\,size\\)\\]{height:calc-size(auto,size)!important}stagewise-companion-anchor .h-auto{height:auto!important}stagewise-companion-anchor .h-full{height:100%!important}stagewise-companion-anchor .h-screen{height:100vh!important}stagewise-companion-anchor .max-h-full{max-height:100%!important}stagewise-companion-anchor .min-h-0{min-height:calc(var(--spacing)*0)!important}stagewise-companion-anchor .min-h-48{min-height:calc(var(--spacing)*48)!important}stagewise-companion-anchor .w-8{width:calc(var(--spacing)*8)!important}stagewise-companion-anchor .w-9\\.5{width:calc(var(--spacing)*9.5)!important}stagewise-companion-anchor .w-96{width:calc(var(--spacing)*96)!important}stagewise-companion-anchor .w-\\[50\\%\\]{width:50%!important}stagewise-companion-anchor .w-auto{width:auto!important}stagewise-companion-anchor .w-fit{width:-moz-fit-content!important;width:fit-content!important}stagewise-companion-anchor .w-full{width:100%!important}stagewise-companion-anchor .w-max{width:-moz-max-content!important;width:max-content!important}stagewise-companion-anchor .w-screen{width:100vw!important}stagewise-companion-anchor .max-w-8{max-width:calc(var(--spacing)*8)!important}stagewise-companion-anchor .max-w-90{max-width:calc(var(--spacing)*90)!important}stagewise-companion-anchor .max-w-\\[40vw\\]{max-width:40vw!important}stagewise-companion-anchor .max-w-full{max-width:100%!important}stagewise-companion-anchor .min-w-0{min-width:calc(var(--spacing)*0)!important}stagewise-companion-anchor .min-w-3{min-width:calc(var(--spacing)*3)!important}stagewise-companion-anchor .min-w-24{min-width:calc(var(--spacing)*24)!important}stagewise-companion-anchor .flex-1{flex:1!important}stagewise-companion-anchor .flex-shrink-0,stagewise-companion-anchor .shrink-0{flex-shrink:0!important}stagewise-companion-anchor .origin-bottom{transform-origin:bottom!important}stagewise-companion-anchor .origin-bottom-left{transform-origin:0 100%!important}stagewise-companion-anchor .origin-bottom-right{transform-origin:100% 100%!important}stagewise-companion-anchor .origin-center{transform-origin:50%!important}stagewise-companion-anchor .origin-top{transform-origin:top!important}stagewise-companion-anchor .origin-top-left{transform-origin:0 0!important}stagewise-companion-anchor .origin-top-right{transform-origin:100% 0!important}stagewise-companion-anchor .scale-25{--tw-scale-x:25%!important;--tw-scale-y:25%!important;--tw-scale-z:25%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .scale-50{--tw-scale-x:50%!important;--tw-scale-y:50%!important;--tw-scale-z:50%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .scale-100{--tw-scale-x:100%!important;--tw-scale-y:100%!important;--tw-scale-z:100%!important;scale:var(--tw-scale-x)var(--tw-scale-y)!important}stagewise-companion-anchor .transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)!important}stagewise-companion-anchor .animate-pulse{animation:var(--animate-pulse)!important}stagewise-companion-anchor .animate-spin{animation:var(--animate-spin)!important}stagewise-companion-anchor .cursor-copy{cursor:copy!important}stagewise-companion-anchor .cursor-not-allowed{cursor:not-allowed!important}stagewise-companion-anchor .cursor-pointer{cursor:pointer!important}stagewise-companion-anchor .resize{resize:both!important}stagewise-companion-anchor .resize-none{resize:none!important}stagewise-companion-anchor .snap-start{scroll-snap-align:start!important}stagewise-companion-anchor .list-inside{list-style-position:inside!important}stagewise-companion-anchor .list-decimal{list-style-type:decimal!important}stagewise-companion-anchor .flex-col{flex-direction:column!important}stagewise-companion-anchor .flex-col-reverse{flex-direction:column-reverse!important}stagewise-companion-anchor .flex-row{flex-direction:row!important}stagewise-companion-anchor .flex-wrap{flex-wrap:wrap!important}stagewise-companion-anchor .items-center{align-items:center!important}stagewise-companion-anchor .items-end{align-items:flex-end!important}stagewise-companion-anchor .items-start{align-items:flex-start!important}stagewise-companion-anchor .items-stretch{align-items:stretch!important}stagewise-companion-anchor .justify-between{justify-content:space-between!important}stagewise-companion-anchor .justify-center{justify-content:center!important}stagewise-companion-anchor .justify-end{justify-content:flex-end!important}stagewise-companion-anchor .justify-start{justify-content:flex-start!important}stagewise-companion-anchor .gap-0\\.5{gap:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .gap-1{gap:calc(var(--spacing)*1)!important}stagewise-companion-anchor .gap-2{gap:calc(var(--spacing)*2)!important}stagewise-companion-anchor .gap-3{gap:calc(var(--spacing)*3)!important}stagewise-companion-anchor :where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0!important;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse))!important;margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))!important}stagewise-companion-anchor :where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0!important;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse))!important;margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))!important}stagewise-companion-anchor :where(.divide-y>:not(:last-child)){--tw-divide-y-reverse:0!important;border-bottom-style:var(--tw-border-style)!important;border-top-style:var(--tw-border-style)!important;border-top-width:calc(1px*var(--tw-divide-y-reverse))!important;border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))!important}stagewise-companion-anchor :where(.divide-y-reverse>:not(:last-child)){--tw-divide-y-reverse:1!important}stagewise-companion-anchor :where(.divide-blue-200>:not(:last-child)){border-color:var(--color-blue-200)!important}stagewise-companion-anchor :where(.divide-border\\/20>:not(:last-child)){border-color:#71717b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor :where(.divide-border\\/20>:not(:last-child)){border-color:color-mix(in oklab,var(--color-border)20%,transparent)!important}}stagewise-companion-anchor :where(.divide-orange-200>:not(:last-child)){border-color:var(--color-orange-200)!important}stagewise-companion-anchor .truncate{text-overflow:ellipsis!important;white-space:nowrap!important;overflow:hidden!important}stagewise-companion-anchor .overflow-hidden{overflow:hidden!important}stagewise-companion-anchor .overflow-visible{overflow:visible!important}stagewise-companion-anchor .overflow-y-auto{overflow-y:auto!important}stagewise-companion-anchor .rounded{border-radius:.25rem!important}stagewise-companion-anchor .rounded-2xl{border-radius:var(--radius-2xl)!important}stagewise-companion-anchor .rounded-full{border-radius:3.40282e38px!important}stagewise-companion-anchor .rounded-lg{border-radius:var(--radius-lg)!important}stagewise-companion-anchor .rounded-md{border-radius:var(--radius-md)!important}stagewise-companion-anchor .rounded-t-3xl{border-top-left-radius:var(--radius-3xl)!important;border-top-right-radius:var(--radius-3xl)!important}stagewise-companion-anchor .rounded-t-lg{border-top-left-radius:var(--radius-lg)!important;border-top-right-radius:var(--radius-lg)!important}stagewise-companion-anchor .rounded-b-3xl{border-bottom-right-radius:var(--radius-3xl)!important;border-bottom-left-radius:var(--radius-3xl)!important}stagewise-companion-anchor .rounded-b-lg{border-bottom-right-radius:var(--radius-lg)!important;border-bottom-left-radius:var(--radius-lg)!important}stagewise-companion-anchor .border{border-style:var(--tw-border-style)!important;border-width:1px!important}stagewise-companion-anchor .border-2{border-style:var(--tw-border-style)!important;border-width:2px!important}stagewise-companion-anchor .border-t{border-top-style:var(--tw-border-style)!important;border-top-width:1px!important}stagewise-companion-anchor .border-solid{--tw-border-style:solid!important;border-style:solid!important}stagewise-companion-anchor .border-blue-200{border-color:var(--color-blue-200)!important}stagewise-companion-anchor .border-blue-300{border-color:var(--color-blue-300)!important}stagewise-companion-anchor .border-blue-500{border-color:var(--color-blue-500)!important}stagewise-companion-anchor .border-blue-600\\/80{border-color:#155dfccc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-blue-600\\/80{border-color:color-mix(in oklab,var(--color-blue-600)80%,transparent)!important}}stagewise-companion-anchor .border-border\\/30{border-color:#71717b4d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-border\\/30{border-color:color-mix(in oklab,var(--color-border)30%,transparent)!important}}stagewise-companion-anchor .border-green-500{border-color:var(--color-green-500)!important}stagewise-companion-anchor .border-green-600\\/80{border-color:#00a544cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .border-green-600\\/80{border-color:color-mix(in oklab,var(--color-green-600)80%,transparent)!important}}stagewise-companion-anchor .border-orange-200{border-color:var(--color-orange-200)!important}stagewise-companion-anchor .border-orange-300{border-color:var(--color-orange-300)!important}stagewise-companion-anchor .border-orange-500{border-color:var(--color-orange-500)!important}stagewise-companion-anchor .border-pink-500{border-color:var(--color-pink-500)!important}stagewise-companion-anchor .border-purple-500{border-color:var(--color-purple-500)!important}stagewise-companion-anchor .border-red-200{border-color:var(--color-red-200)!important}stagewise-companion-anchor .border-red-500{border-color:var(--color-red-500)!important}stagewise-companion-anchor .border-transparent{border-color:#0000!important}stagewise-companion-anchor .border-yellow-500{border-color:var(--color-yellow-500)!important}stagewise-companion-anchor .border-zinc-300{border-color:var(--color-zinc-300)!important}stagewise-companion-anchor .border-zinc-500{border-color:var(--color-zinc-500)!important}stagewise-companion-anchor .bg-amber-50{background-color:var(--color-amber-50)!important}stagewise-companion-anchor .bg-background\\/60{background-color:#fff9!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-background\\/60{background-color:color-mix(in oklab,var(--color-background)60%,transparent)!important}}stagewise-companion-anchor .bg-blue-50{background-color:var(--color-blue-50)!important}stagewise-companion-anchor .bg-blue-50\\/90{background-color:#eff6ffe6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-50\\/90{background-color:color-mix(in oklab,var(--color-blue-50)90%,transparent)!important}}stagewise-companion-anchor .bg-blue-100\\/80{background-color:#dbeafecc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-100\\/80{background-color:color-mix(in oklab,var(--color-blue-100)80%,transparent)!important}}stagewise-companion-anchor .bg-blue-500{background-color:var(--color-blue-500)!important}stagewise-companion-anchor .bg-blue-600{background-color:var(--color-blue-600)!important}stagewise-companion-anchor .bg-blue-600\\/20{background-color:#155dfc33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-blue-600\\/20{background-color:color-mix(in oklab,var(--color-blue-600)20%,transparent)!important}}stagewise-companion-anchor .bg-green-500{background-color:var(--color-green-500)!important}stagewise-companion-anchor .bg-green-600\\/5{background-color:#00a5440d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-green-600\\/5{background-color:color-mix(in oklab,var(--color-green-600)5%,transparent)!important}}stagewise-companion-anchor .bg-orange-50\\/90{background-color:#fff7ede6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-orange-50\\/90{background-color:color-mix(in oklab,var(--color-orange-50)90%,transparent)!important}}stagewise-companion-anchor .bg-orange-100\\/80{background-color:#ffedd5cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-orange-100\\/80{background-color:color-mix(in oklab,var(--color-orange-100)80%,transparent)!important}}stagewise-companion-anchor .bg-orange-500{background-color:var(--color-orange-500)!important}stagewise-companion-anchor .bg-orange-600{background-color:var(--color-orange-600)!important}stagewise-companion-anchor .bg-pink-500{background-color:var(--color-pink-500)!important}stagewise-companion-anchor .bg-purple-500{background-color:var(--color-purple-500)!important}stagewise-companion-anchor .bg-red-100{background-color:var(--color-red-100)!important}stagewise-companion-anchor .bg-red-500{background-color:var(--color-red-500)!important}stagewise-companion-anchor .bg-rose-600{background-color:var(--color-rose-600)!important}stagewise-companion-anchor .bg-transparent{background-color:#0000!important}stagewise-companion-anchor .bg-white{background-color:var(--color-white)!important}stagewise-companion-anchor .bg-white\\/40{background-color:#fff6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/40{background-color:color-mix(in oklab,var(--color-white)40%,transparent)!important}}stagewise-companion-anchor .bg-white\\/80{background-color:#fffc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/80{background-color:color-mix(in oklab,var(--color-white)80%,transparent)!important}}stagewise-companion-anchor .bg-white\\/90{background-color:#ffffffe6!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-white\\/90{background-color:color-mix(in oklab,var(--color-white)90%,transparent)!important}}stagewise-companion-anchor .bg-yellow-500{background-color:var(--color-yellow-500)!important}stagewise-companion-anchor .bg-zinc-50\\/80{background-color:#fafafacc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-50\\/80{background-color:color-mix(in oklab,var(--color-zinc-50)80%,transparent)!important}}stagewise-companion-anchor .bg-zinc-300{background-color:var(--color-zinc-300)!important}stagewise-companion-anchor .bg-zinc-500{background-color:var(--color-zinc-500)!important}stagewise-companion-anchor .bg-zinc-500\\/10{background-color:#71717b1a!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-500\\/10{background-color:color-mix(in oklab,var(--color-zinc-500)10%,transparent)!important}}stagewise-companion-anchor .bg-zinc-500\\/40{background-color:#71717b66!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-500\\/40{background-color:color-mix(in oklab,var(--color-zinc-500)40%,transparent)!important}}stagewise-companion-anchor .bg-zinc-700\\/80{background-color:#3f3f46cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .bg-zinc-700\\/80{background-color:color-mix(in oklab,var(--color-zinc-700)80%,transparent)!important}}stagewise-companion-anchor .bg-gradient-to-tr{--tw-gradient-position:to top right in oklab!important;background-image:linear-gradient(var(--tw-gradient-stops))!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(55\\,48\\,163\\,0\\)_55\\%\\,rgba\\(55\\,48\\,163\\,0\\.35\\)_73\\%\\)\\]{background-image:radial-gradient(circle,#3730a300 55%,#3730a359 73%)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(219\\,39\\,119\\,0\\.2\\)_0\\%\\,rgba\\(219\\,39\\,119\\,0\\)_100\\%\\)\\]{background-image:radial-gradient(circle,#db277733,#db277700)!important}stagewise-companion-anchor .bg-\\[radial-gradient\\(circle\\,rgba\\(255\\,255\\,255\\,0\\)_60\\%\\,rgba\\(255\\,255\\,255\\,0\\.2\\)_70\\%\\)\\]{background-image:radial-gradient(circle,#fff0 60%,#fff3 70%)!important}stagewise-companion-anchor .from-blue-600{--tw-gradient-from:var(--color-blue-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-indigo-700{--tw-gradient-from:var(--color-indigo-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-orange-600{--tw-gradient-from:var(--color-orange-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .from-sky-700{--tw-gradient-from:var(--color-sky-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .via-blue-500{--tw-gradient-via:var(--color-blue-500)!important;--tw-gradient-via-stops:var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-via)var(--tw-gradient-via-position),var(--tw-gradient-to)var(--tw-gradient-to-position)!important;--tw-gradient-stops:var(--tw-gradient-via-stops)!important}stagewise-companion-anchor .to-fuchsia-700{--tw-gradient-to:var(--color-fuchsia-700)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-red-600{--tw-gradient-to:var(--color-red-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-sky-600{--tw-gradient-to:var(--color-sky-600)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .to-teal-500{--tw-gradient-to:var(--color-teal-500)!important;--tw-gradient-stops:var(--tw-gradient-via-stops,var(--tw-gradient-position),var(--tw-gradient-from)var(--tw-gradient-from-position),var(--tw-gradient-to)var(--tw-gradient-to-position))!important}stagewise-companion-anchor .fill-current{fill:currentColor!important}stagewise-companion-anchor .fill-white{fill:var(--color-white)!important}stagewise-companion-anchor .fill-zinc-500\\/50{fill:#71717b80!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .fill-zinc-500\\/50{fill:color-mix(in oklab,var(--color-zinc-500)50%,transparent)!important}}stagewise-companion-anchor .fill-zinc-950{fill:var(--color-zinc-950)!important}stagewise-companion-anchor .stroke-black\\/30{stroke:#0000004d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .stroke-black\\/30{stroke:color-mix(in oklab,var(--color-black)30%,transparent)!important}}stagewise-companion-anchor .stroke-none{stroke:none!important}stagewise-companion-anchor .stroke-white{stroke:var(--color-white)!important}stagewise-companion-anchor .stroke-zinc-950{stroke:var(--color-zinc-950)!important}stagewise-companion-anchor .stroke-1{stroke-width:1px!important}stagewise-companion-anchor .p-0\\.5{padding:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .p-1{padding:calc(var(--spacing)*1)!important}stagewise-companion-anchor .p-2{padding:calc(var(--spacing)*2)!important}stagewise-companion-anchor .p-3{padding:calc(var(--spacing)*3)!important}stagewise-companion-anchor .p-4{padding:calc(var(--spacing)*4)!important}stagewise-companion-anchor .px-0\\.5{padding-inline:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .px-1{padding-inline:calc(var(--spacing)*1)!important}stagewise-companion-anchor .px-2{padding-inline:calc(var(--spacing)*2)!important}stagewise-companion-anchor .px-3{padding-inline:calc(var(--spacing)*3)!important}stagewise-companion-anchor .px-4{padding-inline:calc(var(--spacing)*4)!important}stagewise-companion-anchor .py-0{padding-block:calc(var(--spacing)*0)!important}stagewise-companion-anchor .py-0\\.5{padding-block:calc(var(--spacing)*.5)!important}stagewise-companion-anchor .py-2{padding-block:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pt-2{padding-top:calc(var(--spacing)*2)!important}stagewise-companion-anchor .pt-4{padding-top:calc(var(--spacing)*4)!important}stagewise-companion-anchor .pr-6{padding-right:calc(var(--spacing)*6)!important}stagewise-companion-anchor .pb-4{padding-bottom:calc(var(--spacing)*4)!important}stagewise-companion-anchor .pl-2{padding-left:calc(var(--spacing)*2)!important}stagewise-companion-anchor .text-base{font-size:var(--text-base)!important;line-height:var(--tw-leading,var(--text-base--line-height))!important}stagewise-companion-anchor .text-lg{font-size:var(--text-lg)!important;line-height:var(--tw-leading,var(--text-lg--line-height))!important}stagewise-companion-anchor .text-sm{font-size:var(--text-sm)!important;line-height:var(--tw-leading,var(--text-sm--line-height))!important}stagewise-companion-anchor .text-xs{font-size:var(--text-xs)!important;line-height:var(--tw-leading,var(--text-xs--line-height))!important}stagewise-companion-anchor .text-\\[0\\.5em\\]{font-size:.5em!important}stagewise-companion-anchor .font-bold{--tw-font-weight:var(--font-weight-bold)!important;font-weight:var(--font-weight-bold)!important}stagewise-companion-anchor .font-medium{--tw-font-weight:var(--font-weight-medium)!important;font-weight:var(--font-weight-medium)!important}stagewise-companion-anchor .font-normal{--tw-font-weight:var(--font-weight-normal)!important;font-weight:var(--font-weight-normal)!important}stagewise-companion-anchor .font-semibold{--tw-font-weight:var(--font-weight-semibold)!important;font-weight:var(--font-weight-semibold)!important}stagewise-companion-anchor .text-amber-800{color:var(--color-amber-800)!important}stagewise-companion-anchor .text-blue-500{color:var(--color-blue-500)!important}stagewise-companion-anchor .text-blue-600{color:var(--color-blue-600)!important}stagewise-companion-anchor .text-blue-700{color:var(--color-blue-700)!important}stagewise-companion-anchor .text-blue-800{color:var(--color-blue-800)!important}stagewise-companion-anchor .text-foreground{color:var(--color-foreground)!important}stagewise-companion-anchor .text-indigo-700{color:var(--color-indigo-700)!important}stagewise-companion-anchor .text-orange-600{color:var(--color-orange-600)!important}stagewise-companion-anchor .text-orange-700{color:var(--color-orange-700)!important}stagewise-companion-anchor .text-orange-800{color:var(--color-orange-800)!important}stagewise-companion-anchor .text-red-600{color:var(--color-red-600)!important}stagewise-companion-anchor .text-red-700{color:var(--color-red-700)!important}stagewise-companion-anchor .text-transparent{color:#0000!important}stagewise-companion-anchor .text-violet-700{color:var(--color-violet-700)!important}stagewise-companion-anchor .text-white{color:var(--color-white)!important}stagewise-companion-anchor .text-zinc-500{color:var(--color-zinc-500)!important}stagewise-companion-anchor .text-zinc-600{color:var(--color-zinc-600)!important}stagewise-companion-anchor .text-zinc-700{color:var(--color-zinc-700)!important}stagewise-companion-anchor .text-zinc-950{color:var(--color-zinc-950)!important}stagewise-companion-anchor .opacity-0{opacity:0!important}stagewise-companion-anchor .opacity-20{opacity:.2!important}stagewise-companion-anchor .opacity-30{opacity:.3!important}stagewise-companion-anchor .opacity-80{opacity:.8!important}stagewise-companion-anchor .opacity-100{opacity:1!important}stagewise-companion-anchor .shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .ring{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(1px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:#00000080!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .shadow-black\\/50{--tw-shadow-color:color-mix(in oklab,color-mix(in oklab,var(--color-black)50%,transparent)var(--tw-shadow-alpha),transparent)!important}}stagewise-companion-anchor .ring-transparent{--tw-ring-color:transparent!important}stagewise-companion-anchor .ring-zinc-950\\/20{--tw-ring-color:#09090b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .ring-zinc-950\\/20{--tw-ring-color:color-mix(in oklab,var(--color-zinc-950)20%,transparent)!important}}stagewise-companion-anchor .outline{outline-style:var(--tw-outline-style)!important;outline-width:1px!important}stagewise-companion-anchor .blur{--tw-blur:blur(8px)!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .blur-md{--tw-blur:blur(var(--blur-md))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .blur-none{--tw-blur: !important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-md{--tw-drop-shadow-size:drop-shadow(0 3px 3px var(--tw-drop-shadow-color,#0000001f))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-md))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xl{--tw-drop-shadow-size:drop-shadow(0 9px 7px var(--tw-drop-shadow-color,#0000001a))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xl))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-xs{--tw-drop-shadow-size:drop-shadow(0 1px 1px var(--tw-drop-shadow-color,#0000000d))!important;--tw-drop-shadow:drop-shadow(var(--drop-shadow-xs))!important;filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:#000!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .drop-shadow-black{--tw-drop-shadow-color:color-mix(in oklab,var(--color-black)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:oklch(25.7% .09 281.288)!important;--tw-drop-shadow:var(--tw-drop-shadow-size)!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .drop-shadow-indigo-950{--tw-drop-shadow-color:color-mix(in oklab,var(--color-indigo-950)var(--tw-drop-shadow-alpha),transparent)!important}}stagewise-companion-anchor .filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}stagewise-companion-anchor .backdrop-blur{--tw-backdrop-blur:blur(8px)!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-blur-md{--tw-backdrop-blur:blur(var(--blur-md))!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .backdrop-saturate-150{--tw-backdrop-saturate:saturate(150%)!important;-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important;backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)!important}stagewise-companion-anchor .transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-all{transition-property:all!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to!important;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function))!important;transition-duration:var(--tw-duration,var(--default-transition-duration))!important}stagewise-companion-anchor .duration-0{--tw-duration:0s!important;transition-duration:0s!important}stagewise-companion-anchor .duration-100{--tw-duration:.1s!important;transition-duration:.1s!important}stagewise-companion-anchor .duration-150{--tw-duration:.15s!important;transition-duration:.15s!important}stagewise-companion-anchor .duration-300{--tw-duration:.3s!important;transition-duration:.3s!important}stagewise-companion-anchor .duration-500{--tw-duration:.5s!important;transition-duration:.5s!important}stagewise-companion-anchor .ease-out{--tw-ease:var(--ease-out)!important;transition-timing-function:var(--ease-out)!important}stagewise-companion-anchor .outline-none{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .select-none{-webkit-user-select:none!important;-moz-user-select:none!important;user-select:none!important}stagewise-companion-anchor :is(.\\*\\:size-full>*){width:100%!important;height:100%!important}stagewise-companion-anchor .placeholder\\:text-zinc-400::-moz-placeholder{color:var(--color-zinc-400)!important}stagewise-companion-anchor .placeholder\\:text-zinc-400::placeholder{color:var(--color-zinc-400)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:#09090b80!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:#09090b80!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/50::placeholder{color:color-mix(in oklab,var(--color-zinc-950)50%,transparent)!important}}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:#09090bb3!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:#09090bb3!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::-moz-placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}stagewise-companion-anchor .placeholder\\:text-zinc-950\\/70::placeholder{color:color-mix(in oklab,var(--color-zinc-950)70%,transparent)!important}}@media (hover:hover){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:#e40014cc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:border-red-600\\/80:hover{border-color:color-mix(in oklab,var(--color-red-600)80%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-blue-200\\/80:hover{background-color:#bedbffcc!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-blue-200\\/80:hover{background-color:color-mix(in oklab,var(--color-blue-200)80%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-orange-200:hover{background-color:var(--color-orange-200)!important}stagewise-companion-anchor .hover\\:bg-orange-700:hover{background-color:var(--color-orange-700)!important}stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:#e4001433!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-red-600\\/20:hover{background-color:color-mix(in oklab,var(--color-red-600)20%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-500\\/20:hover{background-color:#71717b33!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-500\\/20:hover{background-color:color-mix(in oklab,var(--color-zinc-500)20%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:#09090b0d!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/5:hover{background-color:color-mix(in oklab,var(--color-zinc-950)5%,transparent)!important}}stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:#09090b1a!important}@supports (color:color-mix(in lab,red,red)){stagewise-companion-anchor .hover\\:bg-zinc-950\\/10:hover{background-color:color-mix(in oklab,var(--color-zinc-950)10%,transparent)!important}}stagewise-companion-anchor .hover\\:text-orange-800:hover{color:var(--color-orange-800)!important}stagewise-companion-anchor .hover\\:text-white:hover{color:var(--color-white)!important}stagewise-companion-anchor .hover\\:underline:hover{text-decoration-line:underline!important}stagewise-companion-anchor .hover\\:opacity-100:hover{opacity:1!important}stagewise-companion-anchor .hover\\:shadow-lg:hover{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a)!important;box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)!important}}stagewise-companion-anchor .focus\\:border-blue-500:focus{border-color:var(--color-blue-500)!important}stagewise-companion-anchor .focus\\:border-zinc-500:focus{border-color:var(--color-zinc-500)!important}stagewise-companion-anchor .focus\\:text-zinc-900:focus{color:var(--color-zinc-900)!important}stagewise-companion-anchor .focus\\:outline-none:focus{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .disabled\\:opacity-50:disabled{opacity:.5!important}stagewise-companion-anchor .data-focus\\:outline-none[data-focus]{--tw-outline-style:none!important;outline-style:none!important}stagewise-companion-anchor .animate-shake{animation:.5s ease-in-out 2 shake}}@keyframes shake{0%,to{transform:translate(0)}10%,30%,50%,70%,90%{transform:translate(-2px)}20%,40%,60%,80%{transform:translate(2px)}}@keyframes gradient-animation{0%{background-position:0%}50%{background-position:100%}to{background-position:0%}}stagewise-companion-anchor stagewise-companion-anchor{all:initial;interpolate-size:allow-keywords;transform:translate(0);color:var(--color-zinc-950)!important;letter-spacing:normal!important;text-rendering:auto!important;font-family:Inter,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important;font-weight:400!important;line-height:normal!important}@supports (font-variation-settings:normal){stagewise-companion-anchor stagewise-companion-anchor{font-optical-sizing:auto!important;font-family:InterVariable,Noto Color Emoji,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,SF Compact,SF Pro,Helvetica Neue,sans-serif!important}}stagewise-companion-anchor #headlessui-portal-root{z-index:50!important;width:100vw!important;height:100vh!important;position:fixed!important}stagewise-companion-anchor #headlessui-portal-root>*{pointer-events:auto!important}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@keyframes spin{to{transform:rotate(360deg)}}@keyframes pulse{50%{opacity:.5}}';function T0(t){const e={},r=["id","class","name","type","href","src","alt","for","placeholder"],n=[];for(let i=0;i<t.attributes.length;i++){const o=t.attributes[i];o.name.startsWith("data-")?n.push({name:o.name,value:o.value}):(r.includes(o.name.toLowerCase())||o.name.toLowerCase()!=="style")&&(e[o.name]=o.value)}return n.forEach(i=>{e[i.name]=i.value}),e}function M0(t,e){var r;let n=`<element index="${e+1}">
`;n+=`  <tag>${t.tagName.toLowerCase()}</tag>
`;const i=t.id;i&&(n+=`  <id>${i}</id>
`);const o=Array.from(t.classList).join(", ");o&&(n+=`  <classes>${o}</classes>
`);const a=T0(t);if(Object.keys(a).length>0){n+=`  <attributes>
`;for(const[l,c]of Object.entries(a))(l.toLowerCase()!=="class"||!o)&&(n+=`    <${l}>${c}</${l}>
`);n+=`  </attributes>
`}const s=(r=t.innerText)==null?void 0:r.trim();if(s&&(n+=`  <text>${s.length>100?`${s.substring(0,100)}...`:s}</text>
`),n+=`  <structural_context>
`,t.parentElement){const l=t.parentElement;n+=`    <parent>
`,n+=`      <tag>${l.tagName.toLowerCase()}</tag>
`,l.id&&(n+=`      <id>${l.id}</id>
`);const c=Array.from(l.classList).join(", ");c&&(n+=`      <classes>${c}</classes>
`),n+=`    </parent>
`}else n+=`    <parent>No parent element found (likely root or disconnected)</parent>
`;n+=`  </structural_context>
`;try{const l=window.getComputedStyle(t),c={color:l.color,backgroundColor:l.backgroundColor,fontSize:l.fontSize,fontWeight:l.fontWeight,display:l.display};n+=`  <styles>
`;for(const[u,d]of Object.entries(c))n+=`    <${u}>${d}</${u}>
`;n+=`  </styles>
`}catch{n+=`  <styles>Could not retrieve computed styles</styles>
`}return n+=`</element>
`,n}function A0(t,e,r,n){const i=n.map(a=>`
      <plugin_contexts>
<${a.pluginName}>
${a.contextSnippets.map(s=>`    <${s.promptContextName}>${s.content}</${s.promptContextName}>`).join(`
`)}
</${a.pluginName}>
</plugin_contexts>
`.trim()).join(`
`);if(!t||t.length===0)return`
    <request>
      <user_goal>${e}</user_goal>
      <url>${r}</url>
  <context>No specific element was selected on the page. Please analyze the page code in general or ask for clarification.</context>
  ${i}
</request>`.trim();let o="";return t.forEach((a,s)=>{o+=M0(a,s)}),`
<request>
  <user_goal>${e}</user_goal>
  <url>${r}</url>
  <selected_elements>
    ${o.trim()}
  </selected_elements>
  ${i}
</request>`.trim()}const Wd=rt(null),Vd="stgws:companion";function R0(){try{const t=sessionStorage.getItem(Vd);return t?JSON.parse(t):{}}catch(t){return console.error("Failed to load state from storage:",t),{}}}function I0(t){try{sessionStorage.setItem(Vd,JSON.stringify(t))}catch(e){console.error("Failed to save state to storage:",e)}}function $0({children:t}){const[e,r]=me(()=>{const p=R0();return{appBlockRequestList:[],appUnblockRequestList:[],lastBlockRequestNumber:0,lastUnblockRequestNumber:0,isMainAppBlocked:!1,toolbarBoxRef:Vo(),minimized:p.minimized??!1,requestMainAppBlock:()=>0,requestMainAppUnblock:()=>0,discardMainAppBlock:()=>{},discardMainAppUnblock:()=>{},setToolbarBoxRef:()=>{},unsetToolbarBoxRef:()=>{},minimize:()=>{},expand:()=>{}}});xe(()=>{I0({minimized:e.minimized})},[e.minimized]);const n=V(()=>{let p=0;return r(h=>(p=h.lastBlockRequestNumber+1,{...h,appBlockRequestList:[...h.appBlockRequestList,p],lastBlockRequestNumber:p,isMainAppBlocked:h.appUnblockRequestList.length===0})),p},[]),i=V(()=>{let p=0;return r(h=>(p=h.lastUnblockRequestNumber+1,{...h,appUnblockRequestList:[...h.appUnblockRequestList,p],lastUnblockRequestNumber:p,isMainAppBlocked:!1})),p},[]),o=V(p=>{r(h=>{const b=h.appBlockRequestList.filter(f=>f!==p);return{...h,appBlockRequestList:b,isMainAppBlocked:b.length>0&&h.appUnblockRequestList.length===0}})},[]),a=V(p=>{r(h=>{const b=h.appUnblockRequestList.filter(f=>f!==p);return{...h,appUnblockRequestList:b,isMainAppBlocked:h.appBlockRequestList.length>0&&b.length===0}})},[]),s=V(p=>{r(h=>({...h,toolbarBoxRef:p}))},[]),l=V(()=>{r(p=>({...p,toolbarBoxRef:Vo()}))},[]),c=V(()=>{r(p=>({...p,minimized:!0}))},[]),u=V(()=>{r(p=>({...p,minimized:!1}))},[]),d={requestMainAppBlock:n,requestMainAppUnblock:i,discardMainAppBlock:o,discardMainAppUnblock:a,isMainAppBlocked:e.isMainAppBlocked,toolbarBoxRef:e.toolbarBoxRef,setToolbarBoxRef:s,unsetToolbarBoxRef:l,minimized:e.minimized,minimize:c,expand:u};return m(Wd.Provider,{value:d,children:t})}function $a(){const t=Le(Wd);if(!t)throw new Error("useAppState must be used within an AppStateProvider");return t}const qd=rt({chats:[],currentChatId:null,createChat:()=>"",deleteChat:()=>{},setCurrentChat:()=>{},setChatInput:()=>{},addChatDomContext:()=>{},removeChatDomContext:()=>{},addMessage:()=>{},chatAreaState:"hidden",setChatAreaState:()=>{},isPromptCreationActive:!1,startPromptCreation:()=>{},stopPromptCreation:()=>{},promptState:"idle",resetPromptState:()=>{}}),z0=({children:t})=>{const[e,r]=me([{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]),[n,i]=me("new_chat"),[o,a]=me("hidden"),[s,l]=me(!1),[c,u]=me("idle"),d=V(()=>{u("idle")},[]),{minimized:p}=$a(),{selectedSession:h,setShouldPromptWindowSelection:b,windows:f}=Bt();xe(()=>{p&&(l(!1),a("hidden"))},[p]);const{bridge:w}=Nd(),y=V(()=>{const N=go(),T={id:N,title:null,messages:[],inputValue:"",domContextElements:[]};return r(pe=>[...pe,T]),i(N),N},[]),v=V(N=>{r(T=>{const pe=T.filter(Z=>Z.id!==N);return pe.length===0?[{id:"new_chat",messages:[],title:"New chat",inputValue:"",domContextElements:[]}]:pe}),n===N&&r(T=>(i(T[0].id),T))},[n]),_=V(N=>{i(N)},[]),S=V((N,T)=>{r(pe=>pe.map(Z=>Z.id===N?{...Z,inputValue:T}:Z))},[]),{plugins:E}=Vi(),I=V(()=>{l(!0),o==="hidden"&&a("compact"),E.forEach(N=>{var T;(T=N.onPromptingStart)==null||T.call(N)})},[o]),H=V(()=>{l(!1),u("idle"),r(N=>N.map(T=>T.id===n?{...T,domContextElements:[]}:T)),o==="compact"&&a("hidden"),E.forEach(N=>{var T;(T=N.onPromptingAbort)==null||T.call(N)})},[n,o]),R=V(N=>{a(N),N==="hidden"&&H()},[a,H]),k=V((N,T)=>{const pe=E.filter(Z=>Z.onContextElementSelect);r(Z=>Z.map(ge=>ge.id===N?{...ge,domContextElements:[...ge.domContextElements,{element:T,pluginContext:pe.map(B=>{var G;return{pluginName:B.pluginName,context:(G=B.onContextElementSelect)==null?void 0:G.call(B,T)}})}]}:ge))},[E]),O=V((N,T)=>{r(pe=>pe.map(Z=>Z.id===N?{...Z,domContextElements:Z.domContextElements.filter(ge=>ge.element!==T)}:Z))},[]),$=V(async(N,T,pe=!1)=>{if(!T.trim()||c==="loading")return;const Z=e.find(U=>U.id===N);u("loading");const ge=[],B=E.map(async U=>{var L;const te={id:go(),text:T,contextElements:(Z==null?void 0:Z.domContextElements.map(he=>he.element))||[],sentByPlugin:pe},se=await((L=U.onPromptSend)==null?void 0:L.call(U,te));if(!se||!se.contextSnippets||se.contextSnippets.length===0)return null;const Pe=se.contextSnippets.map(async he=>{const ke=typeof he.content=="string"?he.content:await he.content();return{promptContextName:he.promptContextName,content:ke}}),X=await Promise.all(Pe);return X.length>0?{pluginName:U.pluginName,contextSnippets:X}:null});(await Promise.all(B)).forEach(U=>{U&&ge.push(U)});const G=A0(Z==null?void 0:Z.domContextElements.map(U=>U.element),T,window.location.href,ge),fe={id:go(),content:T.trim(),sender:"user",type:"regular",timestamp:new Date};async function le(){if(w)try{const U=await w.call.triggerAgentPrompt({prompt:G,sessionId:h==null?void 0:h.sessionId},{onUpdate:L=>{}});U.result.success?(setTimeout(()=>{u("success")},1e3),r(L=>L.map(te=>te.id===N?{...te,inputValue:""}:te))):(U.result.errorCode&&U.result.errorCode==="session_mismatch"&&b(!0),u("error"),setTimeout(()=>{u("idle"),l(!1),r(L=>L.map(te=>te.id===N?{...te,inputValue:""}:te))},300))}catch{u("error"),setTimeout(()=>{u("idle"),l(!1),r(U=>U.map(L=>L.id===N?{...L,inputValue:""}:L))},300)}else b(!0),u("error"),setTimeout(()=>{u("idle"),l(!1),r(U=>U.map(L=>L.id===N?{...L,inputValue:""}:L))},300)}le(),o==="hidden"&&a("compact"),r(U=>U.map(L=>L.id===N?{...L,messages:[...L.messages,fe],inputValue:T.trim(),domContextElements:[]}:L))},[o,w,e,l,a,h,c,u,E]),j={chats:e,currentChatId:n,createChat:y,deleteChat:v,setCurrentChat:_,setChatInput:S,addMessage:$,chatAreaState:o,setChatAreaState:R,isPromptCreationActive:s,startPromptCreation:I,stopPromptCreation:H,addChatDomContext:k,removeChatDomContext:O,promptState:c,resetPromptState:d};return m(qd.Provider,{value:j,children:t})};function An(){const t=Le(qd);if(!t)throw new Error("useChatState must be used within a ChatStateProvider");return t}function j0({children:t,config:e}){return m(Wv,{config:e,children:m(Hv,{children:m(Bv,{children:m(qv,{children:m(z0,{children:t})})})})})}function Ei(t,e,r,n=window){xe(()=>{if(!(typeof window>"u")&&n)return n.addEventListener(t,e,r),()=>n.removeEventListener(t,e,r)},[t,e,n,r])}function L0(){const{startPromptCreation:t,stopPromptCreation:e,isPromptCreationActive:r}=An(),n=Ne(()=>({[Ci.CTRL_ALT_C]:()=>r?!1:(t(),!0),[Ci.ESC]:()=>r?(e(),!0):!1}),[t,e,r]),i=V(o=>{for(const[a,s]of Object.entries(na))if(s.isEventMatching(o)){n[a]()&&(o.preventDefault(),o.stopPropagation());break}},[n]);return Ei("keydown",i,{capture:!0}),null}const Ud=typeof document<"u"?Aa.useLayoutEffect:()=>{};function D0(t){const e=ce(null);return Ud(()=>{e.current=t},[t]),V((...r)=>{const n=e.current;return n==null?void 0:n(...r)},[])}const Wt=t=>{var e;return(e=t==null?void 0:t.ownerDocument)!==null&&e!==void 0?e:document},Xt=t=>t&&"window"in t&&t.window===t?t:Wt(t).defaultView||window;function Gd(t,e){return e&&t?t.contains(e):!1}const ia=(t=document)=>t.activeElement;function Zd(t){return t.target}function F0(t){var e;return typeof window>"u"||window.navigator==null?!1:((e=window.navigator.userAgentData)===null||e===void 0?void 0:e.brands.some(r=>t.test(r.brand)))||t.test(window.navigator.userAgent)}function H0(t){var e;return typeof window<"u"&&window.navigator!=null?t.test(((e=window.navigator.userAgentData)===null||e===void 0?void 0:e.platform)||window.navigator.platform):!1}function Yd(t){let e=null;return()=>(e==null&&(e=t()),e)}const B0=Yd(function(){return H0(/^Mac/i)}),W0=Yd(function(){return F0(/Android/i)});function Kd(){let t=ce(new Map),e=V((i,o,a,s)=>{let l=s!=null&&s.once?(...c)=>{t.current.delete(a),a(...c)}:a;t.current.set(a,{type:o,eventTarget:i,fn:l,options:s}),i.addEventListener(o,l,s)},[]),r=V((i,o,a,s)=>{var l;let c=((l=t.current.get(a))===null||l===void 0?void 0:l.fn)||a;i.removeEventListener(o,c,s),t.current.delete(a)},[]),n=V(()=>{t.current.forEach((i,o)=>{r(i.eventTarget,i.type,o,i.options)})},[r]);return xe(()=>n,[n]),{addGlobalListener:e,removeGlobalListener:r,removeAllGlobalListeners:n}}function V0(t){return t.mozInputSource===0&&t.isTrusted?!0:W0()&&t.pointerType?t.type==="click"&&t.buttons===1:t.detail===0&&!t.pointerType}function Qd(t){let e=t;return e.nativeEvent=t,e.isDefaultPrevented=()=>e.defaultPrevented,e.isPropagationStopped=()=>e.cancelBubble,e.persist=()=>{},e}function q0(t,e){Object.defineProperty(t,"target",{value:e}),Object.defineProperty(t,"currentTarget",{value:e})}function Xd(t){let e=ce({isFocused:!1,observer:null});Ud(()=>{const n=e.current;return()=>{n.observer&&(n.observer.disconnect(),n.observer=null)}},[]);let r=D0(n=>{t==null||t(n)});return V(n=>{if(n.target instanceof HTMLButtonElement||n.target instanceof HTMLInputElement||n.target instanceof HTMLTextAreaElement||n.target instanceof HTMLSelectElement){e.current.isFocused=!0;let i=n.target,o=a=>{if(e.current.isFocused=!1,i.disabled){let s=Qd(a);r(s)}e.current.observer&&(e.current.observer.disconnect(),e.current.observer=null)};i.addEventListener("focusout",o,{once:!0}),e.current.observer=new MutationObserver(()=>{if(e.current.isFocused&&i.disabled){var a;(a=e.current.observer)===null||a===void 0||a.disconnect();let s=i===document.activeElement?null:document.activeElement;i.dispatchEvent(new FocusEvent("blur",{relatedTarget:s})),i.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:s}))}}),e.current.observer.observe(i,{attributes:!0,attributeFilter:["disabled"]})}},[r])}let U0=!1,Rn=null,oa=new Set,Kr=new Map,or=!1,aa=!1;const G0={Tab:!0,Escape:!0};function za(t,e){for(let r of oa)r(t,e)}function Z0(t){return!(t.metaKey||!B0()&&t.altKey||t.ctrlKey||t.key==="Control"||t.key==="Shift"||t.key==="Meta")}function Oi(t){or=!0,Z0(t)&&(Rn="keyboard",za("keyboard",t))}function gr(t){Rn="pointer",(t.type==="mousedown"||t.type==="pointerdown")&&(or=!0,za("pointer",t))}function Jd(t){V0(t)&&(or=!0,Rn="virtual")}function eu(t){t.target===window||t.target===document||U0||!t.isTrusted||(!or&&!aa&&(Rn="virtual",za("virtual",t)),or=!1,aa=!1)}function tu(){or=!1,aa=!0}function sa(t){if(typeof window>"u"||Kr.get(Xt(t)))return;const e=Xt(t),r=Wt(t);let n=e.HTMLElement.prototype.focus;e.HTMLElement.prototype.focus=function(){or=!0,n.apply(this,arguments)},r.addEventListener("keydown",Oi,!0),r.addEventListener("keyup",Oi,!0),r.addEventListener("click",Jd,!0),e.addEventListener("focus",eu,!0),e.addEventListener("blur",tu,!1),typeof PointerEvent<"u"&&(r.addEventListener("pointerdown",gr,!0),r.addEventListener("pointermove",gr,!0),r.addEventListener("pointerup",gr,!0)),e.addEventListener("beforeunload",()=>{ru(t)},{once:!0}),Kr.set(e,{focus:n})}const ru=(t,e)=>{const r=Xt(t),n=Wt(t);e&&n.removeEventListener("DOMContentLoaded",e),Kr.has(r)&&(r.HTMLElement.prototype.focus=Kr.get(r).focus,n.removeEventListener("keydown",Oi,!0),n.removeEventListener("keyup",Oi,!0),n.removeEventListener("click",Jd,!0),r.removeEventListener("focus",eu,!0),r.removeEventListener("blur",tu,!1),typeof PointerEvent<"u"&&(n.removeEventListener("pointerdown",gr,!0),n.removeEventListener("pointermove",gr,!0),n.removeEventListener("pointerup",gr,!0)),Kr.delete(r))};function Y0(t){const e=Wt(t);let r;return e.readyState!=="loading"?sa(t):(r=()=>{sa(t)},e.addEventListener("DOMContentLoaded",r)),()=>ru(t,r)}typeof document<"u"&&Y0();function nu(){return Rn!=="pointer"}const K0=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function Q0(t,e,r){let n=Wt(r==null?void 0:r.target);const i=typeof window<"u"?Xt(r==null?void 0:r.target).HTMLInputElement:HTMLInputElement,o=typeof window<"u"?Xt(r==null?void 0:r.target).HTMLTextAreaElement:HTMLTextAreaElement,a=typeof window<"u"?Xt(r==null?void 0:r.target).HTMLElement:HTMLElement,s=typeof window<"u"?Xt(r==null?void 0:r.target).KeyboardEvent:KeyboardEvent;return t=t||n.activeElement instanceof i&&!K0.has(n.activeElement.type)||n.activeElement instanceof o||n.activeElement instanceof a&&n.activeElement.isContentEditable,!(t&&e==="keyboard"&&r instanceof s&&!G0[r.key])}function X0(t,e,r){sa(),xe(()=>{let n=(i,o)=>{Q0(!!(r!=null&&r.isTextInput),i,o)&&t(nu())};return oa.add(n),()=>{oa.delete(n)}},e)}function J0(t){let{isDisabled:e,onFocus:r,onBlur:n,onFocusChange:i}=t;const o=V(l=>{if(l.target===l.currentTarget)return n&&n(l),i&&i(!1),!0},[n,i]),a=Xd(o),s=V(l=>{const c=Wt(l.target),u=c?ia(c):ia();l.target===l.currentTarget&&u===Zd(l.nativeEvent)&&(r&&r(l),i&&i(!0),a(l))},[i,r,a]);return{focusProps:{onFocus:!e&&(r||i||n)?s:void 0,onBlur:!e&&(n||i)?o:void 0}}}function eb(t){let{isDisabled:e,onBlurWithin:r,onFocusWithin:n,onFocusWithinChange:i}=t,o=ce({isFocusWithin:!1}),{addGlobalListener:a,removeAllGlobalListeners:s}=Kd(),l=V(d=>{d.currentTarget.contains(d.target)&&o.current.isFocusWithin&&!d.currentTarget.contains(d.relatedTarget)&&(o.current.isFocusWithin=!1,s(),r&&r(d),i&&i(!1))},[r,i,o,s]),c=Xd(l),u=V(d=>{if(!d.currentTarget.contains(d.target))return;const p=Wt(d.target),h=ia(p);if(!o.current.isFocusWithin&&h===Zd(d.nativeEvent)){n&&n(d),i&&i(!0),o.current.isFocusWithin=!0,c(d);let b=d.currentTarget;a(p,"focus",f=>{if(o.current.isFocusWithin&&!Gd(b,f.target)){let w=new p.defaultView.FocusEvent("blur",{relatedTarget:f.target});q0(w,b);let y=Qd(w);l(y)}},{capture:!0})}},[n,i,c,a,l]);return e?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:u,onBlur:l}}}let la=!1,vo=0;function tb(){la=!0,setTimeout(()=>{la=!1},50)}function ml(t){t.pointerType==="touch"&&tb()}function rb(){if(!(typeof document>"u"))return typeof PointerEvent<"u"&&document.addEventListener("pointerup",ml),vo++,()=>{vo--,!(vo>0)&&(typeof PointerEvent<"u"&&document.removeEventListener("pointerup",ml))}}function iu(t){let{onHoverStart:e,onHoverChange:r,onHoverEnd:n,isDisabled:i}=t,[o,a]=me(!1),s=ce({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;xe(rb,[]);let{addGlobalListener:l,removeAllGlobalListeners:c}=Kd(),{hoverProps:u,triggerHoverEnd:d}=Ne(()=>{let p=(f,w)=>{if(s.pointerType=w,i||w==="touch"||s.isHovered||!f.currentTarget.contains(f.target))return;s.isHovered=!0;let y=f.currentTarget;s.target=y,l(Wt(f.target),"pointerover",v=>{s.isHovered&&s.target&&!Gd(s.target,v.target)&&h(v,v.pointerType)},{capture:!0}),e&&e({type:"hoverstart",target:y,pointerType:w}),r&&r(!0),a(!0)},h=(f,w)=>{let y=s.target;s.pointerType="",s.target=null,!(w==="touch"||!s.isHovered||!y)&&(s.isHovered=!1,c(),n&&n({type:"hoverend",target:y,pointerType:w}),r&&r(!1),a(!1))},b={};return typeof PointerEvent<"u"&&(b.onPointerEnter=f=>{la&&f.pointerType==="mouse"||p(f,f.pointerType)},b.onPointerLeave=f=>{!i&&f.currentTarget.contains(f.target)&&h(f,f.pointerType)}),{hoverProps:b,triggerHoverEnd:h}},[e,r,n,i,s,l,c]);return xe(()=>{i&&d({currentTarget:s.target},s.pointerType)},[i]),{hoverProps:u,isHovered:o}}function ou(t={}){let{autoFocus:e=!1,isTextInput:r,within:n}=t,i=ce({isFocused:!1,isFocusVisible:e||nu()}),[o,a]=me(!1),[s,l]=me(()=>i.current.isFocused&&i.current.isFocusVisible),c=V(()=>l(i.current.isFocused&&i.current.isFocusVisible),[]),u=V(h=>{i.current.isFocused=h,a(h),c()},[c]);X0(h=>{i.current.isFocusVisible=h,c()},[],{isTextInput:r});let{focusProps:d}=J0({isDisabled:n,onFocusChange:u}),{focusWithinProps:p}=eb({isDisabled:!n,onFocusWithinChange:u});return{isFocused:o,isFocusVisible:s,focusProps:n?p:d}}var nb=Object.defineProperty,ib=(t,e,r)=>e in t?nb(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,bo=(t,e,r)=>(ib(t,typeof e!="symbol"?e+"":e,r),r);let ob=class{constructor(){bo(this,"current",this.detect()),bo(this,"handoffState","pending"),bo(this,"currentId",0)}set(t){this.current!==t&&(this.handoffState="pending",this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window>"u"||typeof document>"u"?"server":"client"}handoff(){this.handoffState==="pending"&&(this.handoffState="complete")}get isHandoffComplete(){return this.handoffState==="complete"}},au=new ob;function ab(t){var e,r;return au.isServer?null:t?"ownerDocument"in t?t.ownerDocument:"current"in t?(r=(e=t.current)==null?void 0:e.ownerDocument)!=null?r:document:null:document}function sb(t){typeof queueMicrotask=="function"?queueMicrotask(t):Promise.resolve().then(t).catch(e=>setTimeout(()=>{throw e}))}function su(){let t=[],e={addEventListener(r,n,i,o){return r.addEventListener(n,i,o),e.add(()=>r.removeEventListener(n,i,o))},requestAnimationFrame(...r){let n=requestAnimationFrame(...r);return e.add(()=>cancelAnimationFrame(n))},nextFrame(...r){return e.requestAnimationFrame(()=>e.requestAnimationFrame(...r))},setTimeout(...r){let n=setTimeout(...r);return e.add(()=>clearTimeout(n))},microTask(...r){let n={current:!0};return sb(()=>{n.current&&r[0]()}),e.add(()=>{n.current=!1})},style(r,n,i){let o=r.style.getPropertyValue(n);return Object.assign(r.style,{[n]:i}),this.add(()=>{Object.assign(r.style,{[n]:o})})},group(r){let n=su();return r(n),this.add(()=>n.dispose())},add(r){return t.includes(r)||t.push(r),()=>{let n=t.indexOf(r);if(n>=0)for(let i of t.splice(n,1))i()}},dispose(){for(let r of t.splice(0))r()}};return e}function lb(){let[t]=me(su);return xe(()=>()=>t.dispose(),[t]),t}let ja=(t,e)=>{au.isServer?xe(t,e):Ar(t,e)};function cb(t){let e=ce(t);return ja(()=>{e.current=t},[t]),e}let Ni=function(t){let e=cb(t);return Aa.useCallback((...r)=>e.current(...r),[e])};function db(t){let e=t.width/2,r=t.height/2;return{top:t.clientY-r,right:t.clientX+e,bottom:t.clientY+r,left:t.clientX-e}}function ub(t,e){return!(!t||!e||t.right<e.left||t.left>e.right||t.bottom<e.top||t.top>e.bottom)}function pb({disabled:t=!1}={}){let e=ce(null),[r,n]=me(!1),i=lb(),o=Ni(()=>{e.current=null,n(!1),i.dispose()}),a=Ni(s=>{if(i.dispose(),e.current===null){e.current=s.currentTarget,n(!0);{let l=ab(s.currentTarget);i.addEventListener(l,"pointerup",o,!1),i.addEventListener(l,"pointermove",c=>{if(e.current){let u=db(c);n(ub(u,e.current.getBoundingClientRect()))}},!1),i.addEventListener(l,"pointercancel",o,!1)}}});return{pressed:r,pressProps:t?{}:{onPointerDown:a,onPointerUp:o,onClick:o}}}let hb=rt(void 0);function qi(){return Le(hb)}function gl(...t){return Array.from(new Set(t.flatMap(e=>typeof e=="string"?e.split(" "):[]))).filter(Boolean).join(" ")}function lu(t,e,...r){if(t in e){let i=e[t];return typeof i=="function"?i(...r):i}let n=new Error(`Tried to handle "${t}" but there is no handler defined. Only defined handlers are: ${Object.keys(e).map(i=>`"${i}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,lu),n}var fb=(t=>(t[t.None=0]="None",t[t.RenderStrategy=1]="RenderStrategy",t[t.Static=2]="Static",t))(fb||{}),mb=(t=>(t[t.Unmount=0]="Unmount",t[t.Hidden=1]="Hidden",t))(mb||{});function Ui(){let t=vb();return V(e=>gb({mergeRefs:t,...e}),[t])}function gb({ourProps:t,theirProps:e,slot:r,defaultTag:n,features:i,visible:o=!0,name:a,mergeRefs:s}){s=s??bb;let l=cu(e,t);if(o)return Zn(l,r,n,a,s);let c=i??0;if(c&2){let{static:u=!1,...d}=l;if(u)return Zn(d,r,n,a,s)}if(c&1){let{unmount:u=!0,...d}=l;return lu(u?0:1,{0(){return null},1(){return Zn({...d,hidden:!0,style:{display:"none"}},r,n,a,s)}})}return Zn(l,r,n,a,s)}function Zn(t,e={},r,n,i){let{as:o=r,children:a,refName:s="ref",...l}=wo(t,["unmount","static"]),c=t.ref!==void 0?{[s]:t.ref}:{},u=typeof a=="function"?a(e):a;"className"in l&&l.className&&typeof l.className=="function"&&(l.className=l.className(e)),l["aria-labelledby"]&&l["aria-labelledby"]===l.id&&(l["aria-labelledby"]=void 0);let d={};if(e){let p=!1,h=[];for(let[b,f]of Object.entries(e))typeof f=="boolean"&&(p=!0),f===!0&&h.push(b.replace(/([A-Z])/g,w=>`-${w.toLowerCase()}`));if(p){d["data-headlessui-state"]=h.join(" ");for(let b of h)d[`data-${b}`]=""}}if(o===Ue&&(Object.keys(cr(l)).length>0||Object.keys(cr(d)).length>0))if(!Tn(u)||Array.isArray(u)&&u.length>1){if(Object.keys(cr(l)).length>0)throw new Error(['Passing props on "Fragment"!',"",`The current component <${n} /> is rendering a "Fragment".`,"However we need to passthrough the following props:",Object.keys(cr(l)).concat(Object.keys(cr(d))).map(p=>`  - ${p}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(p=>`  - ${p}`).join(`
`)].join(`
`))}else{let p=u.props,h=p==null?void 0:p.className,b=typeof h=="function"?(...y)=>gl(h(...y),l.className):gl(h,l.className),f=b?{className:b}:{},w=cu(u.props,cr(wo(l,["ref"])));for(let y in d)y in w&&delete d[y];return hd(u,Object.assign({},w,d,c,{ref:i(wb(u),c.ref)},f))}return Ge(o,Object.assign({},wo(l,["ref"]),o!==Ue&&c,o!==Ue&&d),u)}function vb(){let t=ce([]),e=V(r=>{for(let n of t.current)n!=null&&(typeof n=="function"?n(r):n.current=r)},[]);return(...r)=>{if(!r.every(n=>n==null))return t.current=r,e}}function bb(...t){return t.every(e=>e==null)?void 0:e=>{for(let r of t)r!=null&&(typeof r=="function"?r(e):r.current=e)}}function cu(...t){if(t.length===0)return{};if(t.length===1)return t[0];let e={},r={};for(let n of t)for(let i in n)i.startsWith("on")&&typeof n[i]=="function"?(r[i]!=null||(r[i]=[]),r[i].push(n[i])):e[i]=n[i];if(e.disabled||e["aria-disabled"])for(let n in r)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(n)&&(r[n]=[i=>{var o;return(o=i==null?void 0:i.preventDefault)==null?void 0:o.call(i)}]);for(let n in r)Object.assign(e,{[n](i,...o){let a=r[n];for(let s of a){if((i instanceof Event||(i==null?void 0:i.nativeEvent)instanceof Event)&&i.defaultPrevented)return;s(i,...o)}}});return e}function du(...t){if(t.length===0)return{};if(t.length===1)return t[0];let e={},r={};for(let n of t)for(let i in n)i.startsWith("on")&&typeof n[i]=="function"?(r[i]!=null||(r[i]=[]),r[i].push(n[i])):e[i]=n[i];for(let n in r)Object.assign(e,{[n](...i){let o=r[n];for(let a of o)a==null||a(...i)}});return e}function Gi(t){var e;return Object.assign(Pn(t),{displayName:(e=t.displayName)!=null?e:t.name})}function cr(t){let e=Object.assign({},t);for(let r in e)e[r]===void 0&&delete e[r];return e}function wo(t,e=[]){let r=Object.assign({},t);for(let n of e)n in r&&delete r[n];return r}function wb(t){return Aa.version.split(".")[0]>="19"?t.props.ref:t.ref}let yb="button";function _b(t,e){var r;let n=qi(),{disabled:i=n||!1,autoFocus:o=!1,...a}=t,{isFocusVisible:s,focusProps:l}=ou({autoFocus:o}),{isHovered:c,hoverProps:u}=iu({isDisabled:i}),{pressed:d,pressProps:p}=pb({disabled:i}),h=du({ref:e,type:(r=a.type)!=null?r:"button",disabled:i||void 0,autoFocus:o},l,u,p),b=Ne(()=>({disabled:i,hover:c,focus:s,active:d,autofocus:o}),[i,c,s,d,o]);return Ui()({ourProps:h,theirProps:a,slot:b,defaultTag:yb,name:"Button"})}let La=Gi(_b),xb=rt(void 0);function uu(){return Le(xb)}let kb=Symbol();function pu(...t){let e=ce(t);xe(()=>{e.current=t},[t]);let r=Ni(n=>{for(let i of e.current)i!=null&&(typeof i=="function"?i(n):i.current=n)});return t.every(n=>n==null||(n==null?void 0:n[kb]))?void 0:r}let Da=rt(null);Da.displayName="DescriptionContext";function hu(){let t=Le(Da);if(t===null){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,hu),e}return t}function Sb(){var t,e;return(e=(t=Le(Da))==null?void 0:t.value)!=null?e:void 0}let Cb="p";function Eb(t,e){let r=Nn(),n=qi(),{id:i=`headlessui-description-${r}`,...o}=t,a=hu(),s=pu(e);ja(()=>a.register(i),[i,a.register]);let l=n||!1,c=Ne(()=>({...a.slot,disabled:l}),[a.slot,l]),u={ref:s,...a.props,id:i};return Ui()({ourProps:u,theirProps:o,slot:c,defaultTag:Cb,name:a.name||"Description"})}let Ob=Gi(Eb);Object.assign(Ob,{});let Fa=rt(null);Fa.displayName="LabelContext";function fu(){let t=Le(Fa);if(t===null){let e=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,fu),e}return t}function Nb(t){var e,r,n;let i=(r=(e=Le(Fa))==null?void 0:e.value)!=null?r:void 0;return((n=void 0)!=null?n:0)>0?[i,...t].filter(Boolean).join(" "):i}let Pb="label";function Tb(t,e){var r;let n=Nn(),i=fu(),o=uu(),a=qi(),{id:s=`headlessui-label-${n}`,htmlFor:l=o??((r=i.props)==null?void 0:r.htmlFor),passive:c=!1,...u}=t,d=pu(e);ja(()=>i.register(s),[s,i.register]);let p=Ni(w=>{let y=w.currentTarget;if(y instanceof HTMLLabelElement&&w.preventDefault(),i.props&&"onClick"in i.props&&typeof i.props.onClick=="function"&&i.props.onClick(w),y instanceof HTMLLabelElement){let v=document.getElementById(y.htmlFor);if(v){let _=v.getAttribute("disabled");if(_==="true"||_==="")return;let S=v.getAttribute("aria-disabled");if(S==="true"||S==="")return;(v instanceof HTMLInputElement&&(v.type==="radio"||v.type==="checkbox")||v.role==="radio"||v.role==="checkbox"||v.role==="switch")&&v.click(),v.focus({preventScroll:!0})}}}),h=a||!1,b=Ne(()=>({...i.slot,disabled:h}),[i.slot,h]),f={ref:d,...i.props,id:s,htmlFor:l,onClick:p};return c&&("onClick"in f&&(delete f.htmlFor,delete f.onClick),"onClick"in u&&delete u.onClick),Ui()({ourProps:f,theirProps:u,slot:b,defaultTag:l?Pb:"div",name:i.name||"Label"})}let Mb=Gi(Tb);Object.assign(Mb,{});let Ab="textarea";function Rb(t,e){let r=Nn(),n=uu(),i=qi(),{id:o=n||`headlessui-textarea-${r}`,disabled:a=i||!1,autoFocus:s=!1,invalid:l=!1,...c}=t,u=Nb(),d=Sb(),{isFocused:p,focusProps:h}=ou({autoFocus:s}),{isHovered:b,hoverProps:f}=iu({isDisabled:a}),w=du({ref:e,id:o,"aria-labelledby":u,"aria-describedby":d,"aria-invalid":l?"true":void 0,disabled:a||void 0,autoFocus:s},h,f),y=Ne(()=>({disabled:a,invalid:l,hover:b,focus:p,autofocus:s}),[a,l,b,p,s]);return Ui()({ourProps:w,theirProps:c,slot:y,defaultTag:Ab,name:"Textarea"})}let Ib=Gi(Rb);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $b=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),zb=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,n)=>n?n.toUpperCase():r.toLowerCase()),vl=t=>{const e=zb(t);return e.charAt(0).toUpperCase()+e.slice(1)},mu=(...t)=>t.filter((e,r,n)=>!!e&&e.trim()!==""&&n.indexOf(e)===r).join(" ").trim(),jb=t=>{for(const e in t)if(e.startsWith("aria-")||e==="role"||e==="title")return!0};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Lb={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Db=Pn(({color:t="currentColor",size:e=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:i="",children:o,iconNode:a,...s},l)=>Ge("svg",{ref:l,...Lb,width:e,height:e,stroke:t,strokeWidth:n?Number(r)*24/Number(e):r,className:mu("lucide",i),...!o&&!jb(s)&&{"aria-hidden":"true"},...s},[...a.map(([c,u])=>Ge(c,u)),...Array.isArray(o)?o:[o]]));/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bt=(t,e)=>{const r=Pn(({className:n,...i},o)=>Ge(Db,{ref:o,iconNode:e,className:mu(`lucide-${$b(vl(t))}`,`lucide-${t}`,n),...i}));return r.displayName=vl(t),r};/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fb=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],Hb=bt("chevron-down",Fb);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bb=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],Wb=bt("chevron-up",Bb);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vb=[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]],qb=bt("message-circle",Vb);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ub=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Gb=bt("plus",Ub);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zb=[["path",{d:"M15.39 4.39a1 1 0 0 0 1.68-.474 2.5 2.5 0 1 1 3.014 3.015 1 1 0 0 0-.474 1.68l1.683 1.682a2.414 2.414 0 0 1 0 3.414L19.61 15.39a1 1 0 0 1-1.68-.474 2.5 2.5 0 1 0-3.014 3.015 1 1 0 0 1 .474 1.68l-1.683 1.682a2.414 2.414 0 0 1-3.414 0L8.61 19.61a1 1 0 0 0-1.68.474 2.5 2.5 0 1 1-3.014-3.015 1 1 0 0 0 .474-1.68l-1.683-1.682a2.414 2.414 0 0 1 0-3.414L4.39 8.61a1 1 0 0 1 1.68.474 2.5 2.5 0 1 0 3.014-3.015 1 1 0 0 1-.474-1.68l1.683-1.682a2.414 2.414 0 0 1 3.414 0z",key:"w46dr5"}]],Yb=bt("puzzle",Zb);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kb=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],$r=bt("refresh-cw",Kb);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qb=[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]],Xb=bt("send",Qb);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jb=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],ew=bt("settings",Jb);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tw=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],rw=bt("trash-2",tw);/**
 * @license lucide-react v0.503.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nw=[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}],["path",{d:"M5 12.859a10 10 0 0 1 5.17-2.69",key:"1dl1wf"}],["path",{d:"M19 12.859a10 10 0 0 0-2.007-1.523",key:"4k23kn"}],["path",{d:"M2 8.82a15 15 0 0 1 4.177-2.643",key:"1grhjp"}],["path",{d:"M22 8.82a15 15 0 0 0-11.288-3.764",key:"z3jwby"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],gu=bt("wifi-off",nw),iw={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},vu={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},Me={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},Fe={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},At={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class x{static getFirstMatch(e,r){const n=r.match(e);return n&&n.length>0&&n[1]||""}static getSecondMatch(e,r){const n=r.match(e);return n&&n.length>1&&n[2]||""}static matchAndReturnConst(e,r,n){if(e.test(r))return n}static getWindowsVersionName(e){switch(e){case"NT":return"NT";case"XP":return"XP";case"NT 5.0":return"2000";case"NT 5.1":return"XP";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(e){const r=e.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(r.push(0),r[0]===10)switch(r[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(e){const r=e.split(".").splice(0,2).map(n=>parseInt(n,10)||0);if(r.push(0),!(r[0]===1&&r[1]<5)){if(r[0]===1&&r[1]<6)return"Cupcake";if(r[0]===1&&r[1]>=6)return"Donut";if(r[0]===2&&r[1]<2)return"Eclair";if(r[0]===2&&r[1]===2)return"Froyo";if(r[0]===2&&r[1]>2)return"Gingerbread";if(r[0]===3)return"Honeycomb";if(r[0]===4&&r[1]<1)return"Ice Cream Sandwich";if(r[0]===4&&r[1]<4)return"Jelly Bean";if(r[0]===4&&r[1]>=4)return"KitKat";if(r[0]===5)return"Lollipop";if(r[0]===6)return"Marshmallow";if(r[0]===7)return"Nougat";if(r[0]===8)return"Oreo";if(r[0]===9)return"Pie"}}static getVersionPrecision(e){return e.split(".").length}static compareVersions(e,r,n=!1){const i=x.getVersionPrecision(e),o=x.getVersionPrecision(r);let a=Math.max(i,o),s=0;const l=x.map([e,r],c=>{const u=a-x.getVersionPrecision(c),d=c+new Array(u+1).join(".0");return x.map(d.split("."),p=>new Array(20-p.length).join("0")+p).reverse()});for(n&&(s=a-Math.min(i,o)),a-=1;a>=s;){if(l[0][a]>l[1][a])return 1;if(l[0][a]===l[1][a]){if(a===s)return 0;a-=1}else if(l[0][a]<l[1][a])return-1}}static map(e,r){const n=[];let i;if(Array.prototype.map)return Array.prototype.map.call(e,r);for(i=0;i<e.length;i+=1)n.push(r(e[i]));return n}static find(e,r){let n,i;if(Array.prototype.find)return Array.prototype.find.call(e,r);for(n=0,i=e.length;n<i;n+=1){const o=e[n];if(r(o,n))return o}}static assign(e,...r){const n=e;let i,o;if(Object.assign)return Object.assign(e,...r);for(i=0,o=r.length;i<o;i+=1){const a=r[i];typeof a=="object"&&a!==null&&Object.keys(a).forEach(s=>{n[s]=a[s]})}return e}static getBrowserAlias(e){return iw[e]}static getBrowserTypeByAlias(e){return vu[e]||""}}const be=/version\/(\d+(\.?_?\d+)+)/i,ow=[{test:[/googlebot/i],describe(t){const e={name:"Googlebot"},r=x.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/opera/i],describe(t){const e={name:"Opera"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/opr\/|opios/i],describe(t){const e={name:"Opera"},r=x.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/SamsungBrowser/i],describe(t){const e={name:"Samsung Internet for Android"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/Whale/i],describe(t){const e={name:"NAVER Whale Browser"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/MZBrowser/i],describe(t){const e={name:"MZ Browser"},r=x.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/focus/i],describe(t){const e={name:"Focus"},r=x.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/swing/i],describe(t){const e={name:"Swing"},r=x.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/coast/i],describe(t){const e={name:"Opera Coast"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(t){const e={name:"Opera Touch"},r=x.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/yabrowser/i],describe(t){const e={name:"Yandex Browser"},r=x.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/ucbrowser/i],describe(t){const e={name:"UC Browser"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/Maxthon|mxios/i],describe(t){const e={name:"Maxthon"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/epiphany/i],describe(t){const e={name:"Epiphany"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/puffin/i],describe(t){const e={name:"Puffin"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/sleipnir/i],describe(t){const e={name:"Sleipnir"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/k-meleon/i],describe(t){const e={name:"K-Meleon"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/micromessenger/i],describe(t){const e={name:"WeChat"},r=x.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/qqbrowser/i],describe(t){const e={name:/qqbrowserlite/i.test(t)?"QQ Browser Lite":"QQ Browser"},r=x.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/msie|trident/i],describe(t){const e={name:"Internet Explorer"},r=x.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/\sedg\//i],describe(t){const e={name:"Microsoft Edge"},r=x.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/edg([ea]|ios)/i],describe(t){const e={name:"Microsoft Edge"},r=x.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/vivaldi/i],describe(t){const e={name:"Vivaldi"},r=x.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/seamonkey/i],describe(t){const e={name:"SeaMonkey"},r=x.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/sailfish/i],describe(t){const e={name:"Sailfish"},r=x.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,t);return r&&(e.version=r),e}},{test:[/silk/i],describe(t){const e={name:"Amazon Silk"},r=x.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/phantom/i],describe(t){const e={name:"PhantomJS"},r=x.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/slimerjs/i],describe(t){const e={name:"SlimerJS"},r=x.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(t){const e={name:"BlackBerry"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/(web|hpw)[o0]s/i],describe(t){const e={name:"WebOS Browser"},r=x.getFirstMatch(be,t)||x.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/bada/i],describe(t){const e={name:"Bada"},r=x.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/tizen/i],describe(t){const e={name:"Tizen"},r=x.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/qupzilla/i],describe(t){const e={name:"QupZilla"},r=x.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/firefox|iceweasel|fxios/i],describe(t){const e={name:"Firefox"},r=x.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/electron/i],describe(t){const e={name:"Electron"},r=x.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/MiuiBrowser/i],describe(t){const e={name:"Miui"},r=x.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/chromium/i],describe(t){const e={name:"Chromium"},r=x.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,t)||x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/chrome|crios|crmo/i],describe(t){const e={name:"Chrome"},r=x.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/GSA/i],describe(t){const e={name:"Google Search"},r=x.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test(t){const e=!t.test(/like android/i),r=t.test(/android/i);return e&&r},describe(t){const e={name:"Android Browser"},r=x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/playstation 4/i],describe(t){const e={name:"PlayStation 4"},r=x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/safari|applewebkit/i],describe(t){const e={name:"Safari"},r=x.getFirstMatch(be,t);return r&&(e.version=r),e}},{test:[/.*/i],describe(t){const e=/^(.*)\/(.*) /,r=/^(.*)\/(.*)[ \t]\((.*)/,n=t.search("\\(")!==-1?r:e;return{name:x.getFirstMatch(n,t),version:x.getSecondMatch(n,t)}}}],aw=[{test:[/Roku\/DVP/],describe(t){const e=x.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,t);return{name:Fe.Roku,version:e}}},{test:[/windows phone/i],describe(t){const e=x.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,t);return{name:Fe.WindowsPhone,version:e}}},{test:[/windows /i],describe(t){const e=x.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,t),r=x.getWindowsVersionName(e);return{name:Fe.Windows,version:e,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(t){const e={name:Fe.iOS},r=x.getSecondMatch(/(Version\/)(\d[\d.]+)/,t);return r&&(e.version=r),e}},{test:[/macintosh/i],describe(t){const e=x.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,t).replace(/[_\s]/g,"."),r=x.getMacOSVersionName(e),n={name:Fe.MacOS,version:e};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe(t){const e=x.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,t).replace(/[_\s]/g,".");return{name:Fe.iOS,version:e}}},{test(t){const e=!t.test(/like android/i),r=t.test(/android/i);return e&&r},describe(t){const e=x.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,t),r=x.getAndroidVersionName(e),n={name:Fe.Android,version:e};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe(t){const e=x.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,t),r={name:Fe.WebOS};return e&&e.length&&(r.version=e),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(t){const e=x.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,t)||x.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,t)||x.getFirstMatch(/\bbb(\d+)/i,t);return{name:Fe.BlackBerry,version:e}}},{test:[/bada/i],describe(t){const e=x.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,t);return{name:Fe.Bada,version:e}}},{test:[/tizen/i],describe(t){const e=x.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,t);return{name:Fe.Tizen,version:e}}},{test:[/linux/i],describe(){return{name:Fe.Linux}}},{test:[/CrOS/],describe(){return{name:Fe.ChromeOS}}},{test:[/PlayStation 4/],describe(t){const e=x.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,t);return{name:Fe.PlayStation4,version:e}}}],sw=[{test:[/googlebot/i],describe(){return{type:"bot",vendor:"Google"}}},{test:[/huawei/i],describe(t){const e=x.getFirstMatch(/(can-l01)/i,t)&&"Nova",r={type:Me.mobile,vendor:"Huawei"};return e&&(r.model=e),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe(){return{type:Me.tablet,vendor:"Nexus"}}},{test:[/ipad/i],describe(){return{type:Me.tablet,vendor:"Apple",model:"iPad"}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(){return{type:Me.tablet,vendor:"Apple",model:"iPad"}}},{test:[/kftt build/i],describe(){return{type:Me.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"}}},{test:[/silk/i],describe(){return{type:Me.tablet,vendor:"Amazon"}}},{test:[/tablet(?! pc)/i],describe(){return{type:Me.tablet}}},{test(t){const e=t.test(/ipod|iphone/i),r=t.test(/like (ipod|iphone)/i);return e&&!r},describe(t){const e=x.getFirstMatch(/(ipod|iphone)/i,t);return{type:Me.mobile,vendor:"Apple",model:e}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe(){return{type:Me.mobile,vendor:"Nexus"}}},{test:[/[^-]mobi/i],describe(){return{type:Me.mobile}}},{test(t){return t.getBrowserName(!0)==="blackberry"},describe(){return{type:Me.mobile,vendor:"BlackBerry"}}},{test(t){return t.getBrowserName(!0)==="bada"},describe(){return{type:Me.mobile}}},{test(t){return t.getBrowserName()==="windows phone"},describe(){return{type:Me.mobile,vendor:"Microsoft"}}},{test(t){const e=Number(String(t.getOSVersion()).split(".")[0]);return t.getOSName(!0)==="android"&&e>=3},describe(){return{type:Me.tablet}}},{test(t){return t.getOSName(!0)==="android"},describe(){return{type:Me.mobile}}},{test(t){return t.getOSName(!0)==="macos"},describe(){return{type:Me.desktop,vendor:"Apple"}}},{test(t){return t.getOSName(!0)==="windows"},describe(){return{type:Me.desktop}}},{test(t){return t.getOSName(!0)==="linux"},describe(){return{type:Me.desktop}}},{test(t){return t.getOSName(!0)==="playstation 4"},describe(){return{type:Me.tv}}},{test(t){return t.getOSName(!0)==="roku"},describe(){return{type:Me.tv}}}],lw=[{test(t){return t.getBrowserName(!0)==="microsoft edge"},describe(t){if(/\sedg\//i.test(t))return{name:At.Blink};const e=x.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,t);return{name:At.EdgeHTML,version:e}}},{test:[/trident/i],describe(t){const e={name:At.Trident},r=x.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test(t){return t.test(/presto/i)},describe(t){const e={name:At.Presto},r=x.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test(t){const e=t.test(/gecko/i),r=t.test(/like gecko/i);return e&&!r},describe(t){const e={name:At.Gecko},r=x.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}},{test:[/(apple)?webkit\/537\.36/i],describe(){return{name:At.Blink}}},{test:[/(apple)?webkit/i],describe(t){const e={name:At.WebKit},r=x.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,t);return r&&(e.version=r),e}}];class bl{constructor(e,r=!1){if(e==null||e==="")throw new Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},r!==!0&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};const e=x.find(ow,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};const e=x.find(aw,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){const{name:r}=this.getOS();return e?String(r).toLowerCase()||"":r||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){const{type:r}=this.getPlatform();return e?String(r).toLowerCase()||"":r||""}parsePlatform(){this.parsedResult.platform={};const e=x.find(sw,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};const e=x.find(lw,r=>{if(typeof r.test=="function")return r.test(this);if(r.test instanceof Array)return r.test.some(n=>this.test(n));throw new Error("Browser's test function is not valid")});return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return x.assign({},this.parsedResult)}satisfies(e){const r={};let n=0;const i={};let o=0;if(Object.keys(e).forEach(a=>{const s=e[a];typeof s=="string"?(i[a]=s,o+=1):typeof s=="object"&&(r[a]=s,n+=1)}),n>0){const a=Object.keys(r),s=x.find(a,c=>this.isOS(c));if(s){const c=this.satisfies(r[s]);if(c!==void 0)return c}const l=x.find(a,c=>this.isPlatform(c));if(l){const c=this.satisfies(r[l]);if(c!==void 0)return c}}if(o>0){const a=Object.keys(i),s=x.find(a,l=>this.isBrowser(l,!0));if(s!==void 0)return this.compareVersion(i[s])}}isBrowser(e,r=!1){const n=this.getBrowserName().toLowerCase();let i=e.toLowerCase();const o=x.getBrowserTypeByAlias(i);return r&&o&&(i=o.toLowerCase()),i===n}compareVersion(e){let r=[0],n=e,i=!1;const o=this.getBrowserVersion();if(typeof o=="string")return e[0]===">"||e[0]==="<"?(n=e.substr(1),e[1]==="="?(i=!0,n=e.substr(2)):r=[],e[0]===">"?r.push(1):r.push(-1)):e[0]==="="?n=e.substr(1):e[0]==="~"&&(i=!0,n=e.substr(1)),r.indexOf(x.compareVersions(o,n,i))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,r=!1){return this.isBrowser(e,r)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some(r=>this.is(r))}}/*!
 * Bowser - a browser detector
 * https://github.com/lancedikson/bowser
 * MIT License | (c) Dustin Diaz 2012-2015
 * MIT License | (c) Denis Demchenko 2015-2019
 */class cw{static getParser(e,r=!1){if(typeof e!="string")throw new Error("UserAgent should be a string");return new bl(e,r)}static parse(e){return new bl(e).getResult()}static get BROWSER_MAP(){return vu}static get ENGINE_MAP(){return At}static get OS_MAP(){return Fe}static get PLATFORMS_MAP(){return Me}}const dw=()=>Ne(()=>{{const t=cw.parse(window.navigator.userAgent);return{browser:t.browser,engine:t.engine,os:t.os}}},[]);function uw(t){return dw().os.name.toLowerCase().includes("mac")?na[t].keyComboMac:na[t].keyComboDefault}function pw(){const t=An(),[e,r]=me(!1),n=Ne(()=>t.chats.find(f=>f.id===t.currentChatId),[t.chats,t.currentChatId]),i=Ne(()=>(n==null?void 0:n.inputValue)||"",[n==null?void 0:n.inputValue]),o=V(f=>{t.setChatInput(t.currentChatId,f)},[t.setChatInput,t.currentChatId]),a=V(()=>{!n||!i.trim()||t.addMessage(n.id,i)},[n,i,t.addMessage]),s=V(f=>{f.key==="Enter"&&!f.shiftKey&&!e&&(f.preventDefault(),a())},[a,e]),l=V(()=>{r(!0)},[]),c=V(()=>{r(!1)},[]),u=ce(null);xe(()=>{var f,w,y;const v=()=>{var _;return(_=u.current)==null?void 0:_.focus()};return t.isPromptCreationActive?((f=u.current)==null||f.focus(),(w=u.current)==null||w.addEventListener("blur",v)):(y=u.current)==null||y.blur(),()=>{var _;(_=u.current)==null||_.removeEventListener("blur",v)}},[t.isPromptCreationActive]);const d=Ne(()=>Ae("flex size-8 items-center justify-center rounded-full bg-transparent p-1 text-zinc-950 opacity-20 transition-all duration-150",i.length>0&&"bg-blue-600 text-white opacity-100",t.promptState==="loading"&&"cursor-not-allowed bg-zinc-300 text-zinc-500 opacity-30"),[i.length,t.promptState]),p=Ne(()=>Ae("h-full w-full flex-1 resize-none bg-transparent text-zinc-950 transition-all duration-150 placeholder:text-zinc-950/50 focus:outline-none",t.promptState==="loading"&&"text-zinc-500 placeholder:text-zinc-400"),[t.promptState]),h=Ne(()=>{const f="flex h-24 w-full flex-1 flex-row items-end gap-1 rounded-2xl p-4 text-sm text-zinc-950 shadow-md backdrop-blur transition-all duration-150 placeholder:text-zinc-950/70";switch(t.promptState){case"loading":return Ae(f,"border-2 border-transparent bg-zinc-50/80","chat-loading-gradient");case"success":return Ae(f,"border-2 border-transparent bg-zinc-50/80","chat-success-border");case"error":return Ae(f,"border-2 border-transparent bg-zinc-50/80","chat-error-border animate-shake");default:return Ae(f,"border border-border/30 bg-zinc-50/80")}},[t.promptState]),b=uw(Ci.CTRL_ALT_C);return m("div",{className:h,onClick:()=>t.startPromptCreation(),role:"button",tabIndex:0,children:[m(Ib,{ref:u,className:p,value:i,onChange:f=>o(f.currentTarget.value),onKeyDown:s,onCompositionStart:l,onCompositionEnd:c,placeholder:t.isPromptCreationActive?t.promptState==="loading"?"Processing...":"Enter prompt...":`What do you want to change? (${b})`,disabled:t.promptState==="loading"}),m(La,{className:d,disabled:i.length===0||t.promptState==="loading",onClick:a,children:m(Xb,{className:"size-4"})})]})}const Ha=rt(null),hw=({containerRef:t,children:e,snapAreas:r,onDragStart:n,onDragEnd:i})=>{const[o,a]=me({top:0,left:0,right:0,bottom:0});xe(()=>{if(!t.current)return;const b=()=>{if(t.current){const w=t.current.getBoundingClientRect();a({top:w.top,left:w.left,right:w.right,bottom:w.bottom})}};b();const f=new ResizeObserver(b);return f.observe(t.current),window.addEventListener("resize",b),()=>{t.current&&f.unobserve(t.current),f.disconnect(),window.removeEventListener("resize",b)}},[t]);const s=ce(new Set),l=ce(new Set),c=V(b=>(s.current.add(b),()=>s.current.delete(b)),[]),u=V(b=>(l.current.add(b),()=>l.current.delete(b)),[]),d=V(()=>{n&&n(),s.current.forEach(b=>b())},[n]),p=V(()=>{i&&i(),l.current.forEach(b=>b())},[i]),h={borderLocation:o,snapAreas:r,registerDragStart:c,registerDragEnd:u,emitDragStart:d,emitDragEnd:p};return m(Ha.Provider,{value:h,children:e})};function fw(t){const e=Le(Ha),r=ce(e);xe(()=>{r.current=e},[e]);const n=ce(null),i=ce(null),[o,a]=me(null),[s,l]=me(null),c=ce(null),u=ce(null),d=ce(null),p=ce(!1),h=ce(t.initialRelativeCenter),[b,f]=me(null),{startThreshold:w=3,areaSnapThreshold:y=60,onDragStart:v,onDragEnd:_,initialSnapArea:S,springStiffness:E=.2,springDampness:I=.55}=t,H=ce(null),R=ce({x:0,y:0}),k=ce(!1);xe(()=>{if(S&&e&&e.borderLocation&&e.snapAreas&&e.snapAreas[S]&&!p.current){const{top:G,left:fe,right:le,bottom:U}=e.borderLocation,L=le-fe,te=U-G,se={topLeft:{x:fe,y:G},topRight:{x:le,y:G},bottomLeft:{x:fe,y:U},bottomRight:{x:le,y:U}}[S];if(se&&L>0&&te>0){const Pe=(se.x-fe)/L,X=(se.y-G)/te;h.current={x:Pe,y:X}}else se&&console.warn("useDraggable: Container for initialSnapArea has zero width or height. Cannot calculate relative center from snap area. Falling back to initialRelativeCenter or undefined.")}},[S,e]);function O(G){const{top:fe,left:le,right:U,bottom:L}=G,te=(le+U)/2;return{topLeft:{x:le,y:fe},topCenter:{x:te,y:fe},topRight:{x:U,y:fe},bottomLeft:{x:le,y:L},bottomCenter:{x:te,y:L},bottomRight:{x:U,y:L}}}const $=V(()=>{var G,fe;const le=n.current;if(!le)return;const U=le.offsetWidth,L=le.offsetHeight,te=le.offsetParent;let se=0,Pe=0,X=window.innerWidth,he=window.innerHeight;if(te){const ve=te.getBoundingClientRect();se=ve.left,Pe=ve.top,X=te.offsetWidth||window.innerWidth,he=te.offsetHeight||window.innerHeight}let ke=null,ye=null;const Ie=h.current;let Ze=null,Et=null;const Ot=r.current;let wt=!0,yt=!0;if(p.current&&c.current&&d.current&&Ot&&Ot.borderLocation&&Ot.snapAreas){const ve={x:d.current.x-c.current.x,y:d.current.y-c.current.y},qt=O(Ot.borderLocation);let Te=Number.POSITIVE_INFINITY,ze=null,Ut=null;for(const Gt in Ot.snapAreas)if(Ot.snapAreas[Gt]){const $n=qt[Gt];if(!$n)continue;const Va=Math.hypot($n.x-ve.x,$n.y-ve.y);Va<Te&&(Te=Va,ze=Gt,Ut=$n)}ze&&Ut&&Te<=y&&(Ze=ze,Et=Ut),yt=(ve.x-se)/X<=.5,wt=(ve.y-Pe)/he<=.5}if(p.current&&Et)ke=Et.x,ye=Et.y,f(Ze),yt=(Et.x-se)/X<=.5,wt=(Et.y-Pe)/he<=.5;else if(p.current&&c.current&&d.current)ke=d.current.x-c.current.x,ye=d.current.y-c.current.y,f(null),yt=(ke-se)/X<=.5,wt=(ye-Pe)/he<=.5;else{if(Ie&&X>0&&he>0){if(wt=Ie.y<=.5,yt=Ie.x<=.5,yt){const ve=X*Ie.x;ke=se+ve}else{const ve=X*(1-Ie.x);ke=se+X-ve}if(wt){const ve=he*Ie.y;ye=Pe+ve}else{const ve=he*(1-Ie.y);ye=Pe+he-ve}}else{!((G=n.current)!=null&&G.style.left)&&!((fe=n.current)!=null&&fe.style.top)&&console.warn("useDraggable: Cannot determine position. Parent has no dimensions or initialRelativeCenter was not effectively set.");return}f(null)}if(ke===null||ye===null)return;const{borderLocation:Ye}=r.current||{borderLocation:void 0};if(Ye&&U>0&&L>0){const ve=Ye.right-Ye.left,qt=Ye.bottom-Ye.top;let Te=ke,ze=ye;if(U>=ve)Te=Ye.left+ve/2;else{const Ut=Ye.left+U/2,Gt=Ye.right-U/2;Te=Math.max(Ut,Math.min(Te,Gt))}if(L>=qt)ze=Ye.top+qt/2;else{const Ut=Ye.top+L/2,Gt=Ye.bottom-L/2;ze=Math.max(Ut,Math.min(ze,Gt))}ke=Te,ye=ze}if(!H.current){H.current={x:ke,y:ye},R.current={x:0,y:0};const ve=ke-U/2,qt=ye-L/2,Te=le.style;if(Te.right="",Te.bottom="",Te.left="",Te.top="",yt){const ze=ve-se;Te.left=X>0?`${(ze/X*100).toFixed(2)}%`:"0px",Te.right=""}else{const ze=se+X-(ve+U);Te.right=X>0?`${(ze/X*100).toFixed(2)}%`:"0px",Te.left=""}if(wt){const ze=qt-Pe;Te.top=he>0?`${(ze/he*100).toFixed(2)}%`:"0px",Te.bottom=""}else{const ze=Pe+he-(qt+L);Te.bottom=he>0?`${(ze/he*100).toFixed(2)}%`:"0px",Te.top=""}k.current=!0;return}if(!k.current){k.current=!0;return}const Be=H.current,$e=R.current,Vt=ke-Be.x,Ke=ye-Be.y,dt=E*Vt-I*$e.x,In=E*Ke-I*$e.y;$e.x+=dt,$e.y+=In,Be.x+=$e.x,Be.y+=$e.y;const Nt=.5;Math.abs(Vt)<Nt&&Math.abs(Ke)<Nt&&Math.abs($e.x)<Nt&&Math.abs($e.y)<Nt&&(Be.x=ke,Be.y=ye,$e.x=0,$e.y=0),H.current={...Be},R.current={...$e};const Ba=Be.x-U/2,Wa=Be.y-L/2,Je=le.style;if(Je.right="",Je.bottom="",Je.left="",Je.top="",yt){const ve=Ba-se;Je.left=X>0?`${(ve/X*100).toFixed(2)}%`:"0px",Je.right=""}else{const ve=se+X-(Ba+U);Je.right=X>0?`${(ve/X*100).toFixed(2)}%`:"0px",Je.left=""}if(wt){const ve=Wa-Pe;Je.top=he>0?`${(ve/he*100).toFixed(2)}%`:"0px",Je.bottom=""}else{const ve=Pe+he-(Wa+L);Je.bottom=he>0?`${(ve/he*100).toFixed(2)}%`:"0px",Je.top=""}(Math.abs(Be.x-ke)>Nt||Math.abs(Be.y-ye)>Nt||Math.abs($e.x)>Nt||Math.abs($e.y)>Nt||p.current)&&requestAnimationFrame($)},[y,E,I]),[j,N]=me(!1),T=V(G=>{var fe;if(p.current){_&&_(),(fe=r.current)!=null&&fe.emitDragEnd&&r.current.emitDragEnd(),N(!0),setTimeout(()=>N(!1),0);const le=n.current,U=r.current;if(le&&U&&U.borderLocation){const L=le.offsetWidth,te=le.offsetHeight,se=le.offsetParent;let Pe=0,X=0,he=window.innerWidth,ke=window.innerHeight;if(se){const Ke=se.getBoundingClientRect();Pe=Ke.left,X=Ke.top,he=se.offsetWidth||window.innerWidth,ke=se.offsetHeight||window.innerHeight}let ye=0,Ie=0;d.current&&c.current?(ye=d.current.x-c.current.x,Ie=d.current.y-c.current.y):H.current&&(ye=H.current.x,Ie=H.current.y);const Ze=U.borderLocation,Et=Ze.left+L/2,Ot=Ze.right-L/2,wt=Ze.top+te/2,yt=Ze.bottom-te/2;ye=Math.max(Et,Math.min(ye,Ot)),Ie=Math.max(wt,Math.min(Ie,yt));const Ye=O(Ze);let Be=Number.POSITIVE_INFINITY,$e=null,Vt=null;for(const Ke in U.snapAreas)if(U.snapAreas[Ke]){const dt=Ye[Ke];if(!dt)continue;const In=Math.hypot(dt.x-ye,dt.y-Ie);In<Be&&(Be=In,$e=Ke,Vt=dt)}if($e&&Vt){f($e);const Ke=(Vt.x-Pe)/he,dt=(Vt.y-X)/ke;h.current={x:Ke,y:dt}}else{f(null);const Ke=(ye-Pe)/he,dt=(Ie-X)/ke;h.current={x:Ke,y:dt}}}}u.current=null,p.current=!1,window.removeEventListener("mousemove",pe,{capture:!0}),window.removeEventListener("mouseup",T,{capture:!0}),n.current&&(n.current.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor=""},[_]),pe=V(G=>{var fe;u.current&&(Math.hypot(G.clientX-u.current.x,G.clientY-u.current.y)>w&&!p.current&&(p.current=!0,n.current&&(n.current.style.userSelect="none"),document.body.style.userSelect="none",document.body.style.cursor="grabbing",v&&v(),(fe=r.current)!=null&&fe.emitDragStart&&r.current.emitDragStart(),requestAnimationFrame($)),d.current={x:G.clientX,y:G.clientY})},[w,v,$]),Z=V(G=>{if(G.button!==0)return;const fe=i.current,le=n.current;if(fe){if(!fe.contains(G.target)&&G.target!==fe)return}else if(le){if(!le.contains(G.target)&&G.target!==le)return}else{console.error("Draggable element or handle ref not set in mouseDownHandler");return}if(u.current={x:G.clientX,y:G.clientY},!n.current){console.error("Draggable element ref not set in mouseDownHandler");return}const U=n.current.getBoundingClientRect(),L=U.left+U.width/2,te=U.top+U.height/2;c.current={x:G.clientX-L,y:G.clientY-te},window.addEventListener("mousemove",pe,{capture:!0}),window.addEventListener("mouseup",T,{capture:!0})},[pe,T]);xe(()=>{const G=s||o;return G&&G.addEventListener("mousedown",Z),()=>{G&&G.removeEventListener("mousedown",Z),p.current&&(_&&_(),p.current=!1,o&&(o.style.userSelect=""),document.body.style.userSelect="",document.body.style.cursor="",window.removeEventListener("mousemove",pe,{capture:!0}),window.removeEventListener("mouseup",T,{capture:!0}))}},[o,s,Z,_,pe,T]),xe(()=>{n.current&&e&&e.borderLocation&&h.current&&!p.current&&!k.current&&requestAnimationFrame(()=>{n.current&&$()})},[o,e,t.initialRelativeCenter,S,$]);const ge=V(G=>{a(G),n.current=G},[]),B=V(G=>{l(G),i.current=G},[]);return{draggableRef:ge,handleRef:B,position:{snapArea:b,isTopHalf:h.current?h.current.y<=.5:!0,isLeftHalf:h.current?h.current.x<=.5:!0},wasDragged:j}}function xn({children:t}){return m("div",{className:"fade-in slide-in-from-right-2 flex max-h-sm max-w-full animate-in snap-start flex-col items-center justify-between gap-1 py-0.5",children:t})}function mw(t){return m("div",{className:"relative flex w-full shrink-0 items-center justify-center",children:[t.children,t.badgeContent&&m("div",{className:Ae("bg-blue-600 text-white",t.badgeClassName,"pointer-events-none absolute right-0 bottom-0 flex h-3 w-max min-w-3 max-w-8 select-none items-center justify-center truncate rounded-full px-0.5 font-semibold text-[0.5em]"),children:t.badgeContent}),t.statusDot&&m("div",{className:Ae("bg-rose-600",t.statusDotClassName,"pointer-events-none absolute top-0 right-0 size-1.5 rounded-full")})]})}const Pr=Pn(({badgeContent:t,badgeClassName:e,statusDot:r,statusDotClassName:n,tooltipHint:i,variant:o="default",active:a,...s},l)=>{const c=m(La,{ref:l,...s,className:Ae("flex items-center justify-center rounded-full p-1 text-zinc-950 ring ring-transparent transition-all duration-150 hover:bg-zinc-950/5",o==="default"?"size-8":"h-8 rounded-full",a&&"bg-white/40 ring-zinc-950/20",s.className)});return m(mw,{badgeContent:t,badgeClassName:e,statusDot:r,statusDotClassName:n,children:c})});Pr.displayName="ToolbarButton";const gw=({color:t="default",loading:e=!1,loadingSpeed:r="slow",...n})=>{const i={default:"fill-stagewise-700 stroke-none",black:"fill-zinc-950 stroke-none",white:"fill-white stroke-none",zinc:"fill-zinc-500/50 stroke-none",current:"fill-current stroke-none",gradient:"fill-white stroke-black/30 stroke-1"};return m("div",{className:`relative ${t==="gradient"?"overflow-hidden rounded-full":"overflow-visible"} ${n.className||""} ${e?"drop-shadow-xl":""} aspect-square`,children:[t==="gradient"&&m("div",{className:"absolute inset-0",children:[m("div",{className:"absolute inset-0 size-full bg-gradient-to-tr from-indigo-700 via-blue-500 to-teal-500"}),m("div",{className:"absolute top-1/2 left-1/2 size-9/12 bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),m("div",{className:"absolute right-1/2 bottom-1/2 size-full bg-[radial-gradient(circle,rgba(219,39,119,0.2)_0%,rgba(219,39,119,0)_100%)]"}),m("div",{className:"absolute top-0 left-[-10%] size-[120%] bg-[radial-gradient(circle,rgba(255,255,255,0)_60%,rgba(255,255,255,0.2)_70%)]"}),m("div",{className:"absolute top-[-20%] left-0 h-[120%] w-full bg-[radial-gradient(circle,rgba(55,48,163,0)_55%,rgba(55,48,163,0.35)_73%)]"})]}),m("svg",{className:`absolute overflow-visible ${t==="gradient"?"top-[25%] left-[25%] h-[50%] w-[50%] drop-shadow-indigo-950 drop-shadow-xs":"top-0 left-0 h-full w-full"}`,viewBox:"0 0 2048 2048",children:[m("title",{children:"stagewise"}),m("ellipse",{className:i[t]+(e?" animate-pulse":""),id:"path3",ry:"624",rx:"624",cy:"1024",cx:"1024"})]}),m("svg",{className:`absolute overflow-visible ${t==="gradient"?"top-[25%] left-[25%] h-[50%] w-[50%]":"top-0 left-0 h-full w-full"}`,viewBox:"0 0 2048 2048",children:m("path",{id:"path4",className:`origin-center ${i[t]}${e?r==="fast"?" animate-spin-fast":" animate-spin-slow":""}`,d:"M 1024 0 A 1024 1024 0 0 0 0 1024 A 1024 1024 0 0 0 1024 2048 L 1736 2048 L 1848 2048 C 1958.7998 2048 2048 1958.7998 2048 1848 L 2048 1736 L 2048 1024 A 1024 1024 0 0 0 1024 0 z M 1024.9414 200 A 824 824 0 0 1 1848.9414 1024 A 824 824 0 0 1 1024.9414 1848 A 824 824 0 0 1 200.94141 1024 A 824 824 0 0 1 1024.9414 200 z "})})]})},vw=({onOpenPanel:t,isActive:e=!1})=>m(xn,{children:m(Pr,{onClick:t,active:e,children:m(ew,{className:"size-4"})})}),bw=({onClose:t})=>m(Qt,{children:[m(Qt.Header,{title:"Settings"}),m(Qt.Content,{children:m(ww,{})}),m(Qt.Content,{children:m(yw,{})})]}),ww=()=>{const{windows:t,isDiscovering:e,discoveryError:r,discover:n,selectedSession:i,selectSession:o}=Bt(),a=c=>{const u=c.target,d=u.value===""?void 0:u.value;o(d)},{appName:s}=Bt(),l=()=>{n()};return m("div",{className:"space-y-4 pb-4",children:[m("div",{children:[m("label",{htmlFor:"session-select",className:"mb-2 block font-medium text-sm text-zinc-700",children:["IDE Window ",s&&`(${s})`]}),m("div",{className:"flex w-full items-center space-x-2",children:[m("select",{id:"session-select",value:(i==null?void 0:i.sessionId)||"",onChange:a,className:"h-8 min-w-0 flex-1 rounded-lg border border-zinc-300 bg-zinc-500/10 px-3 text-sm backdrop-saturate-150 focus:border-zinc-500 focus:outline-none",disabled:e,children:[m("option",{value:"",disabled:!0,children:t.length>0?"Select an IDE window...":"No windows available"}),t.map(c=>m("option",{value:c.sessionId,children:[c.displayName," - Port ",c.port]},c.sessionId))]}),m("button",{type:"button",onClick:l,disabled:e,className:"flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-zinc-500/10 backdrop-saturate-150 transition-colors hover:bg-zinc-500/20 disabled:opacity-50",title:"Refresh window list",children:m($r,{className:`size-4 ${e?"animate-spin":""}`})})]}),r&&m("p",{className:"mt-1 text-red-600 text-sm",children:["Error discovering windows: ",r]}),!e&&t.length===0&&!r&&m("p",{className:"mt-1 text-sm text-zinc-500",children:"No IDE windows found. Make sure the Stagewise extension is installed and running."})]}),i&&m("div",{className:"rounded-lg bg-blue-50 p-3",children:[m("p",{className:"text-blue-800 text-sm",children:[m("strong",{children:"Selected:"})," ",i.displayName]}),m("p",{className:"mt-1 text-blue-600 text-xs",children:["Session ID: ",i.sessionId.substring(0,8),"..."]})]}),!i&&t.length>0&&m("div",{className:"rounded-lg bg-amber-50 p-3",children:m("p",{className:"text-amber-800 text-sm",children:[m("strong",{children:"No window selected:"})," Please select an IDE window above to connect."]})})]})},yw=()=>m("div",{className:"space-y-2 text-xs text-zinc-700",children:[m("div",{className:"my-2 flex flex-wrap items-center gap-3",children:[m("a",{href:"https://github.com/stagewise-io/stagewise",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-blue-700 hover:underline",title:"GitHub Repository",children:[m("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:m("path",{d:"M12 .5C5.73.5.5 5.73.5 12c0 5.08 3.29 9.39 7.86 10.91.58.11.79-.25.79-.56 0-.28-.01-1.02-.02-2-3.2.7-3.88-1.54-3.88-1.54-.53-1.34-1.3-1.7-1.3-1.7-1.06-.72.08-.71.08-.71 1.17.08 1.78 1.2 1.78 1.2 1.04 1.78 2.73 1.27 3.4.97.11-.75.41-1.27.74-1.56-2.56-.29-5.26-1.28-5.26-5.7 0-1.26.45-2.29 1.19-3.1-.12-.29-.52-1.46.11-3.05 0 0 .98-.31 3.2 1.18a11.1 11.1 0 0 1 2.92-.39c.99 0 1.99.13 2.92.39 2.22-1.49 3.2-1.18 3.2-1.18.63 1.59.23 2.76.11 3.05.74.81 1.19 1.84 1.19 3.1 0 4.43-2.7 5.41-5.27 5.7.42.36.79 1.08.79 2.18 0 1.57-.01 2.84-.01 3.23 0 .31.21.68.8.56C20.71 21.39 24 17.08 24 12c0-6.27-5.23-11.5-12-11.5z"})}),"GitHub"]}),m("a",{href:"https://discord.gg/gkdGsDYaKA",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-indigo-700 hover:underline",title:"Join our Discord",children:[m("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:m("path",{d:"M20.317 4.369A19.791 19.791 0 0 0 16.885 3.2a.117.117 0 0 0-.124.06c-.537.96-1.13 2.22-1.552 3.2a18.524 18.524 0 0 0-5.418 0c-.423-.98-1.016-2.24-1.553-3.2a.117.117 0 0 0-.124-.06A19.736 19.736 0 0 0 3.683 4.369a.105.105 0 0 0-.047.043C.533 9.043-.32 13.579.099 18.057a.12.12 0 0 0 .045.083c1.934 1.426 3.81 2.288 5.671 2.857a.116.116 0 0 0 .127-.043c.438-.602.827-1.24 1.165-1.908a.112.112 0 0 0-.062-.158c-.619-.234-1.205-.52-1.77-.853a.117.117 0 0 1-.012-.194c.119-.09.238-.183.353-.277a.112.112 0 0 1 .114-.013c3.747 1.71 7.789 1.71 11.533 0a.112.112 0 0 1 .115.012c.115.094.234.188.353.278a.117.117 0 0 1-.012.194c-.565.333-1.151.619-1.77.853a.112.112 0 0 0-.062.158c.34.668.728 1.306 1.165 1.908a.115.115 0 0 0 .127.043c1.861-.569 3.737-1.431 5.671-2.857a.12.12 0 0 0 .045-.083c.5-5.177-.838-9.673-3.636-13.645a.105.105 0 0 0-.047-.043zM8.02 15.331c-1.183 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.175 1.095 2.156 2.418 0 1.334-.955 2.419-2.156 2.419zm7.96 0c-1.183 0-2.156-1.085-2.156-2.419 0-1.333.955-2.418 2.156-2.418 1.21 0 2.175 1.095 2.156 2.418 0 1.334-.946 2.419-2.156 2.419z"})}),"Discord"]}),m("a",{href:"https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension",target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-1 text-violet-700 hover:underline",title:"VS Code Marketplace",children:[m("svg",{width:"16",height:"16",fill:"currentColor",viewBox:"0 0 24 24",children:m("path",{d:"M21.805 2.29a2.25 2.25 0 0 0-2.45-.49l-7.5 3.25a2.25 2.25 0 0 0-1.31 2.06v1.13l-5.13 2.22a2.25 2.25 0 0 0-1.31 2.06v3.5a2.25 2.25 0 0 0 1.31 2.06l5.13 2.22v1.13a2.25 2.25 0 0 0 1.31 2.06l7.5 3.25a2.25 2.25 0 0 0 2.45-.49A2.25 2.25 0 0 0 23 20.25V3.75a2.25 2.25 0 0 0-1.195-1.46zM12 20.25v-16.5l7.5 3.25v10l-7.5 3.25z"})}),"VS Code Marketplace"]})]}),m("div",{className:"mt-2",children:[m("span",{className:"font-semibold",children:"Contact:"})," ",m("a",{href:"mailto:<EMAIL>",className:"text-blue-700 hover:underline",children:"<EMAIL>"})]}),m("div",{className:"mt-2 text-zinc-500",children:m("span",{children:["Licensed under AGPL v3."," ",m("a",{href:"https://github.com/stagewise-io/stagewise/blob/main/LICENSE",target:"_blank",rel:"noopener noreferrer",className:"hover:underline",children:"View license"})]})})]});function _w({discover:t,discoveryError:e}){return m("div",{className:"rounded-lg border border-orange-200 bg-orange-50/90 p-4 shadow-lg backdrop-blur",children:[m("div",{className:"mb-3 flex items-center gap-3",children:[m(gu,{className:"size-5 text-orange-600"}),m("h3",{className:"font-semibold text-orange-800",children:"Not Connected"})]}),m("div",{className:"space-y-3 text-orange-700 text-sm",children:[m("p",{children:"The stagewise toolbar isn't connected to any IDE window."}),e&&m("div",{className:"rounded border border-red-200 bg-red-100 p-2 text-red-700",children:[m("strong",{children:"Error:"})," ",e]}),m("div",{className:"space-y-2",children:[m("p",{className:"font-medium",children:"To connect:"}),m("ol",{className:"list-inside list-decimal space-y-1 pl-2 text-xs",children:[m("li",{children:"Open your IDE (Cursor, Windsurf, etc.)"}),m("li",{children:"Install the stagewise extension"}),m("li",{children:"Make sure the extension is active"}),m("li",{children:"Click refresh below"})]})]}),m("button",{type:"button",onClick:t,className:"flex w-full items-center justify-center gap-2 rounded-md bg-orange-600 px-3 py-2 font-medium text-sm text-white transition-colors hover:bg-orange-700",children:[m($r,{className:"size-4"}),"Retry Connection"]}),m("div",{className:"border-orange-200 border-t pt-2",children:m("a",{href:"https://marketplace.visualstudio.com/items?itemName=stagewise.stagewise-vscode-extension",target:"_blank",rel:"noopener noreferrer",className:"text-orange-600 text-xs hover:text-orange-800 hover:underline",children:"Get VS Code Extension →"})})]})]})}function xw(){return m("div",{className:"rounded-lg border border-blue-200 bg-blue-50/90 p-4 shadow-lg backdrop-blur",children:[m("div",{className:"mb-3 flex items-center gap-3",children:[m($r,{className:"size-5 animate-spin text-blue-600"}),m("h3",{className:"font-semibold text-blue-800",children:"Connecting..."})]}),m("div",{className:"text-blue-700 text-sm",children:m("p",{children:["Looking for active agent instances...",m("br",{}),m("span",{className:"text-blue-500 text-xs",children:"VS Code, Cursor, Windsurf ..."})]})})]})}function kw(){const{windows:t,isDiscovering:e,discoveryError:r,discover:n,selectedSession:i,selectSession:o,appName:a}=Bt(),s=c=>{const u=c.target,d=u.value===""?void 0:u.value;o(d)},l=()=>{n()};return m("div",{className:"rounded-lg border border-blue-200 bg-blue-50/90 p-4 shadow-lg backdrop-blur",children:[m("div",{className:"mb-3",children:m("h3",{className:"font-semibold text-blue-800",children:"Select IDE Window"})}),m("div",{className:"space-y-3",children:[m("div",{children:[m("label",{htmlFor:"window-selection-select",className:"mb-2 block font-medium text-blue-700 text-sm",children:["IDE Window ",a&&`(${a})`]}),m("div",{className:"flex w-full items-center space-x-2",children:[m("select",{id:"window-selection-select",value:(i==null?void 0:i.sessionId)||"",onChange:s,className:"h-8 min-w-0 flex-1 rounded-lg border border-blue-300 bg-white/80 px-3 text-sm backdrop-saturate-150 focus:border-blue-500 focus:outline-none",disabled:e,children:[m("option",{value:"",disabled:!0,children:t.length>0?"Select an IDE window...":"No windows available"}),t.map(c=>m("option",{value:c.sessionId,children:[c.displayName," - Port ",c.port]},c.sessionId))]}),m("button",{type:"button",onClick:l,disabled:e,className:"flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-lg bg-blue-100/80 backdrop-saturate-150 transition-colors hover:bg-blue-200/80 disabled:opacity-50",title:"Refresh window list",children:m($r,{className:`size-4 text-blue-600 ${e?"animate-spin":""}`})})]}),r&&m("p",{className:"mt-1 text-red-600 text-sm",children:["Error discovering windows: ",r]}),!e&&t.length===0&&!r&&m("p",{className:"mt-1 text-blue-600 text-sm",children:"No IDE windows found. Make sure the Stagewise extension is installed and running."})]}),i&&m("div",{className:"rounded-lg bg-blue-100/80 p-3",children:[m("p",{className:"text-blue-800 text-sm",children:[m("strong",{children:"Selected:"})," ",i.displayName]}),m("p",{className:"mt-1 text-blue-600 text-xs",children:["Session ID: ",i.sessionId.substring(0,8),"..."]})]}),!i&&m("div",{className:"rounded-lg border border-blue-200 bg-white/90 p-3",children:m("p",{className:"text-blue-800 text-sm",children:[m("strong",{children:"No window selected:"})," Please select an IDE window above to connect."]})})]})]})}function Sw({handleButtonClick:t,pluginBox:e,setPluginBox:r,openPanel:n,setOpenPanel:i,chatState:o}){const a=Vi().plugins.filter(s=>s.onActionClick);return m(Ue,{children:[m(vw,{onOpenPanel:()=>i(n==="settings"?null:"settings"),isActive:n==="settings"}),a.length>0&&m(xn,{children:a.map(s=>m(Pr,{onClick:t(()=>{(e==null?void 0:e.pluginName)!==s.pluginName?s.onActionClick()&&r({component:s.onActionClick(),pluginName:s.pluginName}):r(null)}),active:(e==null?void 0:e.pluginName)===s.pluginName,children:s.iconSvg?m("span",{className:"size-4 stroke-zinc-950 text-zinc-950 *:size-full",children:s.iconSvg}):m(Yb,{className:"size-4"})},s.pluginName))}),m(xn,{children:m(Pr,{onClick:t(()=>o.isPromptCreationActive?o.stopPromptCreation():o.startPromptCreation()),active:o.isPromptCreationActive,children:m(qb,{className:"size-4 stroke-zinc-950"})})})]})}function Cw(){const{discover:t,isDiscovering:e}=Bt();return m(xn,{children:m(Pr,{onClick:e?void 0:()=>t(),className:Ae(e?"text-blue-700":"text-orange-700 hover:bg-orange-200"),children:m($r,{className:Ae("size-4",e&&"animate-spin")})})})}function Ew(){const t=Le(Ha),e=t==null?void 0:t.borderLocation,r=!!e&&e.right-e.left>0&&e.bottom-e.top>0,n=fw({startThreshold:10,initialSnapArea:"bottomRight"}),{windows:i,isDiscovering:o,discoveryError:a,discover:s,shouldPromptWindowSelection:l}=Bt(),c=i.length>0,[u,d]=me(null),[p,h]=me(null),b=An(),{minimized:f,minimize:w,expand:y}=$a();xe(()=>{f&&(d(null),h(null))},[f]);const v=k=>O=>{if(n.wasDragged){O.preventDefault(),O.stopPropagation();return}k()};if(!r)return null;const _=o,S=!c&&!o,E=c,I=l&&E,H=_?{border:"border-blue-300",bg:"bg-blue-100/80",divideBorder:"divide-blue-200",buttonBg:"from-blue-600 to-sky-600",buttonColor:"text-blue-700"}:S?{border:"border-orange-300",bg:"bg-orange-100/80",divideBorder:"divide-orange-200",buttonBg:"from-orange-600 to-red-600",buttonColor:"text-orange-700"}:{border:"border-border/30",bg:"bg-zinc-50/80",divideBorder:"divide-border/20",buttonBg:"from-sky-700 to-fuchsia-700",buttonColor:"stroke-zinc-950"},R=()=>_?m($r,{className:"size-4 animate-spin text-white"}):S?m(gu,{className:"size-4 text-white"}):m(gw,{className:"size-4.5",color:"white"});return m("div",{ref:n.draggableRef,className:"absolute p-0.5",children:[m("div",{className:Ae("absolute flex h-[calc(100vh-32px)] w-96 max-w-[40vw] items-stretch justify-end transition-all duration-300 ease-out",n.position.isTopHalf?"top-0 flex-col-reverse":"bottom-0 flex-col",n.position.isLeftHalf?"left-[100%]":"right-[100%]"),children:[m("div",{className:Ae("flex min-h-0 flex-1 origin-bottom-right flex-col items-stretch px-2 transition-all duration-300 ease-out",(u||p==="settings"||!E||I)&&!f?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none h-0 scale-50 opacity-0 blur-md",n.position.isTopHalf?"justify-start":"justify-end",n.position.isTopHalf?n.position.isLeftHalf?"origin-top-left":"origin-top-right":n.position.isLeftHalf?"origin-bottom-left":"origin-bottom-right"),children:[_&&m(xw,{}),S&&m(_w,{discover:s,discoveryError:a}),I&&m(kw,{}),E&&p==="settings"&&!I&&m(bw,{onClose:()=>h(null)}),E&&!I&&(u==null?void 0:u.component)]}),E&&m("div",{className:Ae("z-20 w-full px-2 transition-all duration-300 ease-out",b.isPromptCreationActive&&!f?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none h-0 scale-50 opacity-0 blur-md",n.position.isTopHalf?"mb-2":"mt-2",n.position.isTopHalf?n.position.isLeftHalf?"origin-top-left":"origin-top-right":n.position.isLeftHalf?"origin-bottom-left":"origin-bottom-right"),children:m(pw,{})})]}),m("div",{ref:n.handleRef,className:Ae("pointer-events-auto z-50 rounded-full border px-0.5 shadow-md backdrop-blur transition-all duration-300 ease-out",H.border,H.bg,n.position.isTopHalf?"flex-col-reverse divide-y-reverse":"flex-col",f?"h-9.5 w-9.5":"h-[calc-size(auto,size)] h-auto w-auto"),children:[m(La,{onClick:()=>y(),className:Ae("absolute right-0 left-0 z-50 flex size-9 origin-center cursor-pointer items-center justify-center rounded-full bg-gradient-to-tr transition-all duration-300 ease-out",H.buttonBg,f?"pointer-events-auto scale-100 opacity-100 blur-none":"pointer-events-none scale-25 opacity-0 blur-md",n.position.isTopHalf?"top-0":"bottom-0"),children:R()}),m("div",{className:Ae("flex h-[calc-size(auto)] scale-100 items-center justify-center divide-y transition-all duration-300 ease-out",H.divideBorder,n.position.isTopHalf?"origin-top flex-col-reverse divide-y-reverse":"origin-bottom flex-col",f&&"pointer-events-none h-0 scale-50 opacity-0 blur-md"),children:[E?m(Sw,{handleButtonClick:v,pluginBox:u,setPluginBox:d,openPanel:p,setOpenPanel:h,chatState:b}):m(Cw,{}),m(xn,{children:m(Pr,{onClick:v(()=>w()),className:Ae("h-5",H.buttonColor,n.position.isTopHalf?"rounded-t-3xl rounded-b-lg":"rounded-t-lg rounded-b-3xl"),children:n.position.isTopHalf?m(Wb,{className:"size-4"}):m(Hb,{className:"size-4"})})})]})]})]})}function Ow(){const t=ce(null);return m("div",{className:"absolute size-full",children:m("div",{className:"absolute inset-4",ref:t,children:m(hw,{containerRef:t,snapAreas:{topLeft:!0,topRight:!0,bottomLeft:!0,bottomRight:!0,topCenter:!0,bottomCenter:!0},children:m(Ew,{})})})})}function Nw(t){const e=ce(null),r=V(o=>{if(o.target.closest(".companion"))return;const a=E0(o.clientX,o.clientY);t.ignoreList.includes(a)||e.current!==a&&(e.current=a,t.onElementHovered(a))},[t]),n=V(()=>{e.current=null,t.onElementUnhovered()},[t]),i=V(o=>{o.preventDefault(),o.stopPropagation(),e.current&&(t.ignoreList.includes(e.current)||t.onElementSelected(e.current))},[t]);return m("div",{className:"pointer-events-auto fixed inset-0 h-screen w-screen cursor-copy",onMouseMove:r,onMouseLeave:n,onClick:i,role:"button",tabIndex:0})}function bu(){const[t,e]=me({width:window.innerWidth,height:window.innerHeight}),r=V(()=>e({width:window.innerWidth,height:window.innerHeight}),[]);return Ei("resize",r),t}function wu(t,e){const r=ce(void 0),n=Ne(()=>1e3/e,[e]),i=ce(0),o=V(a=>{a-i.current>=n&&(t(),i.current=a),r.current=requestAnimationFrame(o)},[t,n]);xe(()=>(r.current=requestAnimationFrame(o),()=>{r.current&&(cancelAnimationFrame(r.current),r.current=void 0)}),[e,o])}function Pw({refElement:t,...e}){const r=ce(null),n=bu(),{plugins:i}=Vi(),o=Ne(()=>t?i.filter(s=>s.onContextElementSelect).map(s=>{var l;return{pluginName:s.pluginName,context:(l=s.onContextElementSelect)==null?void 0:l.call(s,t)}}):[],[t]),a=V(()=>{if(r.current)if(t){const s=t.getBoundingClientRect();r.current.style.top=`${s.top-2}px`,r.current.style.left=`${s.left-2}px`,r.current.style.width=`${s.width+4}px`,r.current.style.height=`${s.height+4}px`,r.current.style.display=void 0}else r.current.style.height="0px",r.current.style.width="0px",r.current.style.top=`${n.height/2}px`,r.current.style.left=`${n.width/2}px`,r.current.style.display="none"},[t,n.height,n.width]);return wu(a,30),m("div",{...e,className:"fixed flex items-center justify-center rounded-lg border-2 border-blue-600/80 bg-blue-600/20 text-white transition-all duration-100",style:{zIndex:1e3},ref:r,children:[m("div",{className:"absolute top-0.5 left-0.5 flex w-full flex-row items-start justify-start gap-1",children:[m("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:m("span",{className:"truncate",children:t.tagName.toLowerCase()})}),o.filter(s=>s.context.annotation).map(s=>{var l;return m("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:[m("span",{className:"size-3 shrink-0 stroke-white text-white *:size-full",children:(l=i.find(c=>c.pluginName===s.pluginName))==null?void 0:l.iconSvg}),m("span",{className:"truncate",children:s.context.annotation})]})})]}),m(Gb,{className:"size-6 drop-shadow-black drop-shadow-md"})]})}function Tw({refElement:t,...e}){const r=ce(null),n=bu(),i=V(()=>{if(r.current)if(t){const l=t.getBoundingClientRect();r.current.style.top=`${l.top}px`,r.current.style.left=`${l.left}px`,r.current.style.width=`${l.width}px`,r.current.style.height=`${l.height}px`,r.current.style.display=void 0}else r.current.style.height="0px",r.current.style.width="0px",r.current.style.top=`${n.height/2}px`,r.current.style.left=`${n.width/2}px`,r.current.style.display="none"},[t,n.height,n.width]);wu(i,30);const o=An(),a=V(()=>{o.removeChatDomContext(o.currentChatId,t)},[o,t]),{plugins:s}=Vi();return m("div",{...e,className:"pointer-events-auto fixed flex cursor-pointer items-center justify-center rounded-lg border-2 border-green-600/80 bg-green-600/5 text-transparent transition-all duration-0 hover:border-red-600/80 hover:bg-red-600/20 hover:text-white",ref:r,onClick:a,role:"button",tabIndex:0,children:[m("div",{className:"absolute top-0.5 left-0.5 flex w-full flex-row items-start justify-start gap-1",children:[m("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:m("span",{className:"truncate",children:t.tagName.toLowerCase()})}),e.pluginContext.filter(l=>l.context.annotation).map(l=>{var c;return m("div",{className:"flex flex-row items-center justify-center gap-0.5 overflow-hidden rounded-md bg-zinc-700/80 px-1 py-0 font-medium text-white text-xs",children:[m("span",{className:"size-3 shrink-0 stroke-white text-white *:size-full",children:(c=s.find(u=>u.pluginName===l.pluginName))==null?void 0:c.iconSvg}),m("span",{className:"truncate",children:l.context.annotation})]})})]}),m(rw,{className:"size-6 drop-shadow-black drop-shadow-md"})]})}function Mw(){const{chats:t,currentChatId:e,addChatDomContext:r,isPromptCreationActive:n,promptState:i}=An(),o=Ne(()=>t.find(d=>d.id===e),[e,t]),a=n&&i!=="loading",s=Ne(()=>(o==null?void 0:o.domContextElements)||[],[o]),[l,c]=me(null),u=V(d=>{r(e,d)},[r,e]);return a?m(Ue,{children:[l&&m(Pw,{refElement:l}),m(Nw,{ignoreList:s.map(d=>d.element),onElementHovered:c,onElementSelected:u,onElementUnhovered:()=>c(null)}),s.map(d=>m(Tw,{refElement:d.element,pluginContext:d.pluginContext}))]}):null}function Aw(){return m("div",{className:Ae("fixed inset-0 h-screen w-screen"),children:[m(Mw,{}),m(Ow,{})]})}function Rw(){const t=ce(!1);return xe(()=>{const e=HTMLElement.prototype.focus;return HTMLElement.prototype.focus=function(...r){const n=this.getRootNode();!(n instanceof ShadowRoot&&n.host instanceof HTMLElement&&n.host.nodeName==="STAGEWISE-COMPANION-ANCHOR")&&t.current||e.apply(this,r)},()=>{HTMLElement.prototype.focus=e}},[]),Ei("focusin",e=>{e.target.localName===_n&&(t.current=!0)},{capture:!0}),Ei("focusout",e=>{e.target.localName===_n&&(t.current=!1)},{capture:!0}),null}function Iw({children:t}){return t}function $w(){const{isMainAppBlocked:t}=$a();return m("div",{className:Ae("fixed inset-0 h-screen w-screen",t?"pointer-events-auto":"pointer-events-none"),role:"button",tabIndex:0})}function zw(t){return m($0,{children:[m(Rw,{}),m($w,{}),m(j0,{config:t,children:[m(L0,{}),m(Iw,{children:m(Aw,{})})]})]})}function jw(t){if(!document.body)throw new Error("stagewise companion cannot find document.body");if(document.body.querySelector(_n))throw console.warn("A stagewise companion anchor already exists. Aborting this instance."),new Error("A stagewise companion anchor already exists.");const e=document.createElement(_n);e.style.position="fixed",e.style.top="0px",e.style.left="0px",e.style.right="0px",e.style.bottom="0px",e.style.pointerEvents="none",e.style.zIndex="2147483647";const r=o=>{o.stopPropagation()};e.onclick=r,e.onmousedown=r,e.onmouseup=r,e.onmousemove=r,e.ondblclick=r,e.oncontextmenu=r,e.onwheel=r,e.onfocus=r,e.onblur=r,document.body.appendChild(e);const n=document.createElement("link");n.rel="stylesheet",n.href="https://rsms.me/inter/inter.css",document.head.appendChild(n);const i=document.createElement("style");i.append(document.createTextNode(P0)),document.head.appendChild(i),Sr(Ge(zw,t),e)}function Lw({config:t,enabled:e=!1}){const r=q.useRef(!1);return q.useEffect(()=>{r.current||!e||(r.current=!0,jw(t))},[t,e]),null}function Dw(){return m("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"-11.5 -10.23174 23 20.46348",children:[m("title",{children:"React Logo"}),m("circle",{cx:"0",cy:"0",r:"2.05",fill:"currentColor"}),m("g",{stroke:"currentColor","stroke-width":"1",fill:"none",children:[m("ellipse",{rx:"11",ry:"4.2"}),m("ellipse",{rx:"11",ry:"4.2",transform:"rotate(60)"}),m("ellipse",{rx:"11",ry:"4.2",transform:"rotate(120)"})]})]})}const Fw=0,Hw=1,Bw=5;function yu(t){var e,r;if(!t)return null;const n=[],i=3,o=Object.keys(t).find(s=>s.startsWith("__reactFiber$")||s.startsWith("__reactInternalInstance$"));if(!o)return null;let a=t[o];if(!a)return null;for(;a&&n.length<i;){let s=null;if(a.tag===Hw||a.tag===Fw){const l=a.type;l&&(s={name:l.displayName||l.name||((e=a._debugOwner)==null?void 0:e.name)||"AnonymousComponent",type:"regular"})}else a.tag===Bw&&a._debugOwner&&((r=a._debugOwner.env)!=null&&r.toLowerCase().includes("server"))&&(s={name:a._debugOwner.name,type:"rsc"});s&&(n.some(c=>c.name===s.name&&c.type===s.type)||n.push(s)),a=a.return}return n.length>0?n:null}function wl(t){const e=yu(t);return e!=null&&e[0]?{annotation:`${e[0].name}${e[0].type==="rsc"?" (RSC)":""}`}:{annotation:null}}function Ww(t){const e=t.map(r=>yu(r));return e.some(r=>r.length>0)?`This is additional information on the elements that the user selected. Use this information to find the correct element in the codebase.

  ${e.map((n,i)=>`
<element index="${i+1}">
  ${n.length===0?"No React component as parent detected":`React component tree (from closest to farthest, 3 closest elements): ${n.map(o=>`{name: ${o.name}, type: ${o.type}}`).join(" child of ")}`}
</element>
    `)}
  `:null}const Vw={displayName:"React",description:"This toolbar adds additional information and metadata for apps using React as a UI framework",iconSvg:m(Dw,{}),pluginName:"react",onContextElementHover:wl,onContextElementSelect:wl,onPromptSend:t=>{const e=Ww(t.contextElements);return e?{contextSnippets:[{promptContextName:"elements-react-component-info",content:e}]}:{contextSnippets:[]}}};function qw(){return g.jsxs("div",{className:"App",children:[g.jsx(Lw,{config:{plugins:[Vw]}}),g.jsx("a",{href:"#main-content",className:"skip-link",children:"Skip to main content"}),g.jsx(rp,{}),g.jsxs("main",{id:"main-content",role:"main","aria-label":"Main content",children:[g.jsx(cp,{}),g.jsx(dp,{}),g.jsx(af,{}),g.jsx(sf,{}),g.jsx(Am,{}),g.jsx(Rm,{}),g.jsx(Im,{}),g.jsx($m,{})]}),g.jsx(zm,{})]})}yo.createRoot(document.getElementById("root")).render(g.jsx(ku.StrictMode,{children:g.jsx(qw,{})}));
